import { Card, CardContent } from "@/components/ui/card";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { mapAccountType } from "@/constants/common.constant";

interface BankInformationProps {
  merchantData: GetMerchantStatusResponse;
}
function BankInformation({ merchantData }: BankInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-14">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            銀行番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxBankNo ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            支店番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxBranchNo ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            銀行名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxBankName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            支店名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxBranchName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            銀行名（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxBankPhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            支店名（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxBranchPhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            口座種別<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxAccountType !== null
                ? mapAccountType.get(merchantData?.agxAccountType)
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            口座番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxAccountNo ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            口座名義<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxAccountHolder ?? ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default BankInformation;
