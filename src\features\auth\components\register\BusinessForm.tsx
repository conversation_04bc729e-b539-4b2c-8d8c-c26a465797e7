
import React, { useState } from 'react';
import { ENTITY_TYPES } from '../../constant';
import { Button } from '@/components/ui/button';
import { IMerchantCoreType } from '../../types';

interface Props {
    data: IMerchantCoreType;
    onNext: () => void;
    onPrev: () => void;
    onUpdate: (data: Partial<IMerchantCoreType>) => void;
}

const BusinessForm = ({ data, onNext, onPrev, onUpdate }: Props) => {
    const [selectedType, setSelectedType] = useState<number | null>(data.agxBusinessForm ? Number(data.agxBusinessForm) : null);

    

    const handleSelection = (type: number) => {
        setSelectedType(type);
        onUpdate({ agxBusinessForm: type });
    };

    const handleNext = () => {
        if (selectedType) {
            onNext();
        }
    };

    return (
        <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-full md:w-[90%] lg:w-[90%] xl:w-[30%] max-w-full flex-col items-center text-[rgba(112,112,112,1)] mt-[33px] py-10 px-8 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
            <h2 className="text-[28px] text-[rgba(112,112,112,1)] font-normal mb-10">法人ですか、個人ですか</h2>

            <div className="flex justify-center gap-8 w-full mb-10">
                {ENTITY_TYPES.map((type) => (
                    <Button 
                        key={type.id}
                        onClick={() => handleSelection(type.id)}
                        variant={selectedType === type.id ? "default" : "outline"}
                        size="lg"
                        className={`h-[50px] rounded-[8px] text-[16px] font-normal mb-2 transition-colors
                            ${selectedType === type.id
                                ? "bg-[#19A492] text-white border-[#19A492] hover:bg-[#15b19d] hover:border-[#15b19d]"
                                : "bg-white text-[rgba(112,112,112,1)] border-[rgba(112,112,112,0.3)] hover:bg-[#89a9a7] hover:text-white hover:border-[#89a9a7]"
                            }`}
                    >
                        {type.label}
                    </Button>
                ))}
            </div>

            <div className="flex justify-center gap-4 w-full">
                <Button
                    onClick={onPrev}
                    className="text-[20px] w-[20%] max-md:w-full h-[45px] bg-gray-400 text-white rounded-[8px] hover:bg-gray-500 transition-colors"
                >
                    戻る
                </Button>

                <Button
                    onClick={handleNext}
                    disabled={!selectedType}
                    className="text-[20px] w-[20%] max-md:w-full h-[45px] bg-[#19A492] text-white rounded-[8px] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#15b19d] transition-colors"
                >
                    次へ
                </Button>
            </div>
        </section>
    );
};

export default BusinessForm;
