
import React, { useState, useRef } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { SITEKEY } from '@/constants/common.constant';
import { Button } from '@/components/ui/button';
import logo from '@/assets/images/グループ 3.svg';
import { useSendMail } from '@/features/auth/hooks/useSendMail';

interface Props {
    data: string | null;
    onNext: () => void;
    onPrev: () => void;
    onUpdate: (data: string) => void;
}


const SendEmail = ({ data, onNext, onPrev, onUpdate }: Props) => {
    const [email, setEmail] = useState(data || '');
    const [captchaVerified, setCaptchaVerified] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const recaptchaRef = useRef<ReCAPTCHA>(null);
    const navigate = useNavigate();
    const {sendMailAsync} = useSendMail();

    const handleCaptchaChange = (token: string | null) => {
        setCaptchaVerified(!!token);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!email || !captchaVerified || isSubmitting) {
            return;
        }

        setIsSubmitting(true);

        // send mail using the service
        try{
            const response = await sendMailAsync({ 'email': email, 'type': searchParams.get('type') })
            if (response.status === 200) {
                onUpdate( email );
                onNext();
            }
        }finally{
            setIsSubmitting(false);
    
            if (recaptchaRef.current) {
                recaptchaRef.current.reset();
                setCaptchaVerified(false);
            }
        }
    };

    return (
        <>
            <section className="self-center mt-1.5 w-full max-w-4xl max-md:max-w-full">
                <div className="flex justify-center pb-2 md:pb-4">
                    <img
                        src={logo}
                        alt="ChoQi Logo"
                        className="h-16 md:h-20 lg:h-full object-contain"
                    />
                </div>
                <div className="flex justify-center">
                    <span className="text-[28px] text-[rgba(112,112,112,1)] font-normal">チョキペイは医療機関専用のキャッシュレスサービスです</span>
                </div>
            </section>
            <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-[500px] max-w-full flex-col items-stretch text-[28px] text-[rgba(112,112,112,1)] font-normal whitespace-nowrap mt-[33px] pt-[58px] pb-[92px] px-12 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
                <form onSubmit={handleSubmit} className="flex flex-col">
                    <div className="mb-6">
                        <div className="relative">
                            <input
                                id="email"
                                type="email"
                                placeholder="メールアドレス"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className="bg-white border w-full h-[66px] px-[19px] rounded-[13px] border-[rgba(112,112,112,1)] border-solid text-[28px] text-[rgba(112,112,112,1)] placeholder:text-[rgba(112,112,112,1)] focus:outline-none focus:ring-2 focus:ring-[rgba(25,164,146,1)] focus:border-transparent max-md:pr-5"
                                required
                            />
                        </div>
                    </div>

                    <div className="mb-6 flex justify-center w-full">
                        <ReCAPTCHA
                            ref={recaptchaRef}
                            sitekey={SITEKEY}
                            onChange={handleCaptchaChange}
                        />
                    </div>

                    <div className="flex justify-center mt-6">
                        <Button
                            type="submit"
                            onClick={handleSubmit}
                            disabled={!email || !captchaVerified || isSubmitting}
                            className="bg-[#19A492] text-white font-medium px-8 py-2 rounded-md w-[120px] h-[48px] hover:bg-[#15b19d] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-[18px]"
                            aria-disabled={!email || !captchaVerified || isSubmitting}
                        >
                            {isSubmitting ? '送信中...' : '登録'}
                        </Button>
                    </div>
                </form>
            </section>
        </>
    );
};

export default SendEmail;
