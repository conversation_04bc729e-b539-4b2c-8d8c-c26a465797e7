import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';

interface SummaryHeaderProps {
  title: string;
  currentPath: string;
  type: string;
  storeName: string;
  agxMerchantNo: string;
}

const SummaryHeader: React.FC<SummaryHeaderProps> = ({
  title,
  currentPath,
  type,
  storeName,
  agxMerchantNo
}) => {
  const navigate = useNavigate();

  const handleChangeTypeChart = (value: string) => {
    navigate(value);
  };

  return (
    <>
      {/* Page Header */}
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col xl:flex-row xl:items-center gap-3 xl:gap-4">
          <h1 className="text-lg sm:text-2xl lg:text-3xl font-bold text-[#6F6F6E]">
            {title}
          </h1>
          <Select value={currentPath} onValueChange={handleChangeTypeChart}>
            <SelectTrigger className="w-full sm:w-80 text-lg text-[#6F6F6E]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem className="text-[#6F6F6E] text-lg" value={`/store/summary/${type}`}>売上金額・件数推移（振込日別）</SelectItem>
              <SelectItem className="text-[#6F6F6E] text-lg" value={`/store/deposit/${type}`}>振込一覧</SelectItem>
              <SelectItem className="text-[#6F6F6E] text-lg" value={`/store/summary-monthly/${type}`}>売上金額・件数推移（振込月別）</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Store Information */}
      <Card className="mb-4 sm:mb-6 border-0 shadow-none text-[#6F6F6E]">
        <CardContent className="p-0 pt-4 sm:pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div className="text-sm sm:text-lg">
              <span>店舗名: </span>
              <span className="break-words">{storeName}</span>
            </div>
            <div className="text-sm sm:text-lg">
              <span>チョキペイ加盟店番号: </span>
              <span className="break-words">{agxMerchantNo}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default SummaryHeader;
