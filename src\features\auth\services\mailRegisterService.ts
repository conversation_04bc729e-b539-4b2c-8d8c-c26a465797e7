import { API_ENDPOINTS } from '@/config/api-endpoints';
import apiService from '../../../services/api';
import { MailRegisterParams, MailRegisterResponse } from '../types';
import { toast } from '@/components/ui/use-toast';

class MailRegisterService {
    async sendMail(requestBody: MailRegisterParams): Promise<MailRegisterResponse> {
        const response = await apiService.post<MailRegisterResponse>(
            API_ENDPOINTS.MAIL.REGISTER,
            requestBody
        );
        return response;
    }
}

export const mailRegisterService = new MailRegisterService();
