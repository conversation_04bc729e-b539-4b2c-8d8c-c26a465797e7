import apiService from '@/services/api';
import { setAuthToken, removeAuthToken } from '@/config/axios';
import { LoginParams, User, AuthResponse, SignupParams, CommonResponse } from '@/features/auth/types';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { contactStore } from '@/features/auth/slices/contactStore';
import { API_ENDPOINTS } from "@/config/api-endpoints"
import { TypeStore } from '@/types/globalType';

class AuthService {
  /**
   * Login
   * @param credentials - Login credentials
   * @returns Auth response
   */
  async login(loginRequest: LoginParams): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, loginRequest);

    // Save token to localStorage
    if (response.token) {
      setAuthToken(response.token);
    }

    // Save user info to authStore
    const userInfo: User = {
      id: response.id,
      username: response.username,
      email: response.email,
      firstName: response.firstName,
      lastName: response.lastName,
      roles: response.roles,
      statusAccount: response.statusAccount,
      memberType: response.memberType,
      agxMerchantNo: response.agxMerchantNo,
      agxNewMerchantNo: response.agxNewMerchantNo,
      agxOldMerchantNo: response.agxOldMerchantNo,
      agxStoreName: response.agxStoreName,
      accountName: response.accountName,
      agxStoreEnglishName: response.agxStoreEnglishName,
      lastSuccessfulLogin: response.lastSuccessfulLogin,
      merchantRegistrationDate: response.merchantRegistrationDate,
      requireVerification: response.requireVerification,
      verificationId: response.verificationId,
    };

    useAuthStore.getState().setUser(userInfo);

    let typeStore : number | null = null;

    if(response?.memberType && ((response?.agxOldMerchantNo && !response?.agxNewMerchantNo) ||(!response?.agxOldMerchantNo && !response?.agxNewMerchantNo) )) {
        typeStore = TypeStore.STORE_CREPICO;
    } else if(!response?.memberType && response?.agxNewMerchantNo) {
        typeStore = TypeStore.STORE_MIGRATE;
    } else if(!response?.memberType && !response?.agxNewMerchantNo) {
        typeStore = TypeStore.STORE_PAYGATE;
    }

    useAuthStore.getState().setTypeStore(typeStore);

    return response;
  }

  /**
   * Sign up new user
   * @param signupRequest - Signup data
   * @returns Auth response
   */
  async signup(signupRequest: SignupParams): Promise<CommonResponse<AuthResponse>> {
    const response = await apiService.post<CommonResponse<AuthResponse>>(API_ENDPOINTS.AUTH.SIGNUP, {
      id: null,
      username: signupRequest.username,
      email: signupRequest.email,
      password: signupRequest.password,
      memberType: signupRequest.memberType === "1" ? true : false,
    });

    // Save token to localStorage
    if (response.data?.token) {
      setAuthToken(response.data?.token);
    }

    // Save user info to Zustand store
    const userInfo: User = {
      id: response.data?.id,
      username: response.data?.username,
      email: response.data?.email,
      firstName: response.data?.firstName,
      lastName: response.data?.lastName,
      roles: response.data?.roles,
      statusAccount: response.data?.statusAccount,
      memberType: response.data?.memberType,
      agxMerchantNo: response.data?.agxMerchantNo,
      agxNewMerchantNo: response.data?.agxNewMerchantNo,
      agxOldMerchantNo: response.data?.agxOldMerchantNo,
      agxStoreName: response.data?.agxStoreName,
      accountName: response.data?.accountName,
      agxStoreEnglishName: response.data?.agxStoreEnglishName,
      lastSuccessfulLogin: response.data?.lastSuccessfulLogin,
    };

    useAuthStore.getState().setUser(userInfo);

    return response;
  }

  /**
   * Logout
   */
  async logout(): Promise<void> {
    removeAuthToken();

    // TODO: Uncomment this when the logout API is ready
    // try {
    //   await apiService.post(API_ENDPOINTS.AUTH.LOGOUT);
    // } catch (error) {
    //   console.error('Logout error:', error);
    // } finally {
    //   // Always remove token, even if there's an error
    //   removeAuthToken();
    // }
  }

  /**
   * Refresh token
   * @returns New access token or null if refresh fails
   */
  async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiService.post<{ accessToken: string }>(API_ENDPOINTS.AUTH.REFRESH_TOKEN, {
        refreshToken
      });

      setAuthToken(response.accessToken);
      return response.accessToken;
    } catch (error) {
      console.error('Refresh token error:', error);
      removeAuthToken();
      return null;
    }
  }
}

export const authService = new AuthService();
export default authService; 