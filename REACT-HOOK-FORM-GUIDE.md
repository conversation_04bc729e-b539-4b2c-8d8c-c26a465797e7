
# 📘 React Hook Form - Hướng dẫn chi tiết

React Hook Form là thư viện giúp quản lý form hi<PERSON><PERSON> quả, nhẹ, dễ tích hợp và ít re-render nhất trong React.

---

## 🔰 Cài đặt

```bash
npm install react-hook-form
```

---

## 🔧 useForm()

Hook chính để khởi tạo form:

```tsx
const methods = useForm({
  defaultValues: { name: '', email: '' },
  mode: 'onSubmit', // onChange | onBlur | onTouched
});
```

### ✅ Các tham số (options):

| Tham số           | Mô tả |
|-------------------|------|
| `defaultValues`   | Giá trị mặc định ban đầu |
| `mode`            | Cách trigger validation: `onChange`, `onBlur`, `onSubmit`, `onTouched` |
| `reValidateMode`  | Khi nào re-validate: `onChange`, `onBlur` |
| `resolver`        | Dùng với <PERSON>/Yup để validate schema |
| `criteriaMode`    | `'firstError'` hoặc `'all'` lỗi |

---

## 📍 Các hàm chính từ `useForm`

| Hàm / object               | Mô tả |
|---------------------------|------|
| `register(name, options)` | Gắn input với form |
| `handleSubmit(fn)`        | Xử lý khi form được submit |
| `watch(name?)`            | Theo dõi giá trị field |
| `setValue(name, value)`   | Đặt giá trị cho field |
| `getValues(name?)`        | Lấy giá trị hiện tại |
| `reset(data?)`            | Reset toàn bộ form hoặc theo field |
| `trigger(name?)`          | Thực thi validation |
| `formState`               | Trạng thái form (`isDirty`, `errors`, `isSubmitting`, ...) |
| `control`                 | Đối tượng dùng cho `<Controller />` |

---

## ✍️ `register()`

```tsx
<input {...register('username', { required: 'Bắt buộc nhập' })} />
```

### ✅ Option cho register:

| Option        | Giải thích |
|---------------|------------|
| `required`    | Bắt buộc nhập |
| `minLength`   | Độ dài tối thiểu |
| `maxLength`   | Độ dài tối đa |
| `pattern`     | Regex validate |
| `validate`    | Custom function |

---

## 🧠 formState

Lấy từ `useForm()` hoặc `useFormContext()`:

```tsx
const { errors, isDirty, isSubmitting } = formState;
```

| Field            | Mô tả |
|------------------|------|
| `errors`         | Lỗi của từng trường |
| `isDirty`        | Có thay đổi gì không |
| `isValid`        | Form có hợp lệ không |
| `isSubmitting`   | Đang submit hay không |
| `dirtyFields`    | Những field đã thay đổi |
| `touchedFields`  | Những field đã blur |

---

## 📦 `Controller` (dùng cho component controlled)

```tsx
<Controller
  name="custom"
  control={control}
  render={({ field, fieldState }) => (
    <>
      <MyInput {...field} />
      {fieldState.error && <p>{fieldState.error.message}</p>}
    </>
  )}
/>
```

| Prop             | Giải thích |
|------------------|-----------|
| `name`           | Tên trường |
| `control`        | Lấy từ `useForm()` |
| `render`         | Hàm render ra UI |
| `rules`          | Quy tắc validate |

---

## 🎁 `FormProvider` và `useFormContext`

Cho phép chia form thành nhiều component con:

```tsx
import { FormProvider, useFormContext } from 'react-hook-form';

const methods = useForm();

<FormProvider {...methods}>
  <FormStep />
</FormProvider>
```

```tsx
// Trong component con
const { register, formState } = useFormContext();
```

---

## 🧪 Validation schema: Zod/Yup

```tsx
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const schema = z.object({
  email: z.string().email(),
});

useForm({
  resolver: zodResolver(schema)
});
```

---

## 🌀 useWatch

Dùng để theo dõi giá trị live mà không cần render lại toàn bộ form:

```tsx
const selected = useWatch({ control, name: 'gender' });
```

---

## 🔁 useFieldArray

Quản lý mảng input (dynamic field):

```tsx
const { fields, append, remove } = useFieldArray({ control, name: 'users' });

fields.map((field, index) => (
  <input {...register(`users.${index}.name`)} />
));
```

---

## 📌 Tips

- ✅ Dùng `Controller` với UI lib như shadcn/ui, MUI, Ant Design
- ✅ Tách form lớn → nhiều component + `FormProvider`
- ✅ Sử dụng schema với `zod` hoặc `yup` giúp validate dễ quản lý
- ✅ `trigger()` rất hữu ích để validate field đơn lẻ hoặc theo bước

---

## 📚 Tài nguyên

- Trang chủ: https://react-hook-form.com/
- Resolver Zod/Yup: https://github.com/react-hook-form/resolvers
- shadcn/ui: https://ui.shadcn.com

---

Happy coding 💻!
