// Types for Store Deposit Detail pages

// Payment breakdown interface
export interface PaymentBreakdown {
  agxMerchantPaymentId: string;
  agxPaymentBreakdownId: string;
  agxTransactionType: number;
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFeeRate: number;
  agxTotalFee: number;
  tax: number;
  agxPaymentAmount: number;
  groupCodeName: string;
}

// Merchant payment interface
export interface MerchantPayment {
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFee: number;
  agxSettlementCompanyTax: number;
  agxInHouseTax: number;
  agxPaymentAmount: number;
  agxInvoiceFlg: number;
  agxBankName: string;
  agxBranchName: string;
  agxAcccountType: string;
  agxAccountNo: string;
  agxAccountHolder: string;
}

// Crepico deposit data interface
export interface DepositData {
  paymentBreakdowns: PaymentBreakdown[];
  merchantPayments: MerchantPayment[];
  total: number;
  subTotalConsumptionTax: number;
  subTotalInclTax10: number;
  subTotalNonTax: number;
  subTotalTaxIncl: number;
}

// Response interfaces
export interface DepositDataResponse {
  data: DepositData;
}

export interface DateResponse {
  data: string[];
}

// Chart data interface
export interface ChartData {
  labels: string[];
  percentages: string[];
}

// CSV export data interface
export interface CSVExportData {
  id: string;
  date: string;
  agxTransactionType: string;
  agxNumberOfSales: string;
  agxSalesAmount: string;
  agxTotalFeeRate: string;
  agxTotalFee: string;
  tax: string;
  agxPaymentAmount: string;
}

// Detail item interface
export interface DepositDetailItem {
  agxTransactionDate: string;
  agxTransactionType: number;
  agxSalesAmount: number;
  agxPaymentDate: string;
  agxMemberId: string;
  groupCodeName: string;
}

export interface DepositDetailData {
  data: DepositDetailItem[];
  totalElement: number;
}

// Note: DepositDetailAllItem is identical to DepositDetailItem, so we'll use DepositDetailItem
export interface DepositDetailAllData {
  data: DepositDetailItem[];
  total: number;
  totalSales: number;
}

// Response interfaces for detail pages
export interface DepositDetailAllResponse {
  data: DepositDetailAllData;
}

export interface DepositDetailResponse {
  data: DepositDetailItem[];
  totalElement: number;
}

export interface CSVExportDetailData {
  agxTransactionDate: string;
  agxTransactionType: string;
  agxSalesAmount: string;
  agxPaymentDate: string;
  agxMemberId: string;
}

export interface CSVExportDetailAllData {
  agxTransactionDate: string;
  agxTransactionType: string;
  agxSalesAmount: string;
  agxPaymentDate: string;
  agxMemberId: string;
}
