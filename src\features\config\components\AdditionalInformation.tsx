import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { GetMerchantStatusResponse } from "../types";
import { OtherMeasuresStatus } from "@/constants/common.constant";
import { formatDateYearMonth } from "@/utils/dateUtils";
import { convertDate, convertPhoneAndFax } from "@/utils/helper";

interface AdditionalInformationProps {
  merchantData: GetMerchantStatusResponse;
}



// Shared function to get status label
export function getOtherMeasuresStatusLabel(
  status: number | undefined,
  date?: Date | string | null,
  formatDateFn?: (date: string) => string
): string {
  switch (status) {
    case OtherMeasuresStatus.IMPLEMENTED:
      return "導入済み";
    case OtherMeasuresStatus.NOT_PLANNED:
      return "導入予定なし";
    case OtherMeasuresStatus.PLANNED:
      if (date && formatDateFn) {
        // Accept both Date and string
        const dateStr = typeof date === "string" ? date : date.toISOString().slice(0, 10);
        return `導入予定 ${formatDateFn(dateStr)}`;
      }
      return "導入予定";
    default:
      return "";
  }
}

function AdditionalInformation({ merchantData }: AdditionalInformationProps) {

  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-14">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            ご担当者名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxContactName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            ご担当者名（フリガナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div
              id="contact_phonetic_name"
              className="w-full h-7 rounded-md flex items-center px-3"
            >
              {merchantData?.agxContactPhoneticName ??
                ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            ご担当者メールアドレス<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md flex items-center px-3">
              {merchantData?.agxContactEmail ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            ご担当者電話番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md flex items-center px-3">
              {convertPhoneAndFax(merchantData?.agxContactPhoneNumber)[0] || ""}
            </div>
            <span>-</span>
            <div className="w-auto h-7 rounded-md flex items-center px-3">
              {convertPhoneAndFax(merchantData?.agxContactPhoneNumber)[1] || ""}
            </div>
            <span>-</span>
            <div className="w-auto h-7 rounded-md flex items-center px-3">
              {convertPhoneAndFax(merchantData?.agxContactPhoneNumber)[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            資本金<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 flex items-center text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxCapital ?? ""}
            </div>
          </div>
          <div className="col-span-3 text-[#6F6F6E] ml-2">
            <span className="whitespace-nowrap">万円</span>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            従業員数<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 flex items-center text-[#6F6F6E]">
            <div
              id="number_of_employees"
              className="w-full h-10 rounded-md flex items-center px-3"
            >
              {merchantData?.agxNumberOfEmployees ??
                ""}
            </div>
          </div>
          <div className="col-span-3 text-[#6F6F6E] ml-2">
            <span className="whitespace-nowrap">人</span>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            設立年月<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 flex space-x-2 text-[#6F6F6E]">
            <div className="w-1/2 h-7 rounded-md flex items-center px-3">
              {convertDate(merchantData?.agxFoundingDate)[0] || ""}年
            </div>
            <div className="w-1/2 h-7 rounded-md flex items-center px-3">
              {convertDate(merchantData?.agxFoundingDate)[1] || ""}月
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            月商<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 flex items-center text-[#6F6F6E]">
            <div
              id="monthly_sales"
              className="w-full h-7 rounded-md flex items-center px-3"
            >
              {merchantData?.agxMonthlySales ?? ""}
            </div>
          </div>
          <div className="col-span-3 text-[#6F6F6E] ml-2">
            <span className="whitespace-nowrap">万円</span>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-4 text-[#6F6F6E]">
          特定商取引に関する確認
        </h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 md:text-left text-[20px] text-[#6F6F6E] pl-0">訪問販売</Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3 justify-start">
              {merchantData?.agxDoorToDoorSales ? "有" : "無"}
            </div>
          </div>
          <Label className="col-span-3 md:text-left text-[20px] text-[#6F6F6E]">
            電話勧誘販売
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxTelemarketingSales ? "有" : "無"}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 md:text-left text-[20px] text-[#6F6F6E]">
            連鎖販売取引
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxPyramidScheme ? "有" : "無"}
            </div>
          </div>
          <Label className="col-span-3 md:text-left text-[20px] text-[#6F6F6E]">
            業務提供誘引販売取引
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBusinessOpportunityRelatedSales ? "有" : "無"}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 md:text-left text-[20px] text-[#6F6F6E]">
            特定継続的役務
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxSpecifiedContinuousServices ? "有" : "無"}
            </div>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-4 text-[#6F6F6E]">
          カード情報保護対策
        </h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-5 md:text-left text-[20px] text-[#6F6F6E]">
            【A】 クレジットカード情報の保持状況について
          </Label>
          <div className="col-span-7 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxCardInformationRetentionStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "保持している"
                : merchantData?.agxCardInformationRetentionStatus === OtherMeasuresStatus.NOT_PLANNED
                ? "保持していない"
                : merchantData?.agxCardInformationRetentionStatus === OtherMeasuresStatus.PLANNED
                ? `非保持化の予定あり ${
                  merchantData?.agxNoRetainingCardInfoDate
                    ? formatDateYearMonth(
                        typeof merchantData.agxNoRetainingCardInfoDate === "string"
                          ? merchantData.agxNoRetainingCardInfoDate
                          : merchantData.agxNoRetainingCardInfoDate.toISOString().slice(0, 10)
                      )
                    : ""
                }`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-5 md:text-left text-[20px] text-[#6F6F6E]">
            【B】 PCI DSSの準拠状況について
          </Label>
          <div className="col-span-7 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxPcidssStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "準拠している"
                : merchantData?.agxPcidssStatus === OtherMeasuresStatus.NOT_PLANNED
                ? "準拠予定なし"
                : merchantData?.agxPcidssStatus === OtherMeasuresStatus.PLANNED
                ? `準拠予定あり ${
                      merchantData?.agxPcidssExpectedComplianceDate
                        ? formatDateYearMonth(
                            typeof merchantData.agxPcidssExpectedComplianceDate === "string"
                              ? merchantData.agxPcidssExpectedComplianceDate
                              : merchantData.agxPcidssExpectedComplianceDate.toISOString().slice(0, 10)
                          )
                        : ""
                  }`
                : ""}
            </div>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-4 text-[#6F6F6E]">不正使用対策</h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-12 md:text-left font-bold text-[#6F6F6E]">
            【C】クレジットカードの不正使用対策について
          </Label>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 md:text-left text-[20px] text-[#6F6F6E]">
            ① 本人認証サービス(3Dセキュア)
          </Label>
          <div className="col-span-8 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxThreeDSecureStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "導入済み"
                : merchantData?.agxThreeDSecureStatus === OtherMeasuresStatus.NOT_PLANNED
                ? "導入予定なし"
                : merchantData?.agxThreeDSecureStatus === OtherMeasuresStatus.PLANNED
                ? `導入予定 ${
                  merchantData?.agxThreeDSecureDate
                  ? formatDateYearMonth(
                      typeof merchantData.agxThreeDSecureDate === "string"
                        ? merchantData.agxThreeDSecureDate
                        : merchantData.agxThreeDSecureDate.toISOString().slice(0, 10)
                    )
                  : ""
                  }`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 md:text-left text-[20px] text-[#6F6F6E]">
            ② セキュリティコードチェック
          </Label>
          <div className="col-span-8 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxSecurityCodeCheckStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "導入済み"
                : merchantData?.agxSecurityCodeCheckStatus === OtherMeasuresStatus.NOT_PLANNED
                ? "導入予定なし"
                : merchantData?.agxSecurityCodeCheckStatus === OtherMeasuresStatus.PLANNED
                ? `導入予定 ${
                      merchantData?.agxSecurityCodeCheckDate
                      ? formatDateYearMonth(
                          typeof merchantData.agxSecurityCodeCheckDate === "string"
                            ? merchantData.agxSecurityCodeCheckDate
                            : merchantData.agxSecurityCodeCheckDate.toISOString().slice(0, 10)
                        )
                      : ""
                  }`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 md:text-left text-[20px] text-[#6F6F6E]">
            ③ 不正配送先情報の活用
          </Label>
          <div className="col-span-8 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxIllegalDeliveryDestinationStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "導入済み"
                : merchantData?.agxIllegalDeliveryDestinationStatus ===
                  OtherMeasuresStatus.NOT_PLANNED
                ? "導入予定なし"
                : merchantData?.agxIllegalDeliveryDestinationStatus ===
                  OtherMeasuresStatus.PLANNED
                ? `導入予定 ${
                      merchantData?.agxIllegalDeliveryDestinationDate
                        ? formatDateYearMonth(
                            typeof merchantData.agxIllegalDeliveryDestinationDate === "string"
                              ? merchantData.agxIllegalDeliveryDestinationDate
                              : merchantData.agxIllegalDeliveryDestinationDate.toISOString().slice(0, 10)
                          )
                        : ""
                  }`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 md:text-left text-[20px] text-[#6F6F6E]">
            ④ 属性・行動分析
          </Label>
          <div className="col-span-8 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBehaviorAnalysisStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "導入済み"
                : merchantData?.agxBehaviorAnalysisStatus === OtherMeasuresStatus.NOT_PLANNED
                ? "導入予定なし"
                : merchantData?.agxBehaviorAnalysisStatus === OtherMeasuresStatus.PLANNED
                ? `導入予定 ${
                    merchantData?.agxBehaviorAnalysisDate
                      ? formatDateYearMonth(
                          typeof merchantData.agxBehaviorAnalysisDate === "string"
                            ? merchantData.agxBehaviorAnalysisDate
                            : merchantData.agxBehaviorAnalysisDate.toISOString().slice(0, 10)
                        )
                      : ""
                  }`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 md:text-left text-[20px] text-[#6F6F6E]">
            ⑤ その他の対策
          </Label>
          <div className="col-span-8 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
            {merchantData?.agxOtherMeasuresStatus === OtherMeasuresStatus.IMPLEMENTED
                ? "導入済み"
                : merchantData?.agxOtherMeasuresStatus === OtherMeasuresStatus.NOT_PLANNED
                ? "導入予定なし"
                : merchantData?.agxOtherMeasuresStatus === OtherMeasuresStatus.PLANNED
                ? `導入予定 ${
                    merchantData?.agxOtherMeasuresDate
                      ? formatDateYearMonth(
                          typeof merchantData.agxOtherMeasuresDate === "string"
                            ? merchantData.agxOtherMeasuresDate
                            : merchantData.agxOtherMeasuresDate.toISOString().slice(0, 10)
                        )
                      : ""
                  }`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 md:text-left text-[20px] ml-[30px] text-[#6F6F6E]">対策内容</Label>
          <div className="col-span-8 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxOtherMeasuresDescription ?? ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default AdditionalInformation;
