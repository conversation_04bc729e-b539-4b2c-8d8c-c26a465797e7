import React from 'react';
import { Link } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminDepositData } from '../types';
import { formatNumber } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';

interface DepositTablesProps {
  data: AdminDepositData;
  areaFilters: any;
  transferDate: string;
  switchLayoutDate: string;
}

export const DepositTables: React.FC<DepositTablesProps> = ({
  data,
  areaFilters,
  transferDate,
  switchLayoutDate
}) => {
  const lastPaymentIndex = data.agxMerchantPayments.length - 1;
  const totalPayment = data.agxMerchantPayments[lastPaymentIndex];

  const getDepositDetailLink = () => {
    const area = areaFilters.areaSelected === 'all' ? '' : areaFilters.areaSelected;
    const subArea = areaFilters.subAreaSelected === 'all' ? '' : areaFilters.subAreaSelected;
    const merchant = areaFilters.merchantSelected === 'all' ? '' : areaFilters.merchantSelected;

    return `/admin-store/deposit/detail/${transferDate}?area=${area}&subArea=${subArea}&merchant=${merchant}`;
  };

  return (
    <div className="space-y-2">
      {/* Summary Table */}
      <Card className="border-0 shadow-none text-[#6F6F6E] rounded-none">
        <CardContent className="pt-6 xl:w-[900px] border-b border-[#6F6F6E] rounded-none">
          <div className="mb-4">
            <span className="text-xl font-medium mr-2">サマリー</span>
            <span className="w-px h-6 bg-[#6F6F6E] inline-block mx-2" />
            <Link
              to={getDepositDetailLink()}
              className="text-[#1D9987] hover:text-[#1D9987]/80 text-xl font-medium ml-2"
            >
              詳細データを確認する
            </Link>
          </div>

          <div className="overflow-x-auto sm:overflow-visible xl:mx-10 text-[#6F6F6E] text-lg">
            <div className='flex flex-col'>
              {/* Header row */}
              <div className='flex flex-row gap-4 xl:gap-8'>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上件数
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上金額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  手数料額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  （内消費税）
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  振込金額
                </div>
              </div>
              {/* Data row */}
              <div className='flex flex-row gap-4 xl:gap-8'>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.numberOfSales || 0)}件
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.salesAmount || 0)}円
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.totalFee || 0)}円
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  ({formatNumber(totalPayment?.sumTax || 0)})円
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.paymentAmount || 0)}円
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Merchant Payments Table */}
      <Card className='border-0 shadow-none text-[#6F6F6E] rounded-none border-b border-[#6F6F6E]'>
        <CardHeader>
          <CardTitle className="text-xl font-medium">決済種別ごとのデータ</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto text-lg">
            <div className='flex flex-col'>
              {/* Header row */}
              <div className='flex flex-row gap-4 xl:gap-8'>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                 加盟店番号
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  加盟店名
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上件数
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上金額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  手数料額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  （内消費税）
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  振込額
                </div>
              </div>
              {/* Data row */}
              {data.agxMerchantPayments.slice(0, -1).map((item, index) => (
                <div key={index} className='flex flex-row gap-4 xl:gap-8'>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {item.merchantNo}
                  </div>
                  <div className="py-3 px-0 text-left bg-white flex-[1] xl:px-2">
                    {item.storeName}
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.numberOfSales)}件
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.salesAmount)}円
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.totalFee)}円
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    ({formatNumber(item.sumTax)})円
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.paymentAmount)}円
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Breakdown Table */}
      <Card className='border-0 shadow-none text-[#6F6F6E] rounded-none border-b border-[#6F6F6E]'>
        <CardHeader>
          <CardTitle className="text-xl font-medium">振込内訳</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto text-lg">
            <div className='flex flex-col'>
              {/* Header row */}
              <div className='flex flex-row gap-4 xl:gap-8'>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[2] xl:px-2">
                  取引区分
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  加盟店番号
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上件数
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上金額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  手数料率
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  手数料額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  （内消費税額）
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  振込額
                </div>
              </div>
              {/* Data row */}
              {data.agxPaymentBreakdowns.map((item, index) => (
                <div key={index} className='flex flex-row gap-4 xl:gap-8'>
                  <div className="py-3 px-0 text-left bg-white flex-[2] xl:px-2">
                    <Link
                      to={`/admin-store/deposit/detail/${transferDate}/${item.agxTransactionType}/${item.agxMerchantNo}/${btoa(item.agxPaymentBreakdownId)}`}
                      className="text-[#1D9987] hover:text-[#1D9987]/80"
                    >
                      {mapTransactionType.get(item.agxTransactionType)}{item.groupCodeName}
                    </Link>
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {item.agxMerchantNo}
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.agxNumberOfSales)}件
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.agxSalesAmount)}円
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.agxTotalFeeRate)}%
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.agxTotalFee)}円
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    ({formatNumber(item.agxInHouseTax)})円
                  </div>
                  <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                    {formatNumber(item.agxPaymentAmount)}円
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tax Summary Table - Only show for dates after switchLayoutDate */}
      {transferDate > switchLayoutDate && (
        <div className="flex justify-end">
          <Card className="w-96 border-0 shadow-none text-[#6F6F6E] rounded-none">
            <CardContent className="pt-6">
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className='text-lg'>非課税小計</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalNonTax)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className='text-lg'>10%小計（税込）</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalInclTax10)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className='text-lg'>内消費税額</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalConsumptionTax)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-semibold text-lg">合計（税込）</TableCell>
                    <TableCell className="text-right font-semibold text-lg">
                      {formatNumber(data.subTotalTaxIncl)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
