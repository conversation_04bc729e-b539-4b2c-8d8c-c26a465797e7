import Box from "@/components/ui/box";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormButtons } from "./ui/FormButtons";
import { STEP } from "@/constants/common.constant";
import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { findDataBank, replaceHalfToFull } from "../utils";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { bankSchema, type BankFormData } from "@/features/application-steps/schemas/bank.schema";
import ConfirmDialog from "@/components/ConfirmDialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";

const url = "https://apis.bankcode-jp.com/v1/banks/";

const Bank = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {

    const [show, setShow] = useState(false);
    const [showGuideModal, setShowGuideModal] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        trigger,
        setValue,
        getValues,
        watch
    } = useForm<BankFormData>({
        resolver: zodResolver(bankSchema),
        mode: "onBlur",
        defaultValues: {
            agxBankNo: "",
            agxBankName: "",
            agxBankPhoneticName: "",
            agxBranchNo: "",
            agxBranchName: "",
            agxBranchPhoneticName: "",
            agxAccountType: "*********",
            agxAccountNo: "",
            agxAccountHolder: "",
        }
    });

    const onSubmit = async () => {
        const isValid = await trigger();
        if (!isValid) {
            return;
        }
        setAgxMerchantParams({
            ...agxMerchantParams,
            ...getValues(),
        } as AgxMerchantParams);

        setStep(STEP.ADDITIONAL);
    };

    const showConfirmDialog = async () => {
        const isValid = await trigger();
        if (!isValid) {
            return false;
        }
        setShow(true);
    }

    const onSave = async () => {
        updateMerchant({ ...agxMerchantParams, ...getValues() } as AgxMerchantParams);
        setShow(false);
    };

    const handleBack = () => {

        setAgxMerchantParams({
            ...agxMerchantParams,
            ...getValues(),
        } as AgxMerchantParams);

        setStep(STEP.SHOPINFO);
    };

    const handleChangeAgxBankNo = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxBankNo", value);
        trigger("agxBankNo");

        try {
            if (value?.trim().length === 4) {
                const data = await findDataBank(url, value);
                if (data) {
                    setValue("agxBankNo", value);
                    setValue("agxBankName", replaceHalfToFull(data?.name));
                    setValue("agxBankPhoneticName", data?.halfWidthKana);
                } else {
                    setValue("agxBankName", "");
                    setValue("agxBankPhoneticName", "");
                }
                trigger(["agxBankNo", "agxBankName", "agxBankPhoneticName"]);
            }
        } catch (error) {
            console.error(error);
        }
    };

    const handleChangeAgxBranchNo = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxBranchNo", value);
        trigger("agxBranchNo");

        try {
            if (value?.trim().length === 3) {
                const urlBranch = url + getValues().agxBankNo + "/branches/";
                const data = await findDataBank(urlBranch, value);
                setValue("agxBranchNo", value);
                setValue("agxBranchName", data?.length > 0 ? replaceHalfToFull(data[0]?.name) : '');
                setValue("agxBranchPhoneticName", data?.length > 0 ? data[0]?.halfWidthKana : '');
                trigger(["agxBranchNo", "agxBranchName", "agxBranchPhoneticName"]);
            }
        } catch (error) {
            console.error(error);
        }
    }

    useEffect(() => {
        if (agxMerchantParams) {
            setValue("agxBankNo", agxMerchantParams.agxBankNo || "");
            setValue("agxBankName", agxMerchantParams.agxBankName || "");
            setValue("agxBankPhoneticName", agxMerchantParams.agxBankPhoneticName || "");
            setValue("agxBranchNo", agxMerchantParams.agxBranchNo || "");
            setValue("agxBranchName", agxMerchantParams.agxBranchName || "");
            setValue("agxBranchPhoneticName", agxMerchantParams.agxBranchPhoneticName || "");
            setValue("agxAccountNo", agxMerchantParams.agxAccountNo || "");
            setValue("agxAccountHolder", agxMerchantParams.agxAccountHolder || "");
            setValue("agxAccountType", agxMerchantParams.agxAccountType.toString() || "");
        }
    }, [agxMerchantParams]);

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box className="mx-auto mb-32 mt-[41px] space-y-[20px] sm:space-y-[41px]">
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            銀行名<span className="text-[#FF0000] text-lg sm:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full sm:max-w-[377px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxBankNo")}
                                maxLength={4}
                                data-toggle='tooltip'
                                data-placement='top'
                                data-original-title='※4桁の数字で入力してください。'
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="金融機関コード（半角数字）"
                                onChange={handleChangeAgxBankNo}
                            />
                            <div className="min-h-[20px] mt-1">
                                {errors.agxBankNo && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                        {errors.agxBankNo?.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full sm:max-w-[291px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxBankName")}
                                maxLength={15}
                                onBlur={() => trigger(["agxBankNo", "agxBankName", "agxBankPhoneticName"])}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="銀行名（全角）"
                            />
                        </Box>
                        <Box className="w-full sm:max-w-[291px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxBankPhoneticName")}
                                maxLength={15}
                                onBlur={() => trigger(["agxBankNo", "agxBankName", "agxBankPhoneticName"])}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="ｷﾞﾝｺｳﾒｲ（半角ｶﾅ）"
                            />
                        </Box>
                    </Box>

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            支店名<span className="text-[#FF0000] text-lg sm:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full sm:max-w-[377px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxBranchNo")}
                                maxLength={3}
                                data-toggle='tooltip'
                                data-placement='top'
                                data-original-title='※4桁の数字で入力してください。'
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="支店番号（半角数字）"
                                onChange={handleChangeAgxBranchNo}
                            />
                            <div className="min-h-[20px] mt-1">
                                {(errors.agxBranchNo) && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                        {errors.agxBranchNo?.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full sm:max-w-[291px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxBranchName")}
                                maxLength={15}
                                onBlur={() => trigger(["agxBranchName", "agxBranchPhoneticName", "agxBranchNo"])}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="銀行名（全角）"
                            />
                        </Box>
                        <Box className="w-full sm:max-w-[302px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxBranchPhoneticName")}
                                maxLength={15}
                                onBlur={() => trigger(["agxBranchPhoneticName", "agxBranchName", "agxBranchNo"])}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="ｷﾞﾝｺｳﾒｲ（半角ｶﾅ）"
                            />
                        </Box>
                    </Box>

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            口座番号<span className="text-[#FF0000] text-lg sm:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full sm:max-w-[291px] sm:flex-1 relative flex flex-col">
                            <select
                                {...register("agxAccountType")}
                                className="
                                w-full h-[40px] md:h-[50px] lg:h-[66px]
                                rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                border border-solid border-[#707070]
                                px-3 md:px-4 lg:px-[17px]
                                py-0 md:py-0 lg:py-0
                                pr-10 md:pr-12 lg:pr-14
                                text-sm md:text-base lg:text-[1.75rem] 
                                text-[#707070] bg-white
                                leading-[40px] md:leading-[50px] lg:leading-[66px]
                                focus:border-[#1D9987] focus:outline-none transition-colors 
                                appearance-none
                                disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                            >
                                <option value={*********}>普通預金</option>
                                <option value={283260001}>当座預金</option>
                            </select>
                            {/* Custom dropdown arrow */}
                            <div className="absolute right-0 top-[40%] -translate-y-1/2 flex items-center pr-4 pointer-events-none">
                                <svg className="w-5 h-5 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="min-h-[20px] mt-1">
                                {errors.agxAccountNo && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                        {errors.agxAccountNo.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full sm:max-w-[291px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxAccountNo")}
                                maxLength={7}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="口座番号（半角数字）"
                            />
                        </Box>
                    </Box>

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            口座名義<span className="text-[#FF0000] text-lg sm:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full sm:max-w-[291px] sm:flex-1 flex flex-col">
                            <input
                                {...register("agxAccountHolder")}
                                maxLength={30}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="ｺｳｻﾞﾒｲｷﾞ（半角ｶﾅ）"
                            />
                            <div className="min-h-[20px] mt-1">
                                {errors.agxAccountHolder && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                        {errors.agxAccountHolder.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                    </Box>
                    <Box className="flex flex-col sm:flex-row gap-4 sm:gap-10 items-start relative !mt-0">
                        <Label className="w-full sm:w-[22.5%] text-lg sm:text-[1.75rem] required text-[#C44546]" />
                        <Box className="flex-1 flex flex-col mt-0">
                            <Alert variant="destructive" className="border-none mt-0 mb-0 pt-0">
                                <AlertDescription className="text-sm md:text-[1.125rem]">
                                    <p className="mb-2 text-[#C44546]">※ 法人の場合、口座名義(ｶﾅ)はご登録頂いた法人名と同一名義、法人組織名は略語で入力してください。</p>
                                    <p className="mb-3 text-[#C44546]">（ご登録頂いた法人名と異名義の口座指定や法人組織名の正式名称はご利用いただけません。）</p>
                                    <Box className="text-sm md:text-[1.125rem] text-[#707070] mt-2 space-y-1">
                                        <p>例）株式会社チョキペイ → ｶ)ﾁﾖｷﾍﾟｲ</p>
                                        <p className="ml-2 md:ml-4">チョキペイ有限会社 → ﾁﾖｷﾍﾟｲ(ﾕ</p>
                                        <p className="ml-2 md:ml-4">医療法人チョキペイ → ｲ)ﾁﾖｷﾍﾟｲ</p>
                                        <p className="ml-2 md:ml-4">医療法人社団チョキペイ → ｲ)ﾁﾖｷﾍﾟｲ</p>
                                        <p className="ml-2 md:ml-4 mb-3 md:mb-4">医療法人財団チョキペイ→ ｲ)ﾁﾖｷﾍﾟｲ</p>
                                        <button 
                                            type="button"
                                            onClick={() => setShowGuideModal(true)}
                                            className="text-[#1AA492] block ml-2 md:ml-4 hover:underline text-sm md:text-[1.375rem] leading-relaxed cursor-pointer bg-transparent border-none p-0"
                                        >
                                            入力可能な文字・法人略語の入力例はこちら
                                        </button>
                                    </Box>
                                </AlertDescription>
                            </Alert>
                        </Box>
                    </Box>

                    {/* Form buttons */}
                    <FormButtons
                        onSave={showConfirmDialog}
                        onNext={onSubmit}
                        onBack={handleBack}
                        isSubmitting={isUpdating}
                        showBackButton={true}
                    />
                </Box>
            </form>
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                title="入力内容を一時保存します。"
                cancelLabel="戻る"
                confirmLabel="一時保存"
                confirmVariant="danger"
                onCancel={() => setShow(false)}
                onConfirm={onSave}
            />

            {/* 入力可能な文字・法人略語の入力例 Modal */}
            <Dialog open={showGuideModal} onOpenChange={setShowGuideModal}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-bold">口座名義の記載について</DialogTitle>
                        <DialogDescription></DialogDescription>
                    </DialogHeader>
                    <div className="space-y-6 p-4">
                        {/* I. 入力できる文字 */}
                        <div>
                            <h3 className="text-lg font-semibold mb-3">I. 入力できる文字</h3>
                            <div className="overflow-x-auto">
                                <table className="w-full border border-gray-400 border-collapse text-sm">
                                    <thead>
                                        <tr>
                                            <td className="border border-gray-400 p-2 bg-gray-50"></td>
                                            <td className="border border-gray-400 p-2 bg-gray-50"></td>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold">注意事項</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td className="border border-gray-400 p-2 font-semibold">入力できる文字</td>
                                            <td className="border border-gray-400 p-2">半角カタカナ</td>
                                            <td className="border border-gray-400 p-2">
                                                <div className="space-y-2">
                                                    <p>※以下の文字は入力できません。</p>
                                                    <div className="space-y-1">
                                                        <p>① 促音（ｯ）、拗音（ｬ、ｭ、ｮ）</p>
                                                        <p className="ml-4">代わりに「ﾂ、ﾔ、ﾕ、ﾖ」を入力してください。</p>
                                                        <p>② 「・（中黒）」</p>
                                                        <p className="ml-4">代わりに半角スペースを入力してください。</p>
                                                        <p>③ 「ｦ」</p>
                                                        <p className="ml-4">代わりに「ｵ」を入力してください。</p>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* II. 個人名・法人名称の記載方法 */}
                        <div>
                            <h3 className="text-lg font-semibold mb-3">II. 個人名・法人名称の記載方法</h3>
                            <div className="overflow-x-auto">
                                <table className="w-full border border-gray-400 border-collapse text-sm">
                                    <thead>
                                        <tr>
                                            <td className="border border-gray-400 p-2 bg-gray-50"></td>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold">記載方法</td>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold">記入例</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td className="border border-gray-400 p-2 font-semibold">個人名</td>
                                            <td className="border border-gray-400 p-2">姓と名の間にスペースを入れてください</td>
                                            <td className="border border-gray-400 p-2">
                                                                                                 <div className="space-y-1">
                                                     <p>・鈴木太郎 → 正: ｽｽﾞｷ ﾀﾛｳ</p>
                                                     <p className="ml-8">誤: ｽｽﾞｷﾀﾛｳ</p>
                                                 </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td className="border border-gray-400 p-2 font-semibold">法人名</td>
                                            <td className="border border-gray-400 p-2">法人、営業所および事業所の種類名は、括弧を付して略語を使用してください</td>
                                                                                         <td className="border border-gray-400 p-2">
                                                 <div className="space-y-1">
                                                     <p>・株式会社チョキペイ → ｶ)ﾁｮｷﾍﾟｲ</p>
                                                     <p>・チョキペイ有限会社 → ﾁｮｷﾍﾟｲ(ﾕ</p>
                                                     <p>・医療法人チョキペイ → ｲ)ﾁｮｷﾍﾟｲ</p>
                                                     <p>・チョキペイ株式会社東京営業所</p>
                                                     <p className="ml-4">→ ﾁｮｷﾍﾟｲ(ｶ)ﾄｳｷｮｳ(ｴｲ</p>
                                                 </div>
                                             </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* III. 法人組織名略語表 */}
                        <div>
                            <h3 className="text-lg font-semibold mb-3">III. 法人組織名略語表</h3>
                            <div className="overflow-x-auto">
                                <table className="w-full border border-gray-400 border-collapse text-xs">
                                    <thead>
                                        <tr>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold text-center" rowSpan={2}>正式名称</td>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold text-center" colSpan={3}>略語（使用場所別）</td>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold text-center" rowSpan={2}>正式名称</td>
                                            <td className="border border-gray-400 p-2 bg-gray-50 font-semibold text-center" colSpan={3}>略語（使用場所別）</td>
                                        </tr>
                                        <tr>
                                            <td className="border border-gray-400 p-1 bg-gray-50 font-semibold text-center">先頭</td>
                                            <td className="border border-gray-400 p-1 bg-gray-50 font-semibold text-center">途中</td>
                                            <td className="border border-gray-400 p-1 bg-gray-50 font-semibold text-center">末尾</td>
                                            <td className="border border-gray-400 p-1 bg-gray-50 font-semibold text-center">先頭</td>
                                            <td className="border border-gray-400 p-1 bg-gray-50 font-semibold text-center">途中</td>
                                            <td className="border border-gray-400 p-1 bg-gray-50 font-semibold text-center">末尾</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {[
                                            ["株式会社", "ｶ)", "(ｶ)", "(ｶ", "更正保護法人", "ﾎｺﾞ)", "(ﾎｺﾞ)", "(ﾎｺﾞ"],
                                            ["有限会社", "ﾕ)", "(ﾕ)", "(ﾕ", "相互会社", "ｿ)", "(ｿ)", "(ｿ"],
                                            ["合名会社", "ﾒ)", "(ﾒ)", "(ﾒ", "特定非営利活動法人", "ﾄｸﾋ)", "(ﾄｸﾋ)", "(ﾄｸﾋ"],
                                            ["合資会社", "ｼ)", "(ｼ)", "(ｼ", "独立行政法人", "ﾄﾞｸ)", "(ﾄﾞｸ)", "(ﾄﾞｸ"],
                                            ["医療法人", "ｲ)", "(ｲ)", "(ｲ", "弁護士法人", "ﾍﾞﾝ)", "(ﾍﾞﾝ)", "(ﾍﾞﾝ"],
                                            ["医療法人社団", "ｲ)", "(ｲ)", "(ｲ", "有限責任中間法人", "ﾁﾕｳ)", "(ﾁﾕｳ)", "(ﾁﾕｳ"],
                                            ["医療法人財団", "ｲ)", "(ｲ)", "(ｲ", "無限責任中間法人", "ﾁﾕｳ)", "(ﾁﾕｳ)", "(ﾁﾕｳ"],
                                            ["財団法人", "ｻﾞｲ)", "(ｻﾞｲ)", "(ｻﾞｲ", "行政書士法人", "ｷﾞﾖ)", "(ｷﾞﾖ)", "(ｷﾞﾖ"],
                                            ["社団法人", "ｼﾔ)", "(ｼﾔ)", "(ｼﾔ", "司法書士法人", "ｼﾎｳ)", "(ｼﾎｳ)", "(ｼﾎｳ"],
                                            ["宗教法人", "ｼﾕｳ)", "(ｼﾕｳ)", "(ｼﾕｳ", "税理士法人", "ｾﾞｲ)", "(ｾﾞｲ)", "(ｾﾞｲ"],
                                            ["学校法人", "ｶﾞｸ)", "(ｶﾞｸ)", "(ｶﾞｸ", "国立大学法人", "ﾀﾞｲ)", "(ﾀﾞｲ)", "(ﾀﾞｲ"],
                                            ["社会福祉法人", "ﾌｸ)", "(ﾌｸ)", "(ﾌｸ", "農事組合法人", "ﾉｳ)", "(ﾉｳ)", "(ﾉｳ"],
                                            ["営業所", "ｴｲ)", "(ｴｲ)", "(ｴｲ", "出張所", "ｼﾕﾂ)", "(ｼﾕﾂ)", "(ｼﾕﾂ"]
                                        ].map((row, index) => (
                                            <tr key={index}>
                                                <td className="border border-gray-400 p-1">{row[0]}</td>
                                                <td className="border border-gray-400 p-1 text-center">{row[1]}</td>
                                                <td className="border border-gray-400 p-1 text-center">{row[2]}</td>
                                                <td className="border border-gray-400 p-1 text-center">{row[3]}</td>
                                                <td className="border border-gray-400 p-1">{row[4]}</td>
                                                <td className="border border-gray-400 p-1 text-center">{row[5]}</td>
                                                <td className="border border-gray-400 p-1 text-center">{row[6]}</td>
                                                <td className="border border-gray-400 p-1 text-center">{row[7]}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div className="flex justify-end pt-4">
                            <button 
                                type="button"
                                onClick={() => setShowGuideModal(false)}
                                className="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                            >
                                閉じる
                            </button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    )
}

export default Bank;