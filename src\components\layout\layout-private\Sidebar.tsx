import React from 'react';
import { Sidebar } from '@/components/ui/sidebar';
import { 
  AppSidebarHeader,
  AppSidebarMenu,
  AppSidebarFooter,
  LogoutModal 
} from '@/features/app-sidebar';
import { useSidebar, SIDEBAR_STYLES } from '@/features/app-sidebar';

export const AppSidebar: React.FC = React.memo(() => {
  const {
    user,
    menuItems,
    openSubmenus,
    showLogoutModal,
    handleSubmenuToggle,
    handleLogoutClick,
    handleConfirmLogout,
    handleCancelLogout,
  } = useSidebar();

  return (
    <>
      <Sidebar
        className="!bg-white border-r border-gray-500"
        style={SIDEBAR_STYLES.sidebar}
      >
        <AppSidebarHeader 
          agxMerchantNo={user?.agxMerchantNo}
          agxStoreName={user?.agxStoreName}
        />
        
        <AppSidebarMenu 
          menuItems={menuItems}
          openSubmenus={openSubmenus}
          onSubmenuToggle={handleSubmenuToggle}
          onLogout={handleLogoutClick}
        />
        
        <AppSidebarFooter 
          lastSuccessfulLogin={user?.lastSuccessfulLogin}
        />
      </Sidebar>

      <LogoutModal
        isOpen={showLogoutModal}
        onClose={handleCancelLogout}
        onConfirm={handleConfirmLogout}
      />
    </>
  );
});

AppSidebar.displayName = 'AppSidebar';