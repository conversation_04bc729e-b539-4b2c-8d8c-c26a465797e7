import { z } from "zod";
import { validateHumanName, validateWideKana, validateWideString } from "../utils/validate";

export const daiyoushaSchema = z.object({
    agxRepresentativeLastName: z.string().trim(),
    agxRepresentativeFirstName: z.string().trim(),
    agxRepresentativePhoneticLastName: z.string().trim(),
    agxRepresentativePhoneticFirstName: z.string().trim(),
    agxRepresentativeGender: z.coerce.number().optional(),
    representativeBirthdayYear: z.coerce.string().optional(),
    representativeBirthdayMonth: z.coerce.string().optional(),
    representativeBirthdayDay: z.coerce.string().optional(),
    agxRepresentativeBirthday: z.string().optional(),
    agxRepresentativeAddressCopyFlag: z.boolean().default(false),
    agxRepresentativePostalCode: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .regex(/^(\d{7}|\d{3}-\d{4})$/, {
            message: "※書式が不正です。"
        }),
    agxRepresentativePrefecture: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        }),
    agxRepresentativeAddress1: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxRepresentativeAddress2: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxRepresentativePhoneticAddress1: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxRepresentativePhoneticAddress2: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxRepresentativePhoneNumber1: z.string().trim().optional(),
    agxRepresentativePhoneNumber2: z.string().trim().optional(),
    agxRepresentativePhoneNumber3: z.string().trim().optional(),
    agxRepresentativeFaxNumber1: z.string().trim().optional(),
    agxRepresentativeFaxNumber2: z.string().trim().optional(),
    agxRepresentativeFaxNumber3: z.string().trim().optional(),
})
// Validate representative name
.refine((data) => {
    if (data.agxRepresentativeLastName?.trim() === "" && data.agxRepresentativeFirstName?.trim() === "") {
        return false;
    }
    return true;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxRepresentativeLastName"]
})
// Validate representative name
.refine((data) => {
    const agxRepresentativeName = data.agxRepresentativeLastName?.trim() + " " + data.agxRepresentativeFirstName?.trim();
    return validateWideString(agxRepresentativeName, true);
}, {
    message: "※全角文字で入力してください。",
    path: ["agxRepresentativeLastName"]
})
.refine((data) => {
    const agxRepresentativeName = data.agxRepresentativeLastName?.trim() + " " + data.agxRepresentativeFirstName?.trim();
    return validateHumanName(agxRepresentativeName);
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxRepresentativeLastName"]
})
// Validate representative phonetic name
.refine((data) => {
    if (data.agxRepresentativePhoneticLastName && data.agxRepresentativePhoneticFirstName) {
        return true;
    }
    return false;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxRepresentativePhoneticLastName"]
})
.refine((data) => {
    const agxRepresentativePhoneticName = data.agxRepresentativePhoneticLastName?.trim() + " " + data.agxRepresentativePhoneticFirstName?.trim();
    return validateWideKana(agxRepresentativePhoneticName, true);
}, {
    message: "※全角カタカナで入力してください。",
    path: ["agxRepresentativePhoneticLastName"]
})
.refine((data) => {
    const agxRepresentativePhoneticName = data.agxRepresentativePhoneticLastName?.trim() + " " + data.agxRepresentativePhoneticFirstName?.trim();
    return validateHumanName(agxRepresentativePhoneticName);
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxRepresentativePhoneticLastName"]
})
// Validate representative phone number
.refine((data) => {

    const allThreeFilled = (
        data.agxRepresentativePhoneNumber1 && data.agxRepresentativePhoneNumber1.trim() !== "" &&
        data.agxRepresentativePhoneNumber2 && data.agxRepresentativePhoneNumber2.trim() !== "" &&
        data.agxRepresentativePhoneNumber3 && data.agxRepresentativePhoneNumber3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxRepresentativePhoneNumber1"],
})
.refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxRepresentativePhoneNumber1 || "")
        && pat.test(data.agxRepresentativePhoneNumber2 || "")
        && pat.test(data.agxRepresentativePhoneNumber3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxRepresentativePhoneNumber1"]
})
// Validate fax number
.refine((data) => {
    const hasAtLeastOneFax = (
        (data.agxRepresentativeFaxNumber1 && data.agxRepresentativeFaxNumber1.trim() !== "") ||
        (data.agxRepresentativeFaxNumber2 && data.agxRepresentativeFaxNumber2.trim() !== "") ||
        (data.agxRepresentativeFaxNumber3 && data.agxRepresentativeFaxNumber3.trim() !== "")
    );

    if (!hasAtLeastOneFax) {
        return true;
    }

    const allThreeFilled = (
        data.agxRepresentativeFaxNumber1 && data.agxRepresentativeFaxNumber1.trim() !== "" &&
        data.agxRepresentativeFaxNumber2 && data.agxRepresentativeFaxNumber2.trim() !== "" &&
        data.agxRepresentativeFaxNumber3 && data.agxRepresentativeFaxNumber3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxRepresentativeFaxNumber1"],
})
.refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxRepresentativeFaxNumber1 || "")
        && pat.test(data.agxRepresentativeFaxNumber2 || "")
        && pat.test(data.agxRepresentativeFaxNumber3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxRepresentativeFaxNumber1"]
});

export type DaiyoushaFormData = z.infer<typeof daiyoushaSchema>;