import Box from "@/components/ui/box";
import { STEP } from "@/constants/common.constant";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ApplicationStepProps } from "../types";
import { useKiyakuImproved } from "../hooks/useKiyakuImproved";

export const KiyakuPaygate = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {
    const {
        kiyakuRulesState,
        kiyakuLabelState,
        handleKiyakuClickCheckbox,
        handleKiyakuPDFOpen,
        handleKiyakuCheck,
        getValidationStatus,
        isSettlementTypeSelected,
        saveKiyakuState
    } = useKiyakuImproved(agxMerchantParams);

    // Handle next button click
    const handleKiyakuNext = () => {
        if (handleKiyakuCheck()) {
            // Save current kiyaku agreements state to agxMerchantParams before next
            saveKiyakuState(setAgxMerchantParams);
            setStep(STEP.CONFIRM);
        }
    };

    // Handle back button click
    const handleKiyakuBack = () => {
        // Save current kiyaku agreements state to agxMerchantParams before back
        saveKiyakuState(setAgxMerchantParams);
        setStep(STEP.CHOQIPAY);
    };

    // Helper function to render agreement rule
    const renderAgreementRule = (
        ruleKey: string,
        labelKey: string,
        pdfKey: string,
        title: string,
        isVisible: boolean = true
    ) => {
        if (!isVisible) return null;

        return (
            <Box className="flex gap-3 sm:gap-4 items-start mb-4 md:mb-6" key={ruleKey}>
                <Box className="w-[30px] md:w-[40px] lg:w-[5%] flex items-start justify-center md:justify-end mt-1">
                    <Checkbox
                        id={`rule_${ruleKey}`}
                        name={ruleKey}
                        checked={kiyakuRulesState[ruleKey as keyof typeof kiyakuRulesState] || false}
                        disabled={kiyakuLabelState[labelKey as keyof typeof kiyakuLabelState]}
                        onCheckedChange={(checked) => handleKiyakuClickCheckbox(ruleKey, checked as boolean)}
                        className="w-[18px] h-[18px] md:w-[22px] md:h-[22px] lg:w-[27px] lg:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0"
                    />
                </Box>
                <Box className="flex-1">
                    <Box className="mb-2">
                        <Button
                            onClick={() => handleKiyakuPDFOpen(pdfKey, labelKey)}
                            className={`text-sm md:text-base lg:text-[1.75rem] text-decoration-underline underline-offset-4 bg-transparent hover:bg-transparent transition-colors p-0 h-auto text-left ${kiyakuLabelState[labelKey as keyof typeof kiyakuLabelState] ? 'cursor-allowed text-[#19A492]' : 'text-[#707070]'}`}
                        >
                            {title}
                        </Button>
                    </Box>
                </Box>
            </Box>
        );
    };

    return (
        <>
            <Box className="mx-auto space-y-4 md:space-y-6 lg:space-y-8">
                <Box className="flex flex-col gap-4">
                    <Box className="space-y-4 md:space-y-6 lg:space-y-8">
                        <h1 className="font-normal text-[#707070] pl-0 sm:pl-4 md:pl-6 lg:pl-[30px] mb-4 md:mb-6 text-base md:text-xl lg:text-[1.75rem]">
                            規約の内容をご確認ください
                        </h1>
                    </Box>
                </Box>
            </Box>

            <Box className="mx-auto space-y-4 md:space-y-6 lg:space-y-8 mb-6 sm:mb-8 lg:mb-10 mt-4 sm:mt-6 lg:mt-10">
                {/* Basic required agreements (always shown) */}
                {renderAgreementRule('rule_1', 'label_1', 'choqipay', 'チョキペイ加盟店利用規約')}
                {renderAgreementRule('rule_2', 'label_2', 'mufg', 'MUFGカード加盟店規約')}
                {renderAgreementRule('rule_3', 'label_3', 'jcb', 'JCB加盟店規約')}
                {renderAgreementRule('rule_4', 'label_4', 'jcbPremo', 'JCB PREMO取扱加盟店特約')}
                {renderAgreementRule('rule_5', 'label_5', 'tanako', '店子加盟店特約（店頭通販共通）')}
                {renderAgreementRule('rule_6', 'label_6', 'ginren', '銀聯カード加盟店規約')}

                {/* Electronic money agreements - only shown when respective settlement is selected */}
                {renderAgreementRule(
                    'rule_7',
                    'label_7',
                    'traffic',
                    '三菱UFJニコス－交通系電子マネー加盟店規約',
                    isSettlementTypeSelected('traffic')
                )}
                {renderAgreementRule(
                    'rule_8',
                    'label_8',
                    'nanaco',
                    '三菱UFJニコス－nanaco電子マネー加盟店規約',
                    isSettlementTypeSelected('nanaco')
                )}
                {renderAgreementRule(
                    'rule_9',
                    'label_9',
                    'waon',
                    '三菱UFJニコス－WAON加盟店規約',
                    isSettlementTypeSelected('waon')
                )}
                {renderAgreementRule(
                    'rule_10',
                    'label_10',
                    'edy',
                    '三菱UFJニコス－Edy間接加盟店規約',
                    isSettlementTypeSelected('edy')
                )}
                {renderAgreementRule(
                    'rule_11',
                    'label_11',
                    'id',
                    '三菱UFJニコス－ｉＤ加盟店規約',
                    isSettlementTypeSelected('aid')
                )}

                {/* QR code agreement - only shown when QR code settlement is selected */}
                {renderAgreementRule(
                    'rule_12',
                    'label_12',
                    'qrCode',
                    'QRコード決済規約',
                    isSettlementTypeSelected('qrCode')
                )}

                {/* Crepico agreements (always required) */}
                {renderAgreementRule('rule_13', 'label_13', 'crepicoCredit', 'クレピコ端末設置使用規約（クレジット編）')}
                {renderAgreementRule('rule_14', 'label_14', 'crepicoMaintenance', 'クレピコ端末保守サービス規約')}
                {renderAgreementRule('rule_15', 'label_15', 'crepicoService', 'クレピコサービス加入規約')}

                {/* SME program agreements - only shown when business type is 283260008 */}
                {renderAgreementRule(
                    'rule_16',
                    'label_16',
                    'smeProgram',
                    '中小企業優遇プログラム_同意事項',
                    isSettlementTypeSelected('sme')
                )}
                {renderAgreementRule(
                    'rule_17',
                    'label_17',
                    'smeStatement',
                    '中小企業優遇プログラム_表明・確約事項',
                    isSettlementTypeSelected('sme')
                )}
            </Box>

            <Box className="flex mb-32 flex-col gap-4 mt-6 md:mt-8 justify-center items-center">
                <Button
                    onClick={handleKiyakuNext}
                    disabled={!handleKiyakuCheck()}
                    className="rounded-lg md:rounded-xl w-full sm:w-[320px] md:w-[350px] lg:w-[369px] h-[50px] md:h-[60px] lg:h-[66px] bg-[#1AA492] text-white py-3 md:py-4 px-4 md:px-6 text-base md:text-lg font-medium hover:bg-[#159080] transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                    規約に同意して内容確認へ
                </Button>

                <Button
                    onClick={handleKiyakuBack}
                    className="rounded-lg md:rounded-xl w-full sm:w-[320px] md:w-[350px] lg:w-[369px] h-[50px] md:h-[60px] lg:h-[66px] bg-gray-400 text-white py-3 md:py-4 px-4 md:px-6 text-base md:text-lg font-medium hover:bg-gray-500 transition-colors"
                >
                    戻る
                </Button>
            </Box>

        </>
    );
};
