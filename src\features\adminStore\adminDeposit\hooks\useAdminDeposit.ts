import { useState, useMemo, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { adminDepositService } from '../services/adminDepositService';
import { AdminDepositFilters } from '../types';
import { PDFTemplates } from '@/services/pdfTemplates';

const initialAreaFilters = {
  areaSelected: 'all',
  subAreaSelected: 'all',
  merchantSelected: 'all'
};

export const useAdminDeposit = (agxMerchantNo: string) => {
  // Transfer date (auto-triggers API when changed)
  const [transferDate, setTransferDate] = useState('');
  // Area filters for UI (only updated when user interacts with selectors)
  const [areaFilters, setAreaFilters] = useState(initialAreaFilters);
  // PDF display text states
  const [areaSelectorPDF, setAreaSelectorPDF] = useState('全て');
  const [subAreaSelectorPDF, setSubAreaSelectorPDF] = useState('全て');
  const [merchantSelectorPDF, setMerchantSelectorPDF] = useState('全て');
  const [filterLoading, setFilterLoading] = useState(false);

  // Get available dates
  const {
    data: dates,
    isLoading: datesLoading,
    error: datesError
  } = useQuery({
    queryKey: ['admin-deposit-dates', agxMerchantNo],
    queryFn: () => adminDepositService.getDateAdminStoreFromAgxPaymentManagement(agxMerchantNo),
    enabled: !!agxMerchantNo
  });

  // Get selector data
  const {
    data: selectorData,
    isLoading: selectorLoading,
    error: selectorError
  } = useQuery({
    queryKey: ['admin-deposit-selector', agxMerchantNo],
    queryFn: () => adminDepositService.getDataSelector(agxMerchantNo),
    enabled: !!agxMerchantNo
  });

  // Set initial transfer date when dates are loaded
  useEffect(() => {
    if (dates && dates.length > 0) {
      setTransferDate(dates[0]);
    }
  }, [dates]);

  // Get deposit data with useQuery
  const {
    data: depositData,
    isLoading: depositLoading,
    error: depositError,
    refetch: refetchDepositData
  } = useQuery({
    queryKey: [
      'admin-deposit-data',
      agxMerchantNo,
    ],
    queryFn: () => {
      const agxMerchantNoEncode = btoa(agxMerchantNo);
      return adminDepositService.getDataAdminDeposit(
        agxMerchantNoEncode,
        transferDate,
        areaFilters.areaSelected && areaFilters.areaSelected !== 'all' ? areaFilters.areaSelected : '',
        areaFilters.subAreaSelected && areaFilters.subAreaSelected !== 'all' ? areaFilters.subAreaSelected : '',
        areaFilters.merchantSelected && areaFilters.merchantSelected !== 'all' ? areaFilters.merchantSelected : ''
      );
    },
    enabled: !!agxMerchantNo && !!transferDate
  });

  // Get current area ID from any selected filter
  const getCurrentAreaId = () => {
    if (areaFilters.areaSelected && areaFilters.areaSelected !== 'all') return areaFilters.areaSelected;

    if (areaFilters.subAreaSelected && areaFilters.subAreaSelected !== 'all') {
      const selectedSubArea = selectorData?.agxSubAreas.find(item => item.agxSubAreaid === areaFilters.subAreaSelected);
      return selectedSubArea?.agxAreaid || null;
    }

    if (areaFilters.merchantSelected && areaFilters.merchantSelected !== 'all') {
      const selectedMerchant = selectorData?.merchants.find(item => item.agxMerchantNo === areaFilters.merchantSelected);
      return selectedMerchant?.agxAreaid || null;
    }

    return null;
  };

  // Filter areas based on selected subArea or merchant
  const filteredAreas = useMemo(() => {
    if (!selectorData) return [];

    // If subArea is selected, filter areas by that subArea's area
    if (areaFilters.subAreaSelected && areaFilters.subAreaSelected !== 'all') {
      const selectedSubArea = selectorData.agxSubAreas.find(item => item.agxSubAreaid === areaFilters.subAreaSelected);
      const areaId = selectedSubArea?.agxAreaid;
      return areaId ? selectorData.agxAreaFormUsers.filter(item => item.agx_areaid === areaId) : [];
    }

    // If merchant is selected, filter areas by that merchant's area
    if (areaFilters.merchantSelected && areaFilters.merchantSelected !== 'all') {
      const selectedMerchant = selectorData.merchants.find(item => item.agxMerchantNo === areaFilters.merchantSelected);
      const areaId = selectedMerchant?.agxAreaid;
      return areaId ? selectorData.agxAreaFormUsers.filter(item => item.agx_areaid === areaId) : [];
    }

    // Otherwise show all areas
    return selectorData.agxAreaFormUsers;
  }, [selectorData, areaFilters.subAreaSelected, areaFilters.merchantSelected]);

  // Filter sub areas based on current area
  const filteredSubAreas = useMemo(() => {
    if (!selectorData) return [];
    if(areaFilters.merchantSelected && areaFilters.merchantSelected !== 'all') {
      const selectedMerchant = selectorData.merchants.find(item => item.agxMerchantNo === areaFilters.merchantSelected);
      const subAreaId = selectedMerchant?.agxSubAreaid;
      return subAreaId ? selectorData.agxSubAreas.filter(item => item.agxSubAreaid === subAreaId) : [];
    }
    const areaId = getCurrentAreaId();
    return areaId ? selectorData.agxSubAreas.filter(item => item.agxAreaid === areaId) : selectorData.agxSubAreas;
  }, [selectorData, areaFilters.areaSelected, areaFilters.merchantSelected]);
  
  // Filter merchants based on current area or subArea
  const filteredMerchants = useMemo(() => {
    if (!selectorData) return [];
    // If subArea is selected, filter merchants by that subArea
    if (areaFilters.subAreaSelected && areaFilters.subAreaSelected !== 'all') {
      return selectorData.merchants.filter(item => item.agxSubAreaid === areaFilters.subAreaSelected);
    }
    // Otherwise, filter by area
    const areaId = getCurrentAreaId();
    return areaId ? selectorData.merchants.filter(item => item.agxAreaid === areaId) : selectorData.merchants;
  }, [selectorData, areaFilters.areaSelected, areaFilters.subAreaSelected]);

  // Handle area filters change with dependency logic
  const handleAreaFiltersChange = (newFilters: Partial<typeof initialAreaFilters>) => {
    // Handle area change
    if (newFilters.areaSelected !== undefined) {
      setAreaFilters(prev => ({ ...prev, ...newFilters, subAreaSelected: 'all', merchantSelected: 'all' }));
      return;
    }
    // Handle sub area change
    if (newFilters.subAreaSelected !== undefined) {
      setAreaFilters(prev => ({
        ...prev,
        ...newFilters,
        merchantSelected: 'all'
      }));
      return;
    }
    // Handle merchant change
    if (newFilters.merchantSelected !== undefined) {
      setAreaFilters(prev => ({ ...prev, ...newFilters}));
      return;
    }
  };

  // Set PDF display text
  const setTextDisplayPDF = () => {
    const areaText = areaFilters.areaSelected && areaFilters.areaSelected !== 'all'
      ? selectorData?.agxAreaFormUsers.find(a => a.agx_areaid === areaFilters.areaSelected)?.agxAreaName || '全て'
      : '全て';

    const subAreaText = areaFilters.subAreaSelected && areaFilters.subAreaSelected !== 'all'
      ? selectorData?.agxSubAreas.find(sa => sa.agxSubAreaid === areaFilters.subAreaSelected)?.agxSubAreaName || '全て'
      : '全て';

    const merchantText = areaFilters.merchantSelected && areaFilters.merchantSelected !== 'all'
      ? selectorData?.merchants.find(m => m.agxMerchantNo === areaFilters.merchantSelected)?.agxStoreName || '全て'
      : '全て';

    setAreaSelectorPDF(areaText);
    setSubAreaSelectorPDF(subAreaText);
    setMerchantSelectorPDF(merchantText);
  };

  // Execute search (copy current area filters to search filters and trigger API)
  const handleSearch = () => {
    setTextDisplayPDF();
    setFilterLoading(true);
    refetchDepositData().finally(() => {
      setFilterLoading(false);
    });
  };

  // Handle transfer date change (auto-triggers API)
  const handleTransferDateChange = (value: string) => {
    setTransferDate(value);
    setAreaFilters(initialAreaFilters);
  };
  useEffect(() => {
    if (transferDate) {
      setFilterLoading(true);
      refetchDepositData().finally(() => {
        setFilterLoading(false);
      });
    }
  }, [transferDate]);

  // Handle PDF export
  const handleExportPDF = async () => {
    if (!depositData) {
      alert('エクスポートするデータがありません。');
      return;
    }

    try {
      await PDFTemplates.generateAdminDepositListPDF({
        data: depositData,
        transferDate: transferDate,
        areaName: areaSelectorPDF,
        subAreaName: subAreaSelectorPDF,
        merchantName: merchantSelectorPDF,
        switchLayoutDate: "2023-11-05"
      });
    } catch (error) {
      console.error('PDF export error:', error);
      alert('PDF export failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // Combine all loading states
  const isLoading = datesLoading || selectorLoading || depositLoading;

  // Combine all errors
  const error = datesError || selectorError || depositError;

  // Enable download when we have deposit data
  const dlEnable = !!depositData && !depositLoading && !depositError;

  return {
    // Data
    data: depositData || null,
    dates: dates || [],
    selectorData: selectorData || null,
    transferDate: transferDate,
    areaFilters,
    filteredAreas,
    filteredSubAreas,
    filteredMerchants,
    isLoading,
    filterLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    dlEnable,
    handleAreaFiltersChange,
    handleTransferDateChange,
    handleSearch,
    handleExportPDF,
    refetchDepositData
  };
};
