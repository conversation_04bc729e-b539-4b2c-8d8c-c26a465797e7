import React, { useState } from 'react';
import { Download } from 'lucide-react';
import { CSVLink } from 'react-csv';
import { PDFTemplates } from '@/services/pdfTemplates';
import { mapTransactionType } from '@/constants/common.constant';
import { DepositData } from '@/features/store/deposit/types/depositDetail';

interface ExportButtonsProps {
  depositData: DepositData | null;
  transferDate: string;
  agxMerchantNo: string;
  agxStoreName: string;
  type: string;
  dlEnable: boolean;
}

export const ExportButtons: React.FC<ExportButtonsProps> = ({
  depositData,
  transferDate,
  agxMerchantNo,
  agxStoreName,
  type,
  dlEnable
}) => {
  const [isExportHovered, setIsExportHovered] = useState(false);

  // CSV Export headers
  const headers = [
    { label: '', key: 'id' },
    { label: '', key: 'date' },
    { label: '', key: 'agxTransactionType' },
    { label: '', key: 'agxNumberOfSales' },
    { label: '', key: 'agxSalesAmount' },
    { label: '', key: 'agxTotalFeeRate' },
    { label: '', key: 'agxTotalFee' },
    { label: '', key: 'tax' },
    { label: '', key: 'agxPaymentAmount' }
  ];

  const handleExportPDF = async () => {
    if (!depositData) {
      alert('エクスポートするデータがありません。');
      return;
    }

    try {
      await PDFTemplates.generateStoreDepositPDF({
        depositData,
        transferDate,
        merchantNo: agxMerchantNo,
        storeName: agxStoreName,
        type: type
      });
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF export failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const getDataExport = () => {
    const result = [
      {
        id: '店舗名: ' + agxStoreName,
        date: '',
        agxTransactionType: '',
        agxNumberOfSales: '',
        agxSalesAmount: '',
        agxTotalFeeRate: '',
        agxTotalFee: '',
        tax: '',
        agxPaymentAmount: ''
      },
      {
        id: 'チョキペイ加盟店番号: ' + agxMerchantNo,
        date: '',
        agxTransactionType: '',
        agxNumberOfSales: '',
        agxSalesAmount: '',
        agxTotalFeeRate: '',
        agxTotalFee: '',
        tax: '',
        agxPaymentAmount: ''
      },
      {
        id: '',
        date: '',
        agxTransactionType: '',
        agxNumberOfSales: '',
        agxSalesAmount: '',
        agxTotalFeeRate: '',
        agxTotalFee: '',
        tax: '',
        agxPaymentAmount: ''
      },
      {
        id: '振込ID',
        date: '振込日',
        agxTransactionType: '取引区分',
        agxNumberOfSales: '売上件数',
        agxSalesAmount: '売上金額',
        agxTotalFeeRate: '手数料率',
        agxTotalFee: '手数料額',
        tax: '（内消費税額）',
        agxPaymentAmount: '振込額'
      }
    ];

    depositData?.paymentBreakdowns?.forEach(e => {
      result.push({
        id: e.agxMerchantPaymentId,
        date: transferDate,
        agxTransactionType: `${mapTransactionType.get(e.agxTransactionType)}${e.groupCodeName}`,
        agxNumberOfSales: e.agxNumberOfSales.toString(),
        agxSalesAmount: e.agxSalesAmount.toString(),
        agxTotalFeeRate: e.agxTotalFeeRate + '%',
        agxTotalFee: e.agxTotalFee.toString(),
        tax: `(${e.tax})`,
        agxPaymentAmount: e.agxPaymentAmount.toString()
      });
    });

    return result;
  };

  // CSV export configuration
  const csvExport = {
    data: getDataExport(),
    headers: headers,
    filename: `deposit-${transferDate}.csv`,
    separator: ',', // Sử dụng separator tùy chỉnh
    enclosingCharacter: '' // Bỏ dấu ngoặc kép bao quanh
  };

  if (!depositData) {
    return null;
  }

  return (
    <div className="relative">
      <div
        className="relative"
        onMouseEnter={() => setIsExportHovered(true)}
        onMouseLeave={() => setIsExportHovered(false)}
      >
        {/* Download Icon Button */}
        <button
          className="p-2 text-[#1D9987] hover:text-[#1D9987]/80 hover:opacity-80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!dlEnable}
        >
          <Download className="h-8 w-8" />
        </button>

        {/* Dropdown Menu */}
        {isExportHovered && dlEnable && (
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-32 bg-white border border-gray-300 rounded shadow-md z-50">
            {/* @ts-expect-error : type mismatch due to version node */}
            <CSVLink
              data={csvExport.data}
              headers={csvExport.headers}
              filename={csvExport.filename}
              separator={csvExport.separator}
              enclosingCharacter={csvExport.enclosingCharacter}
              className="block w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 border-b border-gray-200 transition-colors duration-150 no-underline"
            >
              CSV
            </CSVLink>
            <button
              onClick={handleExportPDF}
              className="w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
            >
              PDF
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
