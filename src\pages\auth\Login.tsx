import React from 'react';
import { LoginForm } from '@/features/auth/components/LoginForm';
import logo from '@/assets/images/グループ 3.svg';

export const Login: React.FC = () => {
  const handleOnlineServiceClick = () => {
    // Handle online service navigation
  };

  return (
    <div className="bg-white flex flex-col overflow-hidden items-stretch pb-32 px-4 pt-0 md:pt-10 lg:pt-8 xl:pt-0">
      <section className="self-center mt-1.5 w-full max-w-4xl max-md:max-w-full">
        <div className="flex justify-center pb-2 md:pb-4">
          <img 
            src={logo}
            alt="ChoQi Logo" 
            className="h-12 md:h-20 lg:h-full object-contain"
          />
        </div>
        <p className="text-[rgba(25,164,146,1)] text-sm md:text-lg font-normal max-md:max-w-full text-center md:text-left px-2 md:px-4">
          <span className="text-[rgba(112,112,112,1)]">
            チョキペイは決済端末を利用したキャッシュレスサービスです。チョキペイonlineをご利用の方は
          </span>
          <button
            onClick={handleOnlineServiceClick}
            className="text-[rgba(25,164,146,1)] underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-[rgba(25,164,146,1)] focus:ring-offset-1 rounded ml-1"
            aria-label="チョキペイonlineサービスへ移動"
          >
            こちら
          </button>
        </p>
      </section>

      <LoginForm />
    </div>
  );
};
