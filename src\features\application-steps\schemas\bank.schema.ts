import { z } from "zod";
import { validate<PERSON><PERSON>row<PERSON><PERSON>, validateNarrowKanaNumericAndUpperCase, validatePattern, validateWideString } from "../utils/validate";

// Schema validation cho form bank
export const bankSchema = z.object({
    agxBankNo: z.string().optional(),
    agxBankName: z.string().optional(),
    agxBankPhoneticName: z.string().optional(),
    agxAccountType: z.string().optional(),
    agxBranchNo: z.string().optional(),
    agxBranchName: z.string().optional(),
    agxBranchPhoneticName: z.string().optional(),
    agxAccountNo: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .min(7, "※7文字で入力してください。")
        .refine((value) => {
            return validatePattern(value, /^\d*$/);
        }, {
            message: "※半角数字で入力してください。"
        }),
    agxAccountHolder: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        }).refine((value) => {
            return validateNarrowKanaNumericAndUpperCase(value);
        }, {
            message: "※半角ｶﾅ、英数字、大文字で入力してください。"
        }),
}).refine((data) => {
    const allThreeFilled = (
        data.agxBankNo && data.agxBankNo.trim() !== "" &&
        data.agxBankName && data.agxBankName.trim() !== "" &&
        data.agxBankPhoneticName && data.agxBankPhoneticName.trim() !== ""
    );
    return allThreeFilled;
}, {
    path: ["agxBankNo"],
    message: "※必須項目です、値を入力してください。"
})
// Validate bank
.refine((data) => {
    return validatePattern(data.agxBankNo.trim(), /^\d*$/);
}, {
    path: ["agxBankNo"],
    message: "※半角英数字で入力してください。"
})
.refine((data) => {
   return validateWideString(data.agxBankName.trim());
}, {
    path: ["agxBankNo"],
    message: "※全角文字で入力してください。"
})
.refine((data) => {
    return validateNarrowKana(data.agxBankPhoneticName.trim());
}, {
    path: ["agxBankNo"],
    message: "※半角カナで入力してください。"
})
// Validate branch
.refine((data) => {
    const allThreeFilled = (
        data.agxBranchNo && data.agxBranchNo.trim() !== "" &&
        data.agxBranchName && data.agxBranchName.trim() !== "" &&
        data.agxBranchPhoneticName && data.agxBranchPhoneticName.trim() !== ""
    );
    return allThreeFilled;
}, {
    path: ["agxBranchNo"],
    message: "※必須項目です、値を入力してください。"
})
.refine((data) => {
    return validatePattern(data.agxBranchNo.trim(), /^\d*$/);
}, {
    path: ["agxBranchNo"],
    message: "※半角数字で入力してください。"
})
.refine((data) => {
    return validateWideString(data.agxBranchName.trim());
}, {
    path: ["agxBranchNo"],
    message: "※全角文字で入力してください。"
})
.refine((data) => {
    return validateNarrowKana(data.agxBranchPhoneticName.trim());
}, {
    path: ["agxBranchNo"],
    message: "※半角カナで入力してください。"
})

export type BankFormData = z.infer<typeof bankSchema>; 