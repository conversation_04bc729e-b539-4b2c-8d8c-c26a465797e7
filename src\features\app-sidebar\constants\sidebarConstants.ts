import { TypeStore } from '@/types/globalType';
import logoSvg from '@/assets/images/グループ 1318.svg';
import accountIcon from '@/assets/images/Icon material-account-circle.svg';
import receiptIcon from '@/assets/images/Icon payment-invoice-sign-alt-o.svg';
import terminalIcon from '@/assets/images/Icon smartphone.svg';
import logoutIcon from '@/assets/images/グループ 1344.svg';
import homeIcon from '@/assets/images/Icon home.svg';
import helpIcon from '@/assets/images/Icon open-question-mark.svg';
import checkMarkIcon from '@/assets/images/Icon check-mark.svg';
import settingsIcon from '@/assets/images/Icon settings.svg';
import listIcon from '@/assets/images/Icon list.svg';

export const SIDEBAR_ICONS = {
  logo: logoSvg,
  account: accountIcon,
  receipt: receiptIcon,
  terminal: terminalIcon,
  logout: logoutIcon,
  home: homeIcon,
  help: helpIcon,
  checkMark: checkMarkIcon,
  settings: settingsIcon,
  list: listIcon,
} as const;

export interface SubMenuItem {
  title: string;
  url: string;
  isShowModal?: boolean;
}

export interface MenuItem {
  title: string;
  url: string;
  icon: string;
  subItems?: SubMenuItem[];
  isShowModal?: boolean;
}

export const getStoreDepositUrl = (typeStore: TypeStore): string => {
  switch (typeStore) {
    case TypeStore.STORE_CREPICO:
      return "/store/deposit/crepico";
    case TypeStore.STORE_PAYGATE:
      return "/store/deposit/paygate";
    case TypeStore.STORE_MIGRATE:
      return "/store/deposit";
    default:
      return "/store/deposit/crepico";
  }
};

export const getStoreNotificationRollpaperUrl = (typeStore: TypeStore): string => {
  switch (typeStore) {
    case TypeStore.STORE_CREPICO:
      return "https://www.seiko-sol.co.jp/products/crepico/crepico_user/user_order/";
    case TypeStore.STORE_PAYGATE:
      return "https://help-paygate.smaregi.jp/hc/";
    case TypeStore.STORE_MIGRATE:
      return "/store/notification/rollpaper";
    default:
      return "/https://www.seiko-sol.co.jp/products/crepico/crepico_user/user_order/";
  }
};

export const getStoreInvoiceReceiptUrl = (typeStore: TypeStore): string => {
  switch (typeStore) {
    case TypeStore.STORE_CREPICO:
      return "/store/invoice-receipt/crepico";
    case TypeStore.STORE_PAYGATE:
      return "/store/invoice-receipt/paygate";
    default:
      return "/store/invoice-receipt";
  }
};

export const getStoreSupportUrl = (typeStore: TypeStore): string => {
  switch (typeStore) {
    case TypeStore.STORE_CREPICO:
      return "https://www.seiko-sol.co.jp/products/crepico/crepico_user/";
    case TypeStore.STORE_PAYGATE:
      return "https://help-paygate.smaregi.jp/hc/";
    case TypeStore.STORE_MIGRATE:
      return "/store/support";
    default:
      return "https://www.seiko-sol.co.jp/products/crepico/crepico_user/";
  }
};

export const getStoreAgreementUrl = (typeStore: TypeStore): string => {
  switch (typeStore) {
    case TypeStore.STORE_CREPICO:
      return "/store/agreement/crepico";
    case TypeStore.STORE_PAYGATE:
      return "/store/agreement/paygate";
    case TypeStore.STORE_MIGRATE:
      return "/store/agreement";
    default:
      return "/store/agreement";
  }
};

export const getStoreNotificationFaqUrl = (typeStore: TypeStore): string => {
  switch (typeStore) {
    case TypeStore.STORE_PAYGATE:
      return "https://choqi.co.jp/choqipay/faq/faq_list2.html?_gl=1*1im6h43*_ga*MTc3MTcwMzI0NC4xNjk0NDE2MTQz*_ga_CKW2PPLYTQ*MTcwNTk3Nzc1My4xOTkuMS4xNzA1OTc5NDMzLjAuMC4w*_ga_6SPDYLRMQY*MTcwNTk3Nzc1My4zNDcuMS4xNzA1OTc5NDMzLjAuMC4w*_ga_E11ER9VJEZ*MTcwNTk3Nzc1My4zNDcuMS4xNzA1OTc5NDMzLjAuMC4w*_ga_SK2YTSMLQC*MTcwNTk3Nzc1My42MjcuMS4xNzA1OTc5NDMzLjYwLjAuMA..";
    case TypeStore.STORE_CREPICO:
      return "https://choqi.co.jp/choqipay/faq/faq_list12.html";
    case TypeStore.STORE_MIGRATE:
      return "/store/notification/faq";
    default:
      return "https://choqi.co.jp/choqipay/faq/faq_list12.html";
  }
};


export const getStoreMenuItems = (typeStore: TypeStore): MenuItem[] => [
  {
    title: "ホーム",
    url: "/overview",
    icon: SIDEBAR_ICONS.home,
  },
  {
    title: "振込データ",
    url: "",
    icon: SIDEBAR_ICONS.checkMark,
    subItems: [
      { title: "振込一覧", url: getStoreDepositUrl(typeStore) },
      { title: "振込カレンダー", url: "/store/notification/calendar" },
    ]
  },
  {
    title: "領収書・請求書",
    url: getStoreInvoiceReceiptUrl(typeStore),
    icon: SIDEBAR_ICONS.receipt,
  },
  {
    title: "加盟店情報",
    url: "/store/config",
    icon: SIDEBAR_ICONS.settings,
  },
  {
    title: "決済端末",
    url: "",
    icon: SIDEBAR_ICONS.terminal,
    subItems: typeStore === TypeStore.STORE_CREPICO ? [
      { title : "決済データ", url: "/store/crepico-payment" },
      { title: "マニュアル", url: "/#" },
      {
        title: "ロール紙の購入",
        url: "https://www.seiko-sol.co.jp/products/crepico/crepico_user/user_order/"
      }
    ] : typeStore === TypeStore.STORE_MIGRATE ? [
      { title: "決済データ", url: "/store/crepico-payment" },
      { title: "端末管理画面", url: "https://member.paygate.ne.jp/login" },
      { title: "初期設定", url: "/store/notification/paygate" },
      { title: "ロール紙の購入", url: getStoreNotificationRollpaperUrl(typeStore) }
    ] : [
      { title: "端末管理画面", url: "https://member.paygate.ne.jp/login" },
      { title: "初期設定", url: "/store/notification/paygate" },
      { title: "ロール紙の購入", url: getStoreNotificationRollpaperUrl(typeStore) }
    ]
  },
  {
    title: "ヘルプ",
    url: "",
    icon: SIDEBAR_ICONS.help,
    subItems: [
      { title: "よくある質問", url: getStoreNotificationFaqUrl(typeStore) },
      {
        title: "端末サポートサイト",
        url: getStoreSupportUrl(typeStore),
        isShowModal: typeStore === TypeStore.STORE_CREPICO
      },
      { title: "問い合わせ", url: "/contact" },
      { title: "加盟店規約", url: getStoreAgreementUrl(typeStore) }
    ]
  },
  {
    title: "アカウント設定",
    url: "/account",
    icon: SIDEBAR_ICONS.account,
  },
];

export const getAdminStoreMenuItems = (): MenuItem[] => [
  {
    title: "ホーム",
    url: "/overview",
    icon: SIDEBAR_ICONS.home,
  },
  {
    title: "振込データ",
    url: "",
    icon: SIDEBAR_ICONS.checkMark,
    subItems: [
      { title: "振込一覧", url: "/admin-store/deposit" },
      { title: "振込カレンダー", url: "/admin-store/notification/calendar" },
    ]
  },
  {
    title: "領収書・請求書",
    url: "/admin-store/invoice-receipt",
    icon: SIDEBAR_ICONS.receipt,
  },
  {
    title: "エリア・店舗登録",
    url: "/admin-store/config",
    icon: SIDEBAR_ICONS.settings,
  },
  {
    title: "決済端末",
    url: "",
    icon: SIDEBAR_ICONS.terminal,
    subItems: [
      { title: "決済データ", url: "/admin-store/crepico-payment" },
      { title: "端末管理画面", url: "https://member.paygate.ne.jp/login" },
      { title: "初期設定", url: "/admin-store/notification/paygate" },
      { title: "ロール紙の購入", url: "https://help-paygate.smaregi.jp/hc" },
      { title: "加盟店規約", url: "/admin-store/agreement" }
    ]
  },
  {
    title: "ヘルプ",
    url: "",
    icon: SIDEBAR_ICONS.help,
    subItems: [
      { title: "よくある質問", url: "https://choqi.co.jp/choqipay/faq/faq_list12.html" },
      { title: "問い合わせ", url: "/contact" }
    ]
  },
  {
    title: "アカウント設定",
    url: "/account",
    icon: SIDEBAR_ICONS.account,
  },
];

export const SIDEBAR_STYLES = {
  sidebar: {
    height: 'calc(100vh - 80px)',
    backgroundColor: '#ffffff !important',
    '--sidebar-background': '#ffffff',
    '--sidebar-background-mobile': '#ffffff'
  } as React.CSSProperties,
  header: 'p-4 pt-7 pb-1 pl-[25px]',
  logo: 'h-12 object-contain',
  menuActive: 'bg-gray-100 text-black',
  menuInactive: 'text-[#6F6F6E] hover:bg-gray-50',
  subMenuActive: 'bg-gray-200 text-black',
  subMenuInactive: 'text-[#6F6F6E] hover:bg-gray-50',
} as const; 