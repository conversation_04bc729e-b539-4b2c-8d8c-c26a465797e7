import { useQuery } from '@tanstack/react-query';
import { paygateService } from '@/features/store/notification/services/paygateService';
import { PaygateState } from '@/features/store/notification/types/paygateType';
import { useAuthStore } from '@/features/auth/slices/authStore';

export const usePaygateData = () => {
  const { user } = useAuthStore();

  // Fetch paygate data using React Query
  const {
    data: response,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['paygate-data', user?.agxMerchantNo],
    queryFn: () => {
      if (!user?.agxMerchantNo) {
        throw new Error('Merchant number not found');
      }
      return paygateService.getData(user.agxMerchantNo);
    },
    enabled: !!user?.agxMerchantNo,
  });

  // Transform the data to match the expected PaygateState interface
  const paygateData: PaygateState = {
    id: response?.data?.id || "",
    password: response?.data?.password || "",
    appSerialNumber: response?.data?.appSerialNumber || "",
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'データの取得に失敗しました') : null
  };

  return {
    paygateData,
    refetch
  };
};
