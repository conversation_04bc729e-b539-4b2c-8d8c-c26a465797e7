import React from 'react';
import { Download } from "lucide-react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import ChoqiHealthHacking from '@/assets/images/choqi-health-hacking.png';
import { formatDateJapan, formatMoney, formatNumber } from '@/utils/dateUtils';
import Secretkey from '@/assets/images/secretkey.png';
import { InvoiceDetailResponse } from '@/features/store/invoice-receipt/services/invoiceService';
import { axiosPDF } from '@/features/store/invoice-receipt/utils';
import PDFService, { blobToBase64, PDFConfig } from '@/services/pdfService';
import { PDF_IMG_LOGO, PDF_IMG_STAMP } from '@/features/store/invoice-receipt/utils/image-base64';
import { useGetDataInvoiceMonthly } from '@/features/adminStore/invoice-receipt/hooks/useGetDataInvoiceMonthly';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const InvoiceMonthly = () => {
  const { user } = useAuthStore();
  const { yearMonth } = useParams<{ yearMonth: string }>();
  const switchLayoutDate = "2023-11-05";

  // Use the hook to get data
  const { data, isLoading } = useGetDataInvoiceMonthly(user.agxMerchantNo, yearMonth);

  const invoiceNo = `${user.agxMerchantNo}-${yearMonth}`;

  // Handle loading state
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Get PDF table data
  const getInvoiceTableData = () => {
    const tableData: any[][] = [];

    // Add item rows if data exists
    if (data?.dataInvoices) {
        data.dataInvoices.forEach(item => {
            tableData.push([
                { text: item.name, alignment: 'left' },
                { text: formatNumber(item.quantity), alignment: 'right' },
                { text: formatMoney(item.unitPrice), alignment: 'right' },
                { text: formatNumber(item.amount), alignment: 'right' },
            ]);
        });
    }

    return tableData;
  };

  const getRecordDetailPDF = () => {
      const result = [[{
          text: '品目',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '数量',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '単価',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '金額',
          style: 'tableHeader',
          alignment: 'center'
      }]];
      for (const index in data?.dataInvoices) {
          const cols = [];
          cols.push({ text: data?.dataInvoices[index].name, alignment: 'left' },
              { text: formatNumber(data?.dataInvoices[index].quantity), alignment: 'right' },
              { text: formatMoney(data?.dataInvoices[index].unitPrice), alignment: 'right' },
              { text: formatNumber(data?.dataInvoices[index].amount), alignment: 'right' },
          );
          result.push(cols);
      }
      //Table detail footer
      result.push([
          {
              text: '\n',
              border: [false, false, false, false],
          } as any,
          {
              text: '小計',
              border: [true, true, false, false],
          } as any,
          {
              text: '\n',
              border: [false, true, false, false],
          } as any,
          {
              text: formatNumber(data?.subTotal),
              alignment: 'right'
          } as any,
      ]);
      result.push([
          {
              text: '\n',
              border: [false, false, false, false],
          } as any,
          {
              text: '消費税(10%) ',
              border: [true, true, false, false],
          } as any,
          {
              text: '\n',
              border: [false, true, false, false],
          } as any,
          {
              text: formatNumber(data?.tax),
              alignment: 'right'
          } as any,
      ]);
      result.push([
          {
              text: '\n',
              border: [false, false, false, false],
          } as any,
          {
              text: '合計',
              bold: true,
              border: [true, true, false, true],
          } as any,
          {
              text: '\n',
              border: [false, true, false, true],
          } as any,
          {
              text: formatNumber(data?.total),
              bold: true,
              alignment: 'right'
          } as any,
      ]);
      return result;
  }

  const getRecordPaymentPDF = () => {
    const result = [[
        { text: '加盟店番号', alignment: 'left' },
        { text: '店舗名', alignment: 'left' },
        { text: '月額費用', alignment: 'right' },
        { text: '（消費税）', alignment: 'right' },
        { text: 'ご請求金額', alignment: 'right' }
    ]];

    for (const index in data?.merchantPayments) {
        const cols = [];
        const alignment = 'right';
        cols.push({ text: data?.merchantPayments[index].merchantNo, alignment: 'left' },
            { text: data?.merchantPayments[index].storeName, alignment: 'left' },
            { text: formatNumber(data?.merchantPayments[index].salesAmount), alignment: alignment },
            { text: formatNumber(data?.merchantPayments[index].sumTax), alignment: alignment },
            { text: formatNumber(data?.merchantPayments[index].paymentAmount), alignment: alignment }
        );
        result.push(cols);
    }
    return result;
}

  // Handle PDF export
  const handleExportPDF = async () => {
    try {
        // Check if data exists first
        if (!data) {
            alert('データが見つかりません。');
            return;
        }

        // Setup fonts first and wait for them to be ready
        const responseYugothib = await axiosPDF.get('yugothib.ttf');
        if (responseYugothib.status !== 200) {
            alert('フォントの読み込みに失敗しました。');
            return;
        }
        const fontYugothib = await blobToBase64(responseYugothib.data);

        const responseArial = await axiosPDF.get('arial.ttf');
        if (responseArial.status !== 200) {
            alert('フォントの読み込みに失敗しました。');
            return;
        }
        const fontArial = await blobToBase64(responseArial.data);

        // Setup fonts and wait for completion
        await PDFService.setupMultipleFonts(fontYugothib, fontArial);

        // Create main table with items
        const tableData = getInvoiceTableData();

        // Add company info to the right column
        const companyInfo = [
            { text: '〒532-0003', margin: [0, 0, 0, 5], fontSize: 12 },
            { text: '大阪府大阪市淀川区宮原1-6-1', margin: [0, 0, 0, 5], fontSize: 12 },
            { text: '新大阪ブリックビル', margin: [0, 0, 0, 10], fontSize: 12 },
            { text: 'TEL:06-6397-5210', margin: [0, 0, 0, 5], fontSize: 12 },
            { text: 'FAX:06-6397-5211', margin: [0, 0, 0, 5], fontSize: 12 },
            { text: '<EMAIL>', margin: [0, 0, 0, 5], fontSize: 12 }
        ];

        // Add qualified business registration number for newer invoices
        if (data?.invoiceCreatedDate && data?.invoiceCreatedDate > switchLayoutDate) {
            companyInfo.unshift(
                { text: '適格事業者登録番号', margin: [0, 0, 0, 5], fontSize: 12 },
                { text: '(T6120001228218)', margin: [0, 0, 0, 5], fontSize: 12 }
            );
        }

        const details = getRecordDetailPDF();
        const payment = getRecordPaymentPDF();

        const config: PDFConfig = {
            pageSize: { width: 640, height: 900 } as any,
            pageOrientation: 'portrait',
            defaultStyle: {
                font: 'yugothib'
            },
            background: function (page) {
                if (page === 1) {
                    return [
                        {
                            image: PDF_IMG_STAMP,
                            width: 60,
                            margin: [395, 245, 0, 0],
                        }
                    ];
                }
            },
            content: [
                { text: data?.invoiceCreatedDate ? formatDateJapan(data?.invoiceCreatedDate) : '', alignment: 'right', margin: [0, 20, 20, 5] },
                { text: `請求書番号: ${invoiceNo}`, alignment: 'right', margin: [0, 0, 20, 5] },
                { text: '請求書', fontSize: 24, alignment: 'center', style: 'header', margin: [0, 0, 0, 20] },
                { text: `${user.agxStoreName} 御中`, alignment: 'left', margin: [20, 0, 0, 5] },
                {
                    margin: [20, 20, 0, 0],
                    columns: [
                        {
                            width: '*',
                            stack: [
                                { text: '下記のとおりご請求申し上げます。', margin: [0, 0, 0, 20], fontSize: 10 },
                                {
                                    table: {
                                        widths: [100, 100],
                                        heights: [15],
                                        body: [
                                            [
                                                {
                                                    border: [false, false, false, false],
                                                    fillColor: '#f3f3f3',
                                                    text: 'ご請求金額',
                                                    alignment: 'left',
                                                    margin: [0, 2, 0, 0]
                                                },
                                                {
                                                    border: [false, false, false, false],
                                                    fillColor: '#f3f3f3',
                                                    text: formatMoney(data?.total),
                                                    margin: [40, 2, 0, 0]
                                                }
                                            ],
                                        ]
                                    },
                                    layout: {
                                        fillColor: function (rowIndex, node, columnIndex) {
                                            return (rowIndex % 2 === 1) ? '#f3f3f3' : null;
                                        }
                                    }
                                },
                                { text: `お支払期限：${data?.invoiceDeadlineDate ? formatDateJapan(data?.invoiceDeadlineDate) : ''}`, margin: [0, 10, 0, 5], fontSize: 10 },
                            ]
                        },
                        {
                            width: '*',
                            stack: [
                                {
                                    image: PDF_IMG_LOGO,
                                    width: 120,
                                    margin: [0, 0, 0, 30],
                                },
                                { text: 'チョキ株式会社', margin: [0, 10, 0, 5], fontSize: 12 },
                                
                            ]
                        }
                    ]
                },
                {
                    style: 'tableMargin',
                    table: {
                        headerRows: 1,
                        widths: [220, 70, 95, '*'],
                        body: details
                    },
                    layout: {
                        fillColor: function (rowIndex, node, columnIndex) {
                            return (rowIndex === 0) ? '#757575' : null;
                        }
                    }
                },
                {
                    text: '振込先',
                    margin: [20, 0, 0, 5]
                },
                {
                    margin: [20, 0, 20, 20],
                    table: {
                        widths: ['*'],
                        heights: [60],
                        body: [
                            [
                                {
                                    border: [false, false, false, false],
                                    text: '\n三菱UFJ銀行 新大阪支店 普通預金 0317169\n口座名義：チヨキ（カ',
                                    fillColor: '#eeeeee',
                                },
                            ],
                        ]
                    }
                },
                {
                    text: '備考',
                    margin: [20, 0, 0, 5]
                },
                {
                    margin: [20, 0, 20, 0],
                    table: {
                        widths: ['*'],
                        heights: [60],
                        body: [
                            [
                                {
                                    border: [false, false, false, false],
                                    text: '\n※恐れ入りますが、振込手数料はご負担ください。\n※チョキペイにご登録いただいた口座と異なる名義の口座からご入金手続きをされる場合は、備考欄に、本請求書右上に記載の「請求書番号」をご入力のうえ、お振り込みください。\n※既にクレジットカードを登録されている場合、事務手数料はかかりません。\n※クレジットカード未登録の場合は、当月15日までに登録いただきますと、事務手数料はかかりません \n※同一法人で複数店舗でチョキペイをご利用の場合は、まとめてお振込いただくことが可能です。まとめてお振込いただく場合、店舗数に関わらず事務手数料は550円（税込）のみとなります',
                                    fillColor: '#eeeeee',
                                },
                            ],
                        ]
                    }
                },
                {
                    margin: [20, 20, 0, 20],
                    table: {
                        widths: [110, 180, 'auto', 'auto', 'auto'],
                        body: payment
                    },
                    layout: 'lightHorizontalLines',
                    style: 'table'
                }
            ],
            styles: {
                header: {
                    fontSize: 18,
                    bold: true,
                    margin: [0, 0, 0, 10]
                },
                tableMargin: {
                    margin: [20, 10, 20, 15]
                },
                tableHeader: {
                    bold: true,
                    fontSize: 13,
                    color: 'white'
                }
            },
            filename: `invoice_multi_store.pdf`
        };

        // Create PDF after fonts are properly loaded
        await PDFService.createPDFWithSetupFonts(config);
        
    } catch (error) {
        console.error('PDF作成エラー:', error);
        alert('PDF作成に失敗しました。');
    }
  };

  return (
    <div className="p-4 md:py-6 md:px-1 lg:p-6">
      <div className="page-heading">
        <div className="flex">
          <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">請求書</h2>
          <button id="download-pdf" className="p-2 pl-4 text-[#1D9987] hover:text-[#1D9987]/80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" onClick={handleExportPDF}>
            <Download className="h-6 w-6" />
          </button>
        </div>
        <hr className='mt-3 border-1 border-gray-500' />
        <div className="py-4">
            <div className="space-y-2">
                <p id="datetime" className="text-[18px] font-medium text-[#6F6F6E]">{data?.invoiceCreatedDate ? formatDateJapan(data.invoiceCreatedDate) : ''} </p>
                <p id="merchant-no" className="text-[18px] font-medium text-[#6F6F6E]">請求書番号: {invoiceNo}</p>
            </div>
        </div>
      </div>
      <div className="pt-2"><p className="text-[24px] text-[#6F6F6E] font-bold">{user.agxStoreName} 御中</p></div>

      <div className="flex justify-between items-center">
          <div className="w-full"><p className="text-[18px] font-medium text-[#6F6F6E]">下記のとおりご請求申し上げます。</p></div>
          <div className="w-1/4"><img id="image-health" src={ChoqiHealthHacking} width="200" /></div>
      </div>
      <div className="flex justify-between items-start py-4">
        <div className="flex-1">
            <div className="flex w-2/3 justify-between items-center bg-gray-200 p-4 border border-gray-200">
                <p className="text-[26px] font-bold text-[#6F6F6E]">ご請求金額</p>
                <div id="total-fee"><p id="invoice-total" className="text-[26px] font-bold text-[#6F6F6E]">¥ {formatNumber(data?.total || 0)}</p></div>
            </div>
            <div className="pt-4">
                <div id="deadline-date" className="text-[18px] font-medium text-[#6F6F6E]">お支払期限：{formatDateJapan(data?.invoiceDeadlineDate || '')}</div>
            </div>
        </div>
        <div className="flex-1 flex justify-center">
          <div className="pt-6 mb-4 font-medium text-[#6F6F6E] text-left bg-contain bg-no-repeat bg-right-top lg:pl-96" style={{ backgroundImage: `url(${Secretkey})`, backgroundSize: '100px 100px' }}>
            <div id="company-name" className="text-[22px] font-bold mb-2">チョキ株式会社</div>
            {data?.invoiceCreatedDate > switchLayoutDate && (
            <div className="mb-2">
              <div className="text-[16px]">適格事業者登録番号</div>
              <div className="text-[16px]">(T6120001228218)</div>
            </div>
            )}
            <div id="postcode" className="text-[16px]">〒532-0003</div>
            <div id="addr1" className="text-[16px]">大阪府大阪市淀川区宮原1-6-1</div>
            <div id="addr2" className="text-[16px] mb-2">新大阪ブリックビル</div>
            <div id="tell" className="text-[16px]">TEL:06-6397-5210</div>
            <div id="fax" className="text-[16px]">FAX:06-6397-5211</div>
            <div id="mail" className="text-[16px]"><EMAIL></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="row">
          <table className="table w-full border-collapse font-medium">
              <thead>
                  <tr style={{ backgroundColor: '#757575' }}>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4">品目</th>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4 w-20">数量</th>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4 w-24">単価</th>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4 w-24">金額</th>
                  </tr>
              </thead>
              <tbody>
                  {data?.dataInvoices?.length > 0 ? (
                      data.dataInvoices.map((item, index) => (
                          <tr className='border border-[#6F6F6E] text-[16px] text-[#6F6F6E]' key={index}>
                                                             <td className='text-left border border-[#6F6F6E] py-2 px-4'>
                                 <Link 
                                   className='text-[#1D9987] hover:text-[#1D9987]/80' 
                                   to={`/admin-store/admin-invoice-receipt/admin-invoice-monthly/credit-card-monthly-fee/${yearMonth}/${item.name}`}
                                 >
                                   {item.name}
                                 </Link>
                               </td>
                              <td className='text-right border border-[#6F6F6E] py-2 px-4'>{formatNumber(item.quantity || 0)}</td>
                              <td className='text-right border border-[#6F6F6E] py-2 px-4'>{formatNumber(item.unitPrice || 0)}</td>
                              <td className='text-right border border-[#6F6F6E] py-2 px-4'>{formatNumber(item.amount || 0)}</td>
                          </tr>
                      ))
                  ) : (
                      <tr>
                          <td colSpan={4} className='text-[#6F6F6E] text-center border border-[#6F6F6E] py-2 px-4'>
                              請求書に商品がありません。
                          </td>
                      </tr>
                  )}
                  <tr className='border border-[#6F6F6E] text-[16px]'>
                      <td className='text-left border border-[#6F6F6E] py-2 px-4'></td>
                      <td colSpan={2} className='text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4 text-left'>小計</td>
                      <td className='text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4'>
                          {formatNumber(data?.subTotal || 0)}</td>
                  </tr>
                  <tr className='border border-[#6F6F6E] text-[16px]'>
                      <td className='text-left border border-[#6F6F6E] py-2 px-4'></td>
                      <td colSpan={2} className='text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4 text-left'>消費税(10%)</td>
                      <td className='text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4'>
                          {formatNumber(data?.tax || 0)} </td>
                  </tr>
                  <tr className='border border-[#6F6F6E] text-[16px]'>
                      <td className='text-left border border-[#6F6F6E] py-2 px-4'></td>
                      <td colSpan={2} className='text-[#6F6F6E] font-bold border border-[#6F6F6E] py-2 px-4 text-left'>
                          合計</td>
                      <td className='text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4 font-bold'>
                          {formatNumber(data?.total || 0)}</td>
                  </tr>
              </tbody>
          </table>
      </div>

      <div className='py-4'>
        <div className="pt-4">
          <p className="text-[18px] font-medium text-[#6F6F6E]">振込先</p>
        </div>
        <div className="pt-4 bg-gray-200 p-4 border border-gray-200 w-1/2">
            <div className="w-full"><p className="text-[18px] font-medium text-[#6F6F6E]">三菱UFJ銀行 新大阪支店 普通預金 0317169</p></div>
            <div className="w-full"><p className="text-[18px] font-medium text-[#6F6F6E]">口座名義：チヨキ（カ）</p></div>
        </div>
        <div className="pt-4">
          <p className="text-[18px] font-medium text-[#6F6F6E]">備考</p>
        </div>
        <div className="pt-4 bg-gray-200 p-4 border border-gray-200">
          <div className="w-full">
              <p className="text-[18px] font-medium text-[#6F6F6E]">※恐れ入りますが、振込手数料はご負担ください。</p>
              <p className="text-[18px] font-medium text-[#6F6F6E]">※チョキペイにご登録いただいた口座と異なる名義の口座からご入金手続きをされる場合は、備考欄に、</p>
              <p className="text-[18px] font-medium text-[#6F6F6E]">{' '.repeat(4)}本請求書右上に記載の「請求書番号」をご入力のうえ、お振り込みください。</p>
              <p className="text-[18px] font-medium text-[#6F6F6E]">※既にクレジットカードを登録されている場合、事務手数料はかかりません。</p>
              <p className="text-[18px] font-medium text-[#6F6F6E]">※クレジットカード未登録の場合は、当月15日までに登録いただきますと、事務手数料はかかりません。</p>
              <p className="text-[18px] font-medium text-[#6F6F6E]">※同一法人で複数店舗でチョキペイをご利用の場合は、まとめてお振込いただくことが可能です。</p>
              <p className="text-[18px] font-medium text-[#6F6F6E]">{' '.repeat(4)}まとめてお振込いただく場合、店舗数に関わらず事務手数料は550円（税込）のみとなります</p>
          </div>
        </div>
      </div>

            <div className="py-4">
          <div className="rounded-md">
            <Table>
              <TableHeader>
                  <TableRow>
                      <TableHead className='text-[18px] font-medium text-[#6F6F6E]'>加盟店番号</TableHead>
                      <TableHead className='text-[18px] font-medium text-[#6F6F6E]'>店舗名</TableHead>
                      <TableHead className='text-[18px] font-medium text-[#6F6F6E] text-right'>月額費用</TableHead>
                      <TableHead className='text-[18px] font-medium text-[#6F6F6E] text-right'>（消費税）</TableHead>
                      <TableHead className='text-[18px] font-medium text-[#6F6F6E] text-right'>ご請求金額</TableHead>
                  </TableRow>
              </TableHeader>
              <TableBody>
                  {data?.merchantPayments?.map((item, index) => (
                      <TableRow key={index} className='text-[16px] text-[#6F6F6E]'>
                          <TableCell className='font-medium'>{item.merchantNo}</TableCell>
                          <TableCell>
                              {index === (data?.merchantPayments.length - 1) ? (
                                  item.storeName
                              ) : (
                                  <Link 
                                       to={`/admin-store/admin-invoice-receipt/admin-invoice-monthly/monthly-cost-by-store/${yearMonth}/${btoa(item.merchantNo)}`}
                                       className='text-[#1D9987] hover:text-[#1D9987]/80'
                                  >
                                       {item.storeName}
                                  </Link>
                              )}
                          </TableCell>
                          <TableCell className='text-right'>{formatNumber(item.salesAmount)}</TableCell>
                          <TableCell className='text-right'>{formatNumber(item.sumTax)}</TableCell>
                          <TableCell className='text-right'>{formatNumber(item.paymentAmount)}</TableCell>
                      </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
      </div>
    </div>
  );
};

export default InvoiceMonthly;
