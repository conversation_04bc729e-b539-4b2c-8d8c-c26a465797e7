
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { referralCodeSchema, ReferralCodeSchema } from '../../schema';
import { Button } from '@/components/ui/button';
import { IMerchantCoreType } from '../../types';

interface Props {
    data: IMerchantCoreType;
    onNext: () => void;
    onPrev: () => void;
    onUpdate: (data: Partial<IMerchantCoreType>) => void;
}

const EmsEmployeeNo = ({ data, onNext, onPrev, onUpdate }: Props) => {
    const {
        register,
        handleSubmit,
        formState: { errors, isValid },
        watch,
    } = useForm<ReferralCodeSchema>({
        resolver: zodResolver(referralCodeSchema),
        mode: 'onChange',
        defaultValues: {
            agxEmsEmployeeNo: data.agxEmsEmployeeNo || '',
        },
    });

    // Cập nhật parent khi thay đổi
    React.useEffect(() => {
        const subscription = watch((value) => {
            onUpdate({ agxEmsEmployeeNo: value.agxEmsEmployeeNo });
        });
        return () => subscription.unsubscribe();
    }, [watch, onUpdate]);

    const onSubmit = () => {
        onNext();
    };

    return (
        <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-full md:w-[90%] lg:w-[90%] xl:w-[30%] max-w-full flex-col items-center text-[rgba(112,112,112,1)] mt-[33px] py-10 px-8 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
            <div className="text-center mb-8">
                <h2 className="text-[28px] text-[rgba(112,112,112,1)] font-normal mb-2">紹介コードを教えてください</h2>
                <p className="text-[16px] text-[rgba(112,112,112,0.8)] font-normal">お持ちでない場合は空白のまま次に進んでください</p>
            </div>

            <form className="w-full mb-8" onSubmit={handleSubmit(onSubmit)} autoComplete="off">
                <input
                    type="text"
                    placeholder="紹介コード"
                    maxLength={32}
                    {...register('agxEmsEmployeeNo')}
                    className={`w-full h-[50px] px-4 rounded-[8px] border ${errors.agxEmsEmployeeNo ? 'border-red-500' : 'border-[rgba(112,112,112,1)]'} text-[20px] text-[rgba(112,112,112,1)] bg-white focus:outline-none focus:ring-2 focus:ring-[rgba(25,164,146,1)] focus:border-transparent`}
                />
                {errors.agxEmsEmployeeNo && (
                    <div className="text-red-500 text-[14px] mt-1">
                        {errors.agxEmsEmployeeNo.message}
                    </div>
                )}
            </form>

            <div className="flex justify-center gap-4 w-full">
                <Button
                    onClick={onPrev}
                    className="w-[20%] h-[50px] bg-gray-400 text-white rounded-[8px] text-[20px] hover:bg-gray-500 transition-opacity"
                >
                    戻る
                </Button>

                <Button
                    onClick={handleSubmit(onSubmit)}
                    className="w-[20%] h-[50px] bg-[#19A492] text-white rounded-[8px] text-[20px] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#15b19d] transition-opacity"
                >
                    次へ
                </Button>
            </div>
        </section>
    );
};

export default EmsEmployeeNo;
