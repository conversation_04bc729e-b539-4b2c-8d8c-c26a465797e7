import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';
import SummaryPage from '@/features/store/summary/components/SummaryPage';

const SummaryCrepico: React.FC = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user?.memberType ? user?.agxMerchantNo : user?.agxNewMerchantNo || '';
  return (
    <SummaryPage agxMerchantNo={agxMerchantNo} type={STORE.CREPICO} />
  );
};

export default SummaryCrepico;
