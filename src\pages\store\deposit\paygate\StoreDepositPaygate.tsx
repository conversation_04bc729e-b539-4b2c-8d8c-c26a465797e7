import React from 'react';
import { DepositPage } from '@/features/store/deposit/components/DepositPage';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';

const StoreDepositPaygate: React.FC = () => {
  const { user } = useAuthStore();

  // Use actual merchant number from auth store
  const agxMerchantNo = user?.agxMerchantNo;

  if (!agxMerchantNo) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">認証情報が見つかりません。</h2>
      </div>
    );
  }

  return <DepositPage agxMerchantNo={agxMerchantNo} type={STORE.PAYGATE} />;
};

export default StoreDepositPaygate;
