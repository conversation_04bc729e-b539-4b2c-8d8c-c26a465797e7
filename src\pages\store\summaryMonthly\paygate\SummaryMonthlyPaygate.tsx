import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';
import SummaryMonthlyPage from '@/features/store/summary/components/SummaryMonthlyPage';

const SummaryMonthlyPaygate: React.FC = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user?.agxMerchantNo || '';
  return (
      <SummaryMonthlyPage agxMerchantNo={agxMerchantNo} type={STORE.PAYGATE} />
  );
};

export default SummaryMonthlyPaygate;
