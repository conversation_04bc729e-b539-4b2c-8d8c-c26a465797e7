// Shared TypeScript interfaces
export interface ValidateError {
    paymentTime: string;
    saleAmount: string;
}

export interface SortDirection {
    terminalHandlingSerialNumber: boolean | null;
    storeName: boolean | null;
    saleAmount: boolean | null;
    transactionCode: boolean | null;
    terminalIdentificationNumber: boolean | null;
    terminalProcessingDate: boolean | null;
    serviceIdentificationFlag: boolean | null;
}

export interface PaymentData {
    terminalHandlingSerialNumber: string;
    saleAmount: number;
    transactionCode: number;
    terminalProcessingDate: string;
    terminalIdentificationNumber: string;
    serviceIdentificationFlag: string;
    transactionCategory: string;
}

export interface PaymentsResponse {
    data: {
        data: PaymentData[];
        totalElements: number;
        totalPages: number;
    };
    totalYen: number;
    totalItem: number;
}

export interface TerminalNoItem {
    label: string;
    value: string;
}

// Union type for terminal data
export type TerminalNos = string[] | TerminalNoItem[];

// Constants
export const INIT_VALIDATE_ERROR: ValidateError = {
    paymentTime: '',
    saleAmount: ''
};

export const INIT_SORT_DIRECTION: SortDirection = {
    terminalHandlingSerialNumber: null,
    storeName: null,
    saleAmount: null,
    transactionCode: null,
    terminalIdentificationNumber: null,
    terminalProcessingDate: null,
    serviceIdentificationFlag: null,
};

export const CSV_HEADERS = [
    { label: '伝票番号', key: 'terminalHandlingSerialNumber' },
    { label: '売上金額', key: 'saleAmount' },
    { label: '決済状況', key: 'transactionCode' },
    { label: '決済日時', key: 'terminalProcessingDate' },
    { label: '端末識別番号', key: 'terminalIdentificationNumber' },
    { label: '決済種別', key: 'serviceIdentificationFlag' }
];

export const PAGE_SIZE = 50;

// Configuration interface for different implementations
export interface CrepicoPaymentConfig {
    isAdminStore: boolean;
}
