import AgreementCard from '@/components/AgreementCard';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useGetAgreement } from '@/features/agreement';
import { useAuthStore } from '@/features/auth';

const AgreementCrepico = () => {
    const { user } = useAuthStore();

    // TODO: check memberType
    const agxMerchantNo = user?.memberType ? user?.agxMerchantNo : user?.agxNewMerchantNo || '';

    const { data, isLoading } = useGetAgreement(agxMerchantNo);

    const agreementData: { title: string, url: string, isDisplay: boolean }[] = [
        { title: "チョキペイ加盟店利用規約", url: "https://choqi.co.jp/choqipay/rule.pdf", isDisplay: true },
        { title: "MUFGカード加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/mufgcard/shop_01.pdf  ", isDisplay: true },
        { title: "JCB加盟店規約", url: "https://www.jcb.co.jp/kiyaku/pdf/kameiten0705_05.pdf", isDisplay: true },
        { title: "JCB PREMO取扱加盟店特約", url: "https://www.jcb.co.jp/kiyaku/pdf/premo_kameiten.pdf", isDisplay: true },
        { title: "店子加盟店特約（店頭通販共通）", url: "https://www.jcb.co.jp/kiyaku/pdf/tanako_kameiten.pdf", isDisplay: true },
        { title: "銀聯カード加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/ginrei/shop.pdf", isDisplay: true },
        { title: "三菱UFJニコス－交通系電子マネー加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/index_01.pdf", isDisplay: data?.settlementPackage2 },
        { title: "三菱UFJニコス－nanaco電子マネー加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/nanaco.pdf", isDisplay: data?.settlementPackage2 },
        { title: "三菱UFJニコス－WAON加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/waon.pdf", isDisplay: data?.settlementPackage2 },
        { title: "三菱UFJニコス－Edy間接加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/edy.pdf", isDisplay: data?.settlementPackage2 },
        { title: "三菱UFJニコス－ｉＤ加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/id.pdf", isDisplay: data?.settlementPackage2 },
        { title: "QRコード決済規約", url: "https://www.daiwahousefinancial.co.jp/docs/terms/omatome-terms/", isDisplay: true },
        { title: "クレピコ端末設置使用規約（クレジット編）", url: "/pdf/クレピコ端末設置使用規約（クレジット編）.pdf", isDisplay: true },
        { title: "クレピコ端末保守サービス規約", url: "/pdf/クレピコ端末保守サービス規約.pdf", isDisplay: true },
        { title: "クレピコサービス加入規約", url: "/pdf/クレピコサービス加入規約.pdf", isDisplay: true },
    ];

    if (isLoading) return <LoadingSpinner />;

    return <AgreementCard data={agreementData} isDataEmpty={!data} />
}

export default AgreementCrepico;