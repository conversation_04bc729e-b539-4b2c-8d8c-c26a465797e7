import React from 'react';
import { useParams } from 'react-router-dom';
import { DepositDetailPage } from '@/features/store/deposit/components/DepositDetailPage';

const StoreDepositPaygateDetail: React.FC = () => {
  const { merchantNo, paymentBId, transactionType, transferDate } = useParams<{
    merchantNo: string;
    paymentBId: string;
    transactionType: string;
    transferDate: string;
  }>();
  // Decode base64 encoded parameters
  const decodedMerchantNo = merchantNo ? atob(merchantNo) : '';
  const decodedPaymentBId = paymentBId ? atob(paymentBId) : '';
  return <DepositDetailPage merchantNo={decodedMerchantNo} paymentBId={decodedPaymentBId} transactionType={transactionType} transferDate={transferDate} />;
};

export default StoreDepositPaygateDetail;
