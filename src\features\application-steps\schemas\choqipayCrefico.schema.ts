import { z } from "zod";

export const choqipayCreficoSchema = z.object({
  agxNumberOfTerminal: z.preprocess(
    (v) => (v === "" ? undefined : Number(v)),
    z.number({
        required_error: "※入力してください。",
        invalid_type_error: "※数値を入力してください。"
      })
      .min(1,  "※1 ～ 10 の値を入力してください。")
      .max(10, "※1 ～ 10 の値を入力してください。")
  ),
  agxSettlementPackage1: z.boolean().optional(),
  agxSettlementPackage2: z.boolean().optional(),
});

export type ChoqipayCreficoFormData = z.infer<typeof choqipayCreficoSchema>; 