import * as z from "zod";


const regexEmail = new RegExp("^([\\w!#$%&'*+\\-/=?^`{|}~]+(\\.[\\w!#$%&'*+\\-/=?^`{|}~]+)*|\"([\\w!#$%&'*+\\-/=?^`{|}~. ()<>\\[\\]:;@,]|\\\\[\\\\\"])+\")@(([a-zA-Z\\d\\-]+\\.)+[a-zA-Z]+|\\[(\\d{1,3}(\\.\\d{1,3}){3}|IPv6:[\\da-fA-F]{0,4}(:[\\da-fA-F]{0,4}){1,5}(:\\d{1,3}(\\.\\d{1,3}){3}|(:[\\da-fA-F]{0,4}){0,2}))\\])$");

// Password regex pattern matching the old validation logic
const regexPassword = /^(((?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[A-Z])(?=.*[$*!@#&]))|((?=.*[a-z])(?=.*[0-9])(?=.*[$*!@#&]))|((?=.*[A-Z])(?=.*[0-9])(?=.*[$*!@#&])))[a-zA-Z0-9$*!@#&]{8,32}$/;

export const passwordSchema = z
  .object({
    oldPassword: z
      .string()
      .min(1, "※必須項目です、値を入力してください。")
      .min(8, "※古いパスワード８文字以上で指定する必要があります。"),
    newPassword: z
      .string()
      .min(1, "※必須項目です、値を入力してください。")
      .regex(regexPassword, "※パスワードは 8 文字以上で指定する必要があります。 パスワードには、次の 4 種類のうち少なくとも 3 種類の文字が含まれている必要があります: アルファベットの大文字、小文字、数字、および記号（$*!@#&）"),
    confirmPassword: z
      .string()
      .min(1, "※必須項目です、値を入力してください。")
      .min(8, "※パスワードの確認８文字以上で指定する必要があります。"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "※確認パスワードが正しくないので、再入力してください。",
    path: ["confirmPassword"],
  })
  .refine((data) => data.newPassword !== data.oldPassword, {
    message: "※新しいパスワードを前のパスワードと同じにすることはできません。",
    path: ["newPassword"],
  });

export const nameSchema = z.object({
    firstName: z.string().min(1, "名前は必須です"),
    lastName: z.string().min(1, "苗字は必須です"),
  });

export const emailSchema = z
  .object({
    newEmail: z
      .string()
      .min(1, "メールアドレスは必須です")
      .email("有効なメールアドレスを入力してください"),
    confirmEmail: z
      .string()
      .min(1, "メールアドレスは必須です")
      .email("有効なメールアドレスを入力してください")
  })
  .refine((data) => data.newEmail === data.confirmEmail, {
    message: "メールアドレスが一致しません",
    path: ["confirmEmail"],
  });