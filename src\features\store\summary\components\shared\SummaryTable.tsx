import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatNumber } from '@/utils/dateUtils';

interface SummaryTableProps {
  chartLabel: string[];
  lineData: number[];
  creditData: number[];
  electronicData: number[];
  qrData: number[];
  barData?: number[];
  totalData?: number[]; // Optional for SummaryPage
  isMonthly?: boolean; // To distinguish between monthly and daily view
}

const SummaryTable: React.FC<SummaryTableProps> = ({
  chartLabel,
  lineData,
  creditData,
  electronicData,
  qrData,
  barData,
  totalData,
  isMonthly = false
}) => {
  const renderEmptyRow = () => (
    <>
      {Array.from({ length: 12 }, (_, index) => (
        <TableCell key={index} className="text-right"></TableCell>
      ))}
    </>
  );
  const convertDateTable = (date) => {
    if (date != null && date?.length > 7) {
      return date.substring(0, 7);
    } else {
      return date;
    }
  }

  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-0 pt-4 sm:pt-6">
        <div className="overflow-x-auto">
          <Table className="text-xs sm:text-base">
            <TableBody>
              <TableRow>
                <TableCell className="text-xs sm:text-base text-[#6F6F6E]">売上件数</TableCell>
                {lineData.length > 0
                  ? lineData.map((item, index) => (
                    <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                      {formatNumber(item)}
                    </TableCell>
                  ))
                  : renderEmptyRow()
                }
              </TableRow>
              {!isMonthly && (
                <>
                  <TableRow>
                    <TableCell className="text-xs sm:text-base text-[#6F6F6E]">クレジットカード決済</TableCell>
                    {creditData.length > 0
                      ? creditData.map((item, index) => (
                        <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                          {formatNumber(item)}
                        </TableCell>
                      ))
                      : renderEmptyRow()
                    }
                  </TableRow>
                  <TableRow>
                    <TableCell className="text-xs sm:text-base text-[#6F6F6E]">電子マネー決済</TableCell>
                    {electronicData.length > 0
                      ? electronicData.map((item, index) => (
                        <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                          {formatNumber(item)}
                        </TableCell>
                      ))
                      : renderEmptyRow()
                    }
                  </TableRow>
                  <TableRow>
                    <TableCell className="text-xs sm:text-base text-[#6F6F6E]">QRコード決済</TableCell>
                    {qrData.length > 0
                      ? qrData.map((item, index) => (
                        <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                          {formatNumber(item)}
                        </TableCell>
                      ))
                      : renderEmptyRow()
                    }
                  </TableRow>
                </>
              )}
              <TableRow>
                <TableCell className="text-xs sm:text-base text-[#6F6F6E]">売上金額</TableCell>
                {isMonthly ? (
                  barData.length > 0
                    ? barData.map((item, index) => (
                      <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                        {formatNumber(item)}
                      </TableCell>
                    ))
                    : renderEmptyRow()
                ) : (
                  // For daily view, use totalData if available
                  totalData && totalData.length > 0
                    ? totalData.map((item, index) => (
                      <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                        {formatNumber(item)}
                      </TableCell>
                    ))
                    : renderEmptyRow()
                )}
              </TableRow>
              <TableRow>
                <TableCell className="text-xs sm:text-base text-[#6F6F6E]">
                  {isMonthly ? '振込月' : '振込日'}
                </TableCell>
                {chartLabel.length > 0
                  ? !isMonthly
                    ? chartLabel.map((item, index) => (
                      <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                        {item}
                      </TableCell>
                    )) : (
                      chartLabel.map((item, index) => (
                        <TableCell key={index} className="text-right text-xs sm:text-base px-1 sm:px-3 text-[#6F6F6E]">
                          {convertDateTable(item)}
                        </TableCell>
                      ))
                    )
                  : renderEmptyRow()
                }
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default SummaryTable;
