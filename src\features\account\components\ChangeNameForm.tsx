import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { zodResolver } from "@hookform/resolvers/zod";
import { useChangeInfo } from "@/features/account/hooks/useChangeInfo";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useAuthStore } from "@/store";
import { nameSchema } from "../schema";
import { EditMode, NameFormData } from "../types";

// OLD
// interface FormProps {
//   editMode: EditMode;
//   setEditMode: (mode: EditMode) => void;
// }

// function ChangeNameForm({ editMode, setEditMode }: FormProps) {
//   const { user } = useAuthStore();
//   const { changeInfo, isLoading } = useChangeInfo();

//   const nameForm = useForm<NameFormData>({
//     resolver: zodResolver(nameSchema),
//     defaultValues: {
//       firstName: user?.firstName || "",
//       lastName: user?.lastName || "",
//     },
//   });

//   const handleSubmit = nameForm.handleSubmit(async (data) => {
//     await changeInfo({
//       contactId: user?.id,
//       firstName: data.firstName,
//       lastName: data.lastName,
//       email: user?.email,
//     });
//     setEditMode(EditMode.None);
//     nameForm.reset({
//       firstName: data.firstName,
//       lastName: data.lastName,
//     });
//   });

//   return (
//     <Card className="rounded-none border-t-0 border-r-0 border-l-0 shadow-none border-b-2 border-gray-400 text-[#6F6F6E]">
//       <form
//         onSubmit={handleSubmit}
//         className="space-y-4 text-[#6F6F6E] font-[500]"
//       >
//         <CardContent className="px-0">
//           {editMode === EditMode.Name ? (
//             <>
//               <div className="mb-6">
//                 <Label className="md:text-start block text-[20px] font-[500] mb-1">
//                   お名前
//                 </Label>
//                 {/* <div className="text-base md:text-[20px]">
//                   {user?.firstName
//                     ? `${user.firstName} ${user.lastName}`
//                     : "未設定"}
//                 </div> */}
//               </div>
//               <div className="md:flex flex flex-col gap-4 mt-10">
//                 <div className="flex-1 space-y-4">
//                   <div className="md:flex flex-col items-start gap-4 flex-1">
//                     <Label
//                       htmlFor="firstName"
//                       className="w-[277px] font-[500] text-[20px] whitespace-nowrap md:text-left"
//                     >
//                       名
//                     </Label>
//                     <div className="flex-1 w-full">
//                       <Input
//                         id="firstName"
//                         {...nameForm.register("firstName")}
//                         className="rounded-[12px] h-[39px] text-base md:text-[20px] w-full max-w-[422px] border border-[#BABABA] mt-1"
//                       />
//                       {nameForm.formState.errors.firstName && (
//                         <p className="text-red-500 text-sm mt-1">
//                           {nameForm.formState.errors.firstName.message}
//                         </p>
//                       )}
//                     </div>
//                   </div>
//                   <div className="md:flex flex-col items-start gap-4 flex-1">
//                     <Label
//                       htmlFor="lastName"
//                       className="w-[277px] font-[500] text-[20px] whitespace-nowrap md:text-left"
//                     >
//                       姓
//                     </Label>
//                     <div className="flex-1 w-full">
//                       <Input
//                         id="lastName"
//                         {...nameForm.register("lastName")}
//                         className="rounded-[12px] h-[39px] text-base md:text-[20px] w-full max-w-[422px] border border-[#BABABA] mt-1"
//                       />
//                       {nameForm.formState.errors.lastName && (
//                         <p className="text-red-500 text-sm mt-1">
//                           {nameForm.formState.errors.lastName.message}
//                         </p>
//                       )}
//                     </div>
//                   </div>
//                 </div>
//                 <div className="flex justify-center md:justify-end">
//                   <Button
//                     type="submit"
//                     disabled={isLoading}
//                     className="bg-teal-600 flex justify-center items-center hover:bg-teal-700 text-white px-8 w-[120px] md:w-[196px] h-[60px] text-base md:text-[20px] self-end"
//                   >
//                     {isLoading ? "保存中..." : "保存"}
//                   </Button>
//                 </div>
//               </div>
//             </>
//           ) : (
//             <div className="flex items-center justify-between text-[#6F6F6E]">
//               <div>
//                 <Label className="font-[500] text-[#6F6F6E] text-[20px]">
//                   お名前
//                 </Label>
//                 <div className="text-base md:text-[20px]">
//                   {user?.firstName
//                     ? `${user.firstName} ${user.lastName}`
//                     : "未設定"}
//                 </div>
//               </div>
//               <Button
//                 variant="secondary"
//                 onClick={() => setEditMode(EditMode.Name)}
//                 className="bg-[#F7F7F7] hover:bg-[#CCCCCC] text-gray-700 w-[120px] md:w-[196px] h-[60px] text-base md:text-[20px]"
//               >
//                 編集
//               </Button>
//             </div>
//           )}
//         </CardContent>
//       </form>
//     </Card>
//   );
// }

// export default ChangeNameForm;

// NEW
function ChangeNameForm() {
  const { user } = useAuthStore();

  return (
    <Card className="rounded-none border-t-0 border-r-0 border-l-0 shadow-none border-b-2 border-gray-400 text-[#6F6F6E]">
        <CardContent className="px-0">
            <div className="flex items-center justify-between text-[#6F6F6E]">
              <div>
                <Label className="font-[500] text-[#6F6F6E] text-[20px]">
                  お名前
                </Label>
                <div className="text-base md:text-[20px]">
                  {user?.firstName
                    ? `${user.firstName} ${user.lastName}`
                    : "未設定"}
                </div>
              </div>
            </div>
        </CardContent>
    </Card>
  );
}

export default ChangeNameForm;
