import React, { useState, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { useDepositPage } from '@/features/store/deposit/hooks/useDepositPage';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { formatNumber, formatDateJapan } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import { ExportButtons } from './ExportButtons';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { STORE } from '@/types/globalType';
// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Chart configuration constants
const CHART_COLORS = {
  backgroundColor: [
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(54, 162, 235, 0.6)',
  ],
  borderColor: [
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(54, 162, 235, 0.6)',
  ]
};

const CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  layout: {
    padding: {
      top: 20,
      left: 20,
      right: 20
    }
  },
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
      labels: {
        fontColor: '#000',
        fontSize: 10,
        boxWidth: 10,
      },
      fit: 10
    },
    tooltips: {
      enabled: true
    },
  },
  cutout: '50%', // This makes it a doughnut chart
};

// Transaction type classification helpers
const isCredictCardTransaction = (transactionType: number) =>
  transactionType <= 283260004 || (transactionType >= 283260018 && transactionType <= 283260023);

const isElectronicMoneyTransaction = (transactionType: number) =>
  transactionType === 283260024 || (transactionType >= 283260005 && transactionType <= 283260009);

const isQRCodeTransaction = (transactionType: number) =>
  transactionType >= 283260010 && transactionType <= 283260017;

interface DepositPageProps {
  agxMerchantNo: string;
  type: string;
}

export const DepositPage: React.FC<DepositPageProps> = ({ agxMerchantNo, type }) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const agxStoreName = user?.agxStoreName || '';

  const {
    dates,
    depositData,
    transferDate,
    datesLoading,
    depositLoading,
    error,
    dlEnable,
    setTransferDate
  } = useDepositPage(agxMerchantNo);

  const [typeChart, setTypeChart] = useState("/store/deposit/" + type);

  // 11/06の振り込み分から新しいフォーマット
  const switchLayoutDate = "2023-11-05";

  // Calculate chart data using useMemo for better performance
  const chartData = useMemo(() => {
    if (!depositData?.paymentBreakdowns || !depositData?.merchantPayments?.length) {
      return {
        labelsNumberOfSales: [],
        labelsSalesAmount: [],
        percentageNumberOfSales: [],
        percentageSalesAmount: []
      };
    }

    const totalNumberOfSales = depositData.merchantPayments[0].agxNumberOfSales;
    const totalSalesAmount = depositData.merchantPayments[0].agxSalesAmount;

    let creditCardSales = 0;
    let creditCardAmount = 0;
    let electronicMoneySales = 0;
    let electronicMoneyAmount = 0;
    let qrCodeSales = 0;
    let qrCodeAmount = 0;

    depositData.paymentBreakdowns.forEach(element => {
      const transactionType = element.agxTransactionType;

      if (isCredictCardTransaction(transactionType)) {
        creditCardSales += element.agxNumberOfSales;
        creditCardAmount += element.agxSalesAmount;
      } else if (isElectronicMoneyTransaction(transactionType)) {
        electronicMoneySales += element.agxNumberOfSales;
        electronicMoneyAmount += element.agxSalesAmount;
      } else if (isQRCodeTransaction(transactionType)) {
        qrCodeSales += element.agxNumberOfSales;
        qrCodeAmount += element.agxSalesAmount;
      }
    });

    const creditCardSalesPercent = ((creditCardSales / totalNumberOfSales) * 100).toFixed(1);
    const electronicMoneySalesPercent = ((electronicMoneySales / totalNumberOfSales) * 100).toFixed(1);
    const qrCodeSalesPercent = ((qrCodeSales / totalNumberOfSales) * 100).toFixed(1);

    const creditCardAmountPercent = ((creditCardAmount / totalSalesAmount) * 100).toFixed(1);
    const electronicMoneyAmountPercent = ((electronicMoneyAmount / totalSalesAmount) * 100).toFixed(1);
    const qrCodeAmountPercent = ((qrCodeAmount / totalSalesAmount) * 100).toFixed(1);

    return {
      labelsNumberOfSales: [
        `クレジットカード決済[${creditCardSalesPercent}%]`,
        `電子マネー決済[${electronicMoneySalesPercent}%]`,
        `QRコード決済[${qrCodeSalesPercent}%]`
      ],
      labelsSalesAmount: [
        `クレジットカード決済[${creditCardAmountPercent}%]`,
        `電子マネー決済[${electronicMoneyAmountPercent}%]`,
        `QRコード決済[${qrCodeAmountPercent}%]`
      ],
      percentageNumberOfSales: [creditCardSalesPercent, electronicMoneySalesPercent, qrCodeSalesPercent],
      percentageSalesAmount: [creditCardAmountPercent, electronicMoneyAmountPercent, qrCodeAmountPercent]
    };
  }, [depositData]);

  const handleChangeTypeChart = (value: string) => {
    setTypeChart(value);
    navigate(value);
  };

  const handleChangeDate = (value: string) => {
    setTransferDate(value);
  };

  // Computed values for better readability
  const hasDepositData = !!depositData;
  const hasPaymentData = hasDepositData && depositData.merchantPayments?.length > 0;
  const isNewFormat = transferDate > switchLayoutDate;
  const showMonthlyFee = hasPaymentData && depositData.merchantPayments[0].agxInvoiceFlg !== 283260002;
  const showInvoiceFlag = hasPaymentData && depositData.merchantPayments[0].agxInvoiceFlg === 283260000;

  // Helper function to create chart data for Doughnut chart
  const createChartData = (labels: string[], percentages: string[]) => ({
    labels: labels,
    datasets: [
      {
        data: percentages.map(p => parseFloat(p)),
        backgroundColor: CHART_COLORS.backgroundColor,
        borderColor: CHART_COLORS.borderColor,
        borderWidth: 1,
        radius: 150,
      },
    ],
  });
  const createPlugins = (total: number) => [{
    id: 'centerText',
    beforeDraw: function (chart: any) {
      const width = chart.width;
      const height = chart.height;
      const ctx = chart.ctx;
      ctx.restore();
      const fontSize = (height / 220).toFixed(3);
      ctx.font = fontSize + "em sans-serif";
      ctx.textBaseline = "top";
      const text = formatNumber(total);
      const textX = Math.round((width - ctx.measureText(text).width) / 2);
      const textY = (height + 35) / 2;
      ctx.fillText(text, textX, textY);
      ctx.save();
    }
  }];

  if (datesLoading || depositLoading) {
    return (
      <LoadingSpinner />
    )
  }

  if (!hasDepositData) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">振込データがありません。</h2>
      </div>
    )
  }

  return (
    <div className="p-2 mb-16">
      {/* Page Header */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-3xl font-bold text-[#6F6F6E]">振込一覧</h1>
            <Select value={typeChart} onValueChange={handleChangeTypeChart}>
              <SelectTrigger className="w-64 text-[#6F6F6E] text-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem className="text-[#6F6F6E] text-lg" value={`/store/deposit/${type}`}>振込一覧</SelectItem>
                <SelectItem className='text-[#6F6F6E] text-lg' value={`/store/summary/${type}`}>売上金額・件数推移（振込日別）</SelectItem>
                <SelectItem className='text-[#6F6F6E] text-lg' value={`/store/summary-monthly/${type}`}>売上金額・件数推移（振込月別）</SelectItem>
              </SelectContent>
            </Select>
            <ExportButtons
              depositData={depositData}
              transferDate={transferDate}
              agxMerchantNo={agxMerchantNo}
              agxStoreName={agxStoreName}
              type={type}
              dlEnable={dlEnable}
            />
          </div>
        </div>
      </div>
      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error.message || '振込データがありません。'}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6 px-4 text-[#6F6F6E]">
        {dates.length > 0 ? (
          <div className="flex items-center gap-4">
            <label htmlFor="query-date" className="text-lg font-medium">振込日：</label>
            <Select value={transferDate} onValueChange={handleChangeDate}>
              <SelectTrigger className="w-48 text-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dates.map((date, index) => (
                  <SelectItem className="text-[#6F6F6E] text-lg" key={index} value={date}>
                    {formatDateJapan(date)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ) : null}
        {depositData && (
          <>
            {/* Store Info and Summary Table */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 text-[#6F6F6E]">
              <div className="lg:col-span-2">
                <div className="mb-4">
                  <div className="text-lg text-[#6F6F6E]">店舗名: {agxStoreName}</div>
                  <div className="text-lg text-[#6F6F6E]">チョキペイ加盟店番号: {agxMerchantNo}</div>
                </div>

                <div className="overflow-x-auto text-lg">
                  <div className='flex flex-col'>
                    {/* Header row */}
                    <div className='flex flex-row gap-4 xl:gap-8'>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        売上件数
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        売上金額
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        手数料額
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        （内消費税）
                      </div>
                      {showMonthlyFee && (
                        <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                          月額費用
                        </div>
                      )}
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        {type === STORE.PAYGATE ? '振込金額' : '振込額'}
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        ※
                      </div>
                    </div>
                    {/* Data row */}
                    <div className='flex flex-row gap-4 xl:gap-8'>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        <Link
                          to={`/store/deposit/${type}/detail/${transferDate}`}
                          className="text-[#1D9987] hover:text-[#1D9987]/80"
                        >
                          {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxNumberOfSales !== null && depositData.merchantPayments[0].agxNumberOfSales !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxNumberOfSales)}件` : ''}
                        </Link>
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxSalesAmount !== null && depositData.merchantPayments[0].agxSalesAmount !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxSalesAmount)}円` : ''}
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxTotalFee !== null && depositData.merchantPayments[0].agxTotalFee !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxTotalFee)}円` : ''}
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] ? `(${formatNumber((depositData.merchantPayments[0].agxSettlementCompanyTax || 0) + (depositData.merchantPayments[0].agxInHouseTax || 0))})円` : ''}
                      </div>
                      {showMonthlyFee && (
                        <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                          {depositData.total !== null && depositData.total !== undefined ? `${formatNumber(depositData.total)}円` : ''}
                        </div>
                      )}
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxPaymentAmount !== null && depositData.merchantPayments[0].agxPaymentAmount !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxPaymentAmount)}円` : ''}
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {showInvoiceFlag ? '★' : ''}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 text-[#6F6F6E] text-sm">
                  <div>※毎月初の振込は、売上金額より月額費用を差し引いております。売上金額が月額費用未満であった場合、</div>
                  <div>「★」でお知らせします（別途月額費用を、弊社宛にお支払いする必要があります）。</div>
                </div>
              </div>

              {/* Bank Info */}
              <div>
                {isNewFormat && (
                  <div className="mb-4">
                    <div className="text-[#6F6F6E] text-lg">適格事業者登録番号</div>
                    <div className="text-[#6F6F6E] text-lg">(T6120001228218)</div>
                  </div>
                )}

                <div className="font-semibold mb-2 text-[#6F6F6E] text-lg">振込先金融機関</div>
                <div className="text-lg space-y-1">
                  <div>金融機関名: {depositData.merchantPayments?.[0]?.agxBankName || ''}</div>
                  <div>支店名: {depositData.merchantPayments?.[0]?.agxBranchName || ''}</div>
                  <div>口座種別: {depositData.merchantPayments?.[0]?.agxAcccountType || ''}</div>
                  <div>口座番号: {depositData.merchantPayments?.[0]?.agxAccountNo || ''}</div>
                  <div>名義人: {depositData.merchantPayments?.[0]?.agxAccountHolder || ''}</div>
                </div>
              </div>
            </div>



            {/* Breakdown Table */}
            <div className="overflow-x-auto py-4">
              <div className='flex flex-col'>
                {/* Header row */}
                <div className='flex flex-row gap-4 xl:gap-8'>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    決済種別
                  </div>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    売上件数
                  </div>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    売上金額
                  </div>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    手数料率
                  </div>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    手数料額
                  </div>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    （内消費税）
                  </div>
                  <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                    {type === STORE.PAYGATE ? '振込金額' : '振込額'}
                  </div>
                </div>
                {/* Data row */}
                {depositData.paymentBreakdowns?.map((item, index) => (
                  <div key={index} className='flex flex-row gap-4 xl:gap-8'>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      <Link
                        to={`/store/deposit/${type}/detail/${btoa(agxMerchantNo)}/${btoa(item.agxPaymentBreakdownId)}/${item.agxTransactionType}/${transferDate}`}
                        className="text-[#1D9987] hover:text-[#1D9987]/80"
                      >
                        {`${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`}
                      </Link>
                    </div>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      {item.agxNumberOfSales !== null && item.agxNumberOfSales !== undefined ? `${formatNumber(item.agxNumberOfSales)}件` : ''}
                    </div>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      {item.agxSalesAmount !== null && item.agxSalesAmount !== undefined ? `${formatNumber(item.agxSalesAmount)}円` : ''}
                    </div>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      {item.agxTotalFeeRate !== null && item.agxTotalFeeRate !== undefined ? `${Number(item.agxTotalFeeRate).toFixed(2)}%` : ''}
                    </div>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      {item.agxTotalFee !== null && item.agxTotalFee !== undefined ? `${formatNumber(item.agxTotalFee)}円` : ''}
                    </div>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      {item.tax !== null && item.tax !== undefined ? `(${formatNumber(item.tax)})円` : ''}
                    </div>
                    <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                      {item.agxPaymentAmount !== null && item.agxPaymentAmount !== undefined ? `${formatNumber(item.agxPaymentAmount)}円` : ''}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* Tax Summary Table (for new format) */}
            {isNewFormat && (
              <div className="flex justify-end text-[#6F6F6E]">
                <div className="w-80">
                  <Table className="w-full">
                    <TableBody>
                      <TableRow className="border-b border-gray-100">
                        <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">非課税小計</TableCell>
                        <TableCell className="px-4 py-3 text-right text-[#6F6F6E] text-lg">{depositData.subTotalNonTax !== null && depositData.subTotalNonTax !== undefined ? formatNumber(depositData.subTotalNonTax) : ''}</TableCell>
                      </TableRow>
                      <TableRow className="border-b border-gray-100">
                        <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">10%小計（税込）</TableCell>
                        <TableCell className="px-4 py-3 text-right text-[#6F6F6E] text-lg">{depositData.subTotalInclTax10 !== null && depositData.subTotalInclTax10 !== undefined ? formatNumber(depositData.subTotalInclTax10) : ''}</TableCell>
                      </TableRow>
                      <TableRow className="border-b border-gray-100">
                        <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">内消費税額</TableCell>
                        <TableCell className="px-4 py-3 text-right text-[#6F6F6E] text-lg">{depositData.subTotalConsumptionTax !== null && depositData.subTotalConsumptionTax !== undefined ? formatNumber(depositData.subTotalConsumptionTax) : ''}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg font-semibold">合計（税込）</TableCell>
                        <TableCell className="px-4 py-3 text-right text-[#6F6F6E] text-lg font-semibold">{depositData.subTotalTaxIncl !== null && depositData.subTotalTaxIncl !== undefined ? formatNumber(depositData.subTotalTaxIncl) : ''}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Sales Count Chart */}
              <div className="w-full p-4">
                <h3 className="text-xl font-semibold text-center mb-2 text-gray-500">
                  売上件数
                </h3>
                {/* <p className="text-center text-base text-gray-500 mb-2">
                    合計: {formatNumber(depositData.merchantPayments?.[0]?.agxNumberOfSales || 0)}
                  </p> */}
                <div className="h-96">
                  <Doughnut
                    data={createChartData(chartData.labelsNumberOfSales, chartData.percentageNumberOfSales)}
                    options={CHART_OPTIONS}
                    plugins={createPlugins(depositData.merchantPayments?.[0]?.agxNumberOfSales || 0)}
                  />
                </div>
              </div>

              {/* Sales Amount Chart */}
              <div className="w-full p-4">
                <h3 className="text-xl font-semibold text-center mb-2 text-gray-500">
                  売上金額
                </h3>
                {/* <p className="text-center text-base text-gray-500 mb-2">
                    合計: {formatNumber(depositData.merchantPayments?.[0]?.agxSalesAmount || 0)}円
                  </p> */}
                <div className="h-96">
                  <Doughnut
                    data={createChartData(chartData.labelsSalesAmount, chartData.percentageSalesAmount)}
                    options={CHART_OPTIONS}
                    plugins={createPlugins(depositData.merchantPayments?.[0]?.agxSalesAmount || 0)}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
