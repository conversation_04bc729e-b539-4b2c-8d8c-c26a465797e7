import { useMutation } from "@tanstack/react-query"
import { ChangeInfoRequest } from "../types"
import accountService from "../services/accountService"
import { useToast } from "@/hooks/use-toast";
import { useAuthStore } from "@/store";

export const useChangeInfo = () => {
    const { toast } = useToast();
      const { user, setUser } = useAuthStore();
    
    const {mutate, isPending,mutateAsync, isError, error, reset } = useMutation({
        mutationFn: async (data: ChangeInfoRequest) => {
            return await accountService.changeInfo({
                contactId: data.contactId,
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email
            })
        },
        onSuccess: (response) => {
            setUser({...user,
                firstName: response.data.firstName,
                lastName: response.data.lastName,
                email: response.data.email,
            })
            toast({
                title: "情報更新成功",
                description: "情報が更新されました",
                duration: 2000,
            });
        },
        onError: (error: any) => {
            console.error(error)
            toast({
                title: "情報更新失敗",
                description: error?.response?.data?.message || "情報の更新に失敗しました",
                variant: "destructive",
                duration: 2000,
            });
        }
    })
    return {
        changeInfo: mutate,
        changeInfoAsync: mutateAsync,
        isLoading: isPending,
        isError,
        error,
        reset
    }
}