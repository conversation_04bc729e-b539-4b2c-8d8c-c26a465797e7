import { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { AgxMerchantParams, ApplicationStepProps } from "../types"
import { MiniProgressBar } from "@/components/MiniProgressBar"
import { useAuthStore } from "@/store"
import { AccountTypes } from "@/types/globalType"
import { formatDate } from "@/utils/dateUtils"

export const CompleteComponent = ({ agxMerchantParams, updateMerchant }: ApplicationStepProps) => {
    const [progress, setProgress] = useState(0)
    const [isCompleted, setIsCompleted] = useState(false)
    const [saveError, setSaveError] = useState<string | null>(null)
    const [saveSuccess, setSaveSuccess] = useState(false)
    const [apiCallStarted, setApiCallStarted] = useState(false)
    const hasSavedRef = useRef(false)
    const progressTimerRef = useRef<NodeJS.Timeout | null>(null)
    const isInitializedRef = useRef(false)
    const apiCallInProgressRef = useRef(false)
    const { user, setUser } = useAuthStore()

    // Effect to check for previous error on mount
    useEffect(() => {
        const hasError = localStorage.getItem('application_error')
        if (hasError === 'true') {
            // Clear localStorage and redirect to login
            localStorage.clear()
            window.location.href = '/login'
            return
        }
    }, [])

    // Effect to start progress bar ONLY once when component mounts
    useEffect(() => {
        if (isInitializedRef.current) return // Prevent re-running

        isInitializedRef.current = true
        
        // Start running progress bar to 90%
        const duration = 2000 // 2 seconds to reach 90%
        const targetProgress = 90
        const steps = 100
        const increment = targetProgress / steps
        const interval = duration / steps

        progressTimerRef.current = setInterval(() => {
            setProgress(prev => {
                if (prev >= targetProgress) {
                    if (progressTimerRef.current) {
                        clearInterval(progressTimerRef.current)
                        progressTimerRef.current = null
                    }
                    // When reaching 90%, start calling API
                    setApiCallStarted(true)
                    return targetProgress
                }
                return Math.min(prev + increment, targetProgress)
            })
        }, interval)

        return () => {
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current)
                progressTimerRef.current = null
            }
        }
    }, []) // Only run once when mounted

    // Effect to call API when progress reaches 90%
    useEffect(() => {
        if (!apiCallStarted || hasSavedRef.current || !updateMerchant || apiCallInProgressRef.current) return

        const callAPI = async () => {
            try {
                apiCallInProgressRef.current = true
                hasSavedRef.current = true
                setSaveError(null)
                setSaveSuccess(false)

                // Call API with async/await
                await updateMerchant({ ...agxMerchantParams, agxApplicationStatus: 283260000 } as AgxMerchantParams)
                
                setSaveSuccess(true)
                
                // API successful → continue progress from 90% to 100%
                const remainingDuration = 1000 // 1 second to complete from 90% to 100%
                const steps = 50
                const increment = 10 / steps // Remaining 10%
                const interval = remainingDuration / steps

                const finalTimer = setInterval(() => {
                    setProgress(prev => {
                        if (prev >= 100) {
                            clearInterval(finalTimer)
                            setTimeout(() => {
                                setIsCompleted(true)
                            }, 200)
                            return 100
                        }
                        return Math.min(prev + increment, 100)
                    })
                }, interval)

            } catch (error) {
                console.error('API call failed:', error)
                setSaveError('データの保存中にエラーが発生しました。')
                setSaveSuccess(false)
                setIsCompleted(false)
                hasSavedRef.current = false
                apiCallInProgressRef.current = false
                
                // Set error flag to localStorage for reload detection
                localStorage.setItem('application_error', 'true')
            }
        }

        callAPI()
    }, [apiCallStarted]) // Only depend on apiCallStarted

    // Effect to reset completed when error occurs
    useEffect(() => {
        if (saveError) {
            setIsCompleted(false)
        }
    }, [saveError])

    const handleGoToOverview = () => {
        setUser({ ...user, statusAccount: AccountTypes.APPLICATION_COMPLETE, merchantRegistrationDate: formatDate(new Date()) })
        // Navigate to main menu or home
        window.location.href = '/overview';
    }

    const handleGoToLogin = () => {
        // Clear localStorage and redirect to login
        localStorage.clear()
        window.location.href = '/login'
    }

    // Debug: Log when about to render success message
    const shouldShowSuccess = isCompleted && !saveError && saveSuccess

    return (
        <div className="w-full mx-auto p-6 flex flex-col items-center">
            {/* <Card className="mt-6 border-none"> */}
                {/* <CardContent className="p-8 border-none"> */}
                    <div className="text-center space-y-6 w-full flex flex-col items-center justify-center mt-[10%]">
                        {saveError && (
                            <div className="w-full rounded-lg p-4">
                                <div className="flex justify-center mb-2">
                                    <div className="w-16 h-16 flex items-center justify-center">
                                        <span className="text-red-600 text-2xl">⚠️</span>
                                    </div>
                                </div>
                                <h2 className="text-[1.75rem] font-bold text-red-800 mb-2">
                                    エラーが発生しました
                                </h2>
                                <p className="text-red-700 text-[1.75rem] mb-6">{saveError}</p>
                                <Button 
                                    onClick={handleGoToLogin}
                                    className="w-80 h-16 text-[1.75rem] lg:text-[28px] text-white px-6 md:px-8 py-3 bg-red-600 rounded-xl hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                                >
                                    ログインページに戻る
                                </Button>
                            </div>
                        )}

                        {/* Progress Bar - display when no error and not completed */}
                        {!saveError && !isCompleted && (
                            <div className="w-full max-w-7xl mx-auto">
                                <MiniProgressBar progress={progress}/>
                                <p className="text-[#707070] mt-2 text-[1.7rem]">
                                    {progress < 90 ? 'お申込み処理中…' : 
                                     apiCallStarted && !saveSuccess ? 'データを保存中...' : 
                                     'お申込み処理中…'}
                                </p>
                            </div>
                        )}

                        {/* Completion Message - ONLY display when ALL conditions are true */}
                        {isCompleted && !saveError && saveSuccess && (
                            <div className="space-y-4 opacity-0 animate-fade-in-up">
                                <img src="/images/group_1316.svg" alt="complete" width={1278} height={320} className="mx-auto" />
                                
                                <div className="text-[#707070] space-y-2 text-[1.7rem] mb-12">
                                    <p>審査完了まで約1~2ヶ月かかります。 </p>
                                    <p>ご連絡をお待ちください。</p>
                                </div>
                                <Button 
                                    onClick={handleGoToOverview}
                                    className="w-80 h-16 text-[1.75rem] lg:text-[28px] text-white px-6 md:px-8 py-3 bg-teal-500 rounded-xl hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500"
                                >
                                    戻る
                                </Button>
                            </div>
                        )}
                    </div>
                {/* </CardContent> */}
            {/* </Card> */}
                {/* </CardContent> */}
            {/* </Card> */}

            <style>
                {`
                @keyframes fade-in-up {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .animate-fade-in-up {
                    animation: fade-in-up 0.8s ease-out forwards;
                }
                `}
            </style>
        </div>
    )
}