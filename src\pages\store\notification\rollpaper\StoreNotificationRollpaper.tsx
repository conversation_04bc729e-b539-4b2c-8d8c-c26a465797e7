import React from 'react';
import DualServiceLinks from '@/components/DualServiceLinks';

const StoreNotificationRollpaper: React.FC = () => {
  const paygateLink = {
    id: 'paygate',
    label: 'PAYGATE',
    url: 'https://help-paygate.smaregi.jp/hc/'
  };

  const crepicoLink = {
    id: 'crepico',
    label: 'クレピコ',
    url: 'https://www.seiko-sol.co.jp/products/crepico/crepico_user/user_order/'
  };
  
  const crepicoInfo = {
    username: 'cp-user',
    password: 'cp-user1'
  };

  return (
    <DualServiceLinks
      paygateLink={paygateLink}
      crepicoLink={crepicoLink}
      showCrepicoInfo={true}
      crepicoInfo={crepicoInfo}
    />
  );
};

export default StoreNotificationRollpaper;
