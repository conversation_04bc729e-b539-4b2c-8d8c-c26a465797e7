export interface GetMerchantStatusResponse {
    agxMerchantNo:                       string;
    agxCorporateName:                    string;
    agxMerchantid:                       string;
    agxContactId:                        string;
    agxBankPhoneticName:                 string;
    agxSettlementEdy:                    boolean;
    agxSettlementAid:                    boolean;
    agxSettlementJcb:                    boolean;
    agxSettlementNanaco:                 boolean;
    agxPcidssExpectedComplianceDate:     Date;
    agxPcidssStatus:                     number;
    agxSettlementQrCode:                 boolean;
    agxSettlementQuicpay:                boolean;
    agxSettlementWaon:                   boolean;
    agxUrl:                              string | null;
    agxCardInformationRetentionStatus:   number;
    agxNoRetainingCardInfoDate:          Date;
    agxSettlementCard:                   boolean;
    agxSecurityCodeCheckStatus:          number;
    agxSecurityCodeCheckDate:            Date;
    agxOtherMeasuresStatus:              number;
    agxOtherMeasuresDate:                Date;
    agxBrandName:                        string | null;
    agxIllegalDeliveryDestinationStatus: number;
    agxIllegalDeliveryDestinationDate:   Date;
    agxSettlementTraffic:                boolean;
    agxRepresentativeFaxNumber:          string | null;
    agxRepresentativeAddress1:           string;
    agxRepresentativePhoneticAddress1:   string;
    agxRepresentativeAddress2:           string | null;
    agxRepresentativePhoneticAddress2:   string | null;
    agxRepresentativeName:               string;
    agxRepresentativePhoneticName:       string;
    agxRepresentativeGender:             number;
    agxRepresentativeAddressCopyFlag:    boolean;
    agxRepresentativeBirthday:           Date;
    agxRepresentativePostalCode:         string;
    agxRepresentativePrefecture:         string;
    agxRepresentativePhoneticPrefecture: string;
    agxRepresentativePhoneNumber:        string;
    agxAccountHolder:                    string;
    agxAccountNo:                        string;
    agxBusinessDate:                     string | null;
    agxBusinesssHours:                   string | null;
    agxRegularHoliday:                   string | null;
    agxOtherMeasuresDescription:         string | null;
    agxBehaviorAnalysisStatus:           number;
    agxBehaviorAnalysisDate:             Date;
    agxStoreFaxNumber:                   string | null;
    agxStoreAddress1:                    string;
    agxStorePhoneticAddress1:            string;
    agxStoreAddress2:                    string | null;
    agxStorePhoneticAddress2:            string | null;
    agxStoreName:                        string;
    agxStorePhoneticName:                string;
    agxStoreAddressCopyFlag1:            boolean;
    agxStoreAddressCopyFlag2:            boolean;
    agxStorePostalCode:                  string;
    agxStorePrefecture:                  string;
    agxStorePhoneticPrefecture:          string;
    agxStorePhoneNumber:                 string;
    agxNumberOfEmployees:                number;
    agxContactEmail:                     string;
    agxContactName:                      string;
    agxContactPhoneticName:              string;
    agxContactPhoneNumber:               string;
    agxBranchName:                       string;
    agxBranchPhoneticName:               string;
    agxBranchNo:                         string;
    agxBranchType:                       number;
    agxMonthlySales:                     number;
    agxThreeDSecureStatus:               number;
    agxThreeDSecureDate:                 Date;
    agxBusinessOpportunityRelatedSales:  boolean;
    agxCorporateFaxNumber:               string | null;
    agxCorporateAddress1:                string;
    agxCorporatePhoneticAddress1:        string;
    agxCorporateAddress2:                string | null;
    agxCorporatePhoneticAddress2:        string | null;
    agxCorporatePhoneticName:            string;
    agxCorporateEnglishName:             string;
    agxCorporateNumber:                  string;
    agxCorporatePostalCode:              string;
    agxCorporatePrefecture:              string;
    agxCorporatePhoneticPrefecture:      string;
    agxCorporatePhoneNumber:             string;
    agxSpecifiedContinuousServices:      boolean;
    agxApplicationStatus:                number;
    agxColorOfTerminal:                  number;
    agxNumberOfTerminal:                 number;
    agxStoreEnglishName:                 string;
    agxDoorToDoorSales:                  boolean;
    agxFoundingDate:                     Date;
    agxCapital:                          number;
    agxPyramidScheme:                    boolean;
    agxBankName:                         string;
    agxBankNo:                           string;
    agxBankType:                         number;
    agxTelemarketingSales:               boolean;
    agxAccountType:                      number;
    agxBusinessType:                     number;
    agxBusinessForm:                     number;
    agxMedicalInstitutionCode:           string;
    agxEmsEmployeeNo:                    string;
    agxMunMerchantNo:                    string | null;
    agxStoreBranchNo:                    string | null;
    agxJcbExistsMembershipFlag:          string | null;
    agxJcbMerchantNumber:                string | null;
    memberType:                          boolean;
    agxSettlementPackage1:               boolean;
    agxSettlementPackage2:               boolean;
    agxNewTerminalNo1:                   string | null;
    agxNewTerminalNo2:                   string | null;
    agxNewTerminalNo3:                   string | null;
    agxNewTerminalNo4:                   string | null;
    agxNewTerminalNo5:                   string | null;
    agxNewTerminalNo6:                   string | null;
    agxNewTerminalNo7:                   string | null;
    agxNewTerminalNo8:                   string | null;
    agxNewTerminalNo9:                   string | null;
    agxNewTerminalNo10:                  string | null;
}

export interface GetAgxFeeRateResponse {
    qrOtherBankpayRate: string;
    creditVisaRate:     string;
    quicpayRate:        string;
    edyRate:            string;
    creditJcbRate:      string;
    transportationRate: string;
    creditUnionRate:    string;
    qrBankpayRate:      string;
    waonRate:           string;
    idRate:             string;
    nanacoRate:         string;
}