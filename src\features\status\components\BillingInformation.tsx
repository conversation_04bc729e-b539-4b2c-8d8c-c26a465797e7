import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { GetMerchantStatusResponse } from "../types";
import { mapBusinessForm, mapBusinessType } from "@/constants/common.constant";

interface BillingInformationProps {
  merchantData: GetMerchantStatusResponse;
}
function BillingInformation({ merchantData }: BillingInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-14">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            業種
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {mapBusinessType.get(merchantData?.agxBusinessType)}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            法人/個人
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {mapBusinessForm.get(merchantData?.agxBusinessForm)}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            医療機関コード
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxMedicalInstitutionCode}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            管理コード
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxEmsEmployeeNo}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default BillingInformation;
