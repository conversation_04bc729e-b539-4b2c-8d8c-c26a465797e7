import React from 'react';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';
import Box from '@/components/ui/box';

interface FormFieldProps {
  label?: string;
  required?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({ 
  label, 
  required = false, 
  children, 
  className = '' 
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-[200px_1fr] lg:grid-cols-[280px_1fr] gap-2 md:gap-6 lg:gap-8 items-start md:items-center text-[1.75rem] font-normal ${className}`}>
      <div className="flex items-center gap-1 justify-start md:justify-end">
        <Label className="text-[#707070] text-left md:text-right whitespace-nowrap text-[1.75rem]">
          {label}
        </Label>
        {required && (
          <span className="text-[rgba(255,0,0,1)]" aria-label="required">
            *
          </span>
        )}
      </div>
      <div className="flex items-center gap-6">
        {children}
      </div>
    </div>
  );
};

interface DisplayFieldProps {
  label: string;
  value: string;
  required?: boolean;
  className?: string;
  date?: string;
}

export const DisplayField: React.FC<DisplayFieldProps> = ({ 
  label, 
  value, 
  required = false, 
  className = '',
  date = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-[250px_1fr] lg:grid-cols-[550px_1fr] gap-2 md:gap-6 lg:gap-8 items-start text-[1.75rem] font-normal ${className}`}>
      <div className="flex items-start gap-1 justify-start md:justify-end">
        <Label 
          className="text-[#707070] text-left md:text-right text-[1.75rem] break-words hyphens-auto leading-relaxed"
        >
          {label}
        </Label>
        {required && (
          <span className="text-[rgba(255,0,0,1)] flex-shrink-0" aria-label="required">
            *
          </span>
        )}
      </div>
      <div className="flex items-start min-w-0 w-full">
        <span 
          className="text-[rgba(112,112,112,1)] text-[1.75rem] font-normal break-words hyphens-auto leading-relaxed w-full"
          style={{
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
            wordBreak: 'break-word',
            whiteSpace: 'normal',
            hyphens: 'auto',
            maxWidth: '100%'
          }}
        >
          {value} {date && format(new Date(date), 'yyyy年MM月')}
        </span>
      </div>
    </div>
  );
};

type FormInputProps = {
  label: string;
  name: string;
  placeholder?: string;
  register: any;
  errors?: Record<string, any>;
  required?: boolean;
  maxLength?: number;
  dataPattern?: string;
  disabled?: boolean;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxWidth?: string;
};

export const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  placeholder,
  register,
  errors,
  required = false,
  maxLength = 100,
  dataPattern = '',
  disabled = false,
  onBlur,
  onChange,
  maxWidth = 'md:w-full',
}) => {
  const { onChange: registerOnChange, onBlur: registerOnBlur, name: registerName, ref } = register(name);

  return (
    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
      <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
        {label}
        {required && (
          <span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
        )}
      </Label>

      <Box className={`w-full md:flex-1 flex flex-col ${maxWidth}`}>
        <input
          name={registerName}
          ref={ref}
          maxLength={maxLength}
          data-pattern={dataPattern}
          disabled={disabled}
          onBlur={onBlur || registerOnBlur}
          onChange={onChange || registerOnChange}
          placeholder={placeholder}
          className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
        />
        <div className="min-h-[16px] md:min-h-[20px] mt-1">
          {errors?.[name] && (
            <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
              {errors[name].message}
            </span>
          )}
        </div>
      </Box>
    </Box>
  );
};

export const SelectInput: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`bg-white border flex flex-col justify-center w-full px-3 md:px-6 lg:px-3 py-2 md:py-4 lg:py-3 rounded-[13px] border-[rgba(112,112,112,1)] border-solid ${className}`}>
      <select className="bg-transparent text-[rgba(112,112,112,1)] text-sm md:text-base lg:text-[28px] font-normal outline-none">
        <option value="">選択してください</option>
        <option value="hokkaido">北海道</option>
        <option value="tokyo">東京都</option>
        <option value="osaka">大阪府</option>
      </select>
    </div>
  );
};
