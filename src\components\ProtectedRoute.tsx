import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { Roles, AccountTypes, TypeStore } from '@/types/globalType';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredAccountType?: AccountTypes[];
  requiredTypeStore?: TypeStore[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredAccountType,
  requiredTypeStore 
}) => {
  const { user, typeStore, isAuthenticated } = useAuthStore();
  const hasToken = !!localStorage.getItem('accessToken');

  // If not authenticated, redirect to login
  if (!isAuthenticated || !hasToken) {
    return <Navigate to="/login" replace />;
  }

  // Check account type and type store
  if (user) {
    let hasRequiredAccess = true;

    // Check statusAccount if requiredAccountType is specified
    if (requiredAccountType && requiredAccountType.length > 0) {
      const hasRequiredAccountType = requiredAccountType.some(type => 
        user.statusAccount === type
      );
      
      if (!hasRequiredAccountType) {
        hasRequiredAccess = false;
      }
    }

    // Check typeStore if requiredTypeStore is specified
    if (requiredTypeStore && requiredTypeStore.length > 0) {
      const hasRequiredTypeStore = requiredTypeStore.some(type => 
        typeStore === type
      );
      
      if (!hasRequiredTypeStore) {
        hasRequiredAccess = false;
      }
    }

    // If user doesn't have required access and is admin, redirect to unauthorized
    if (!hasRequiredAccess || (user.roles.length === 0 || user.roles.includes(Roles.ROLE_ADMIN))) {
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return <>{children}</>;
}; 