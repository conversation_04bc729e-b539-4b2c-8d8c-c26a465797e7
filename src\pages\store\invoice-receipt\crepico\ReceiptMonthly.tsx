import React from 'react';
import { ReceiptMonthlyLayout } from '@/features/store/invoice-receipt/components/ReceiptMonthlyLayout';
import { useParams } from 'react-router-dom';
import { useStoreReceiptMonthly } from '@/features/store/invoice-receipt/hooks/useStoreReceiptMonthly';

const CrepicoReceiptMonthly: React.FC = () => {
    const { invoiceNo } = useParams<{ invoiceNo: string }>();
    const { data, loading } = useStoreReceiptMonthly(invoiceNo);

    return <ReceiptMonthlyLayout data={data} loading={loading} isReceipt={false} />;
};

export default CrepicoReceiptMonthly;