import React from 'react'
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

interface IProps{
    isDialogOpen: boolean;
    setIsDialogOpen: (v: boolean) => void;
    handleConfirmDelete: () => void;
}


export const ConfirmDeleteModal = ({isDialogOpen,setIsDialogOpen,handleConfirmDelete}: IProps) => {
  return (
    <>
        {/* AlertDialog for delete confirmation */}
        <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <AlertDialogContent>
            <AlertDialogTitle>
                <span className='text-[20px]'>削除の確認</span>
            </AlertDialogTitle>
            <AlertDialogHeader>
              <AlertDialogDescription>
                    <span className='text-[20px]'>本当に削除しますか？</span>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <Button
                className="text-[20px] w-[20%] max-md:w-full h-[48px] bg-gray-400 text-white rounded-[8px] hover:bg-gray-500 transition-colors"
                onClick={() => setIsDialogOpen(false)}>閉じる</Button>
              <Button
                type="button"
                className="bg-[#c94e4e] hover:bg-[#c93838] text-white font-medium px-8 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-[18px]"
                onClick={handleConfirmDelete}>削除</Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </>
  )
}
