import { useQuery } from "@tanstack/react-query";
import { areaSettingService } from "../services/areaSettingService";

export const useQueryAgxArea = ({agxMerchantNo}: {agxMerchantNo: string}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['agxMerchantNo', agxMerchantNo],
    queryFn: async () => await areaSettingService.getAllData(agxMerchantNo)  
  });

  return {
    data, isLoading, isError, error, refetch
  };
}
