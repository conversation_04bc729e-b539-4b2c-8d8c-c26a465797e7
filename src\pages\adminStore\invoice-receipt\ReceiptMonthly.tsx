import { Download } from "lucide-react";
import { useParams } from 'react-router-dom';
import { formatMoney, formatNumber } from '@/utils/dateUtils';
import Secretkey from '@/assets/images/secretkey.png';
import ChoqiHealthHacking from '@/assets/images/choqi-health-hacking.png';
import PDFService, { blobToBase64, PDFConfig } from "@/services/pdfService";
import { axiosPDF } from "@/features/store/invoice-receipt/utils";
import { useAuthStore } from "@/store";
import { useGetDataReceiptMonthly } from "@/features/adminStore/invoice-receipt/hooks/useGetDataReceiptMonthly";
import { LoadingSpinner } from "@/components/LoadingSpinner";

const ReceiptMonthly = () => {
  const { user } = useAuthStore();
  const { yearMonth } = useParams<{ yearMonth: string }>();
  const switchLayoutDate = '2023-11-05';

  // Use the hook to get data
  const { data, isLoading } = useGetDataReceiptMonthly(user.agxMerchantNo, yearMonth);

  // Handle loading state
  if (isLoading) {
    return <LoadingSpinner />;
  }

  const invoiceNo = `${user.agxMerchantNo}-${yearMonth}`;

    // PDF export logic
    const handleExportPDF = async () => {
        try {
            if (!data) {
                alert('データが見つかりません。');
                return;
            }
            // Load fonts
            const responseYugothib = await axiosPDF.get('yugothib.ttf');
            if (responseYugothib.status !== 200) {
                alert('フォントの読み込みに失敗しました。');
                return;
            }
            const fontYugothib = await blobToBase64(responseYugothib.data);
            const responseArial = await axiosPDF.get('arial.ttf');
            if (responseArial.status !== 200) {
                alert('フォントの読み込みに失敗しました。');
                return;
            }
            const fontArial = await blobToBase64(responseArial.data);
            await PDFService.setupMultipleFonts(fontYugothib, fontArial);

            // Company info
            const companyInfo = [
                { text: '〒532-0003', margin: [0, 0, 0, 5], fontSize: 12 },
                { text: '大阪府大阪市淀川区宮原1-6-1', margin: [0, 0, 0, 5], fontSize: 12 },
                { text: '新大阪ブリックビル', margin: [0, 0, 0, 10], fontSize: 12 },
                { text: 'TEL:06-6397-5210', margin: [0, 0, 0, 5], fontSize: 12 },
                { text: 'FAX:06-6397-5211', margin: [0, 0, 0, 5], fontSize: 12 },
                { text: '<EMAIL>', margin: [0, 0, 0, 5], fontSize: 12 }
            ];
            if (data?.invoiceCreatedDate && data.invoiceCreatedDate > switchLayoutDate) {
                companyInfo.unshift(
                    { text: '適格事業者登録番号', margin: [0, 0, 0, 5], fontSize: 12 },
                    { text: '(T6120001228218)', margin: [0, 0, 0, 5], fontSize: 12 }
                );
            }

            const config: PDFConfig = {
                pageSize: { width: 640, height: 850 } as any,
                pageOrientation: 'portrait',
                defaultStyle: { font: 'yugothib' },
                content: [
                    { text: `領収書番号: ${invoiceNo}`, alignment: 'right', margin: [0, 20, 20, 5] },
                    { text: '領収書', fontSize: 24, alignment: 'center', style: 'header', margin: [0, 0, 0, 25] },
                    { text: `${user.agxStoreName} 御中`, alignment: 'left', margin: [40, 0, 0, 15] },
                    {
                        margin: [140, 0, 0, 0],
                        table: {
                            widths: [120, 150],
                            heights: [20],
                            body: [
                                [
                                    { border: [true, true, false, true], text: '\n合計金額', margin: [10, 0, 0, 0] },
                                    { border: [false, true, true, true], text: `\n${data?.total ? formatMoney(data?.total) : 0}`, margin: [90, 0, 0, 10] }
                                ],
                            ]
                        }
                    },
                    {
                        margin: [140, 0, 0, 20],
                        table: {
                            widths: [280, '*'],
                            heights: [20],
                            body: [
                                [
                                    { border: [false, false, false, false], text: '但 決済端末機器代として ', margin: [0, 10, 0, 0] }
                                ],
                                [
                                    { border: [false, false, false, true], text: '上記正に領収いたしました', margin: [0, 0, 0, 5] }
                                ],
                            ]
                        }
                    },
                    {
                        margin: [20, 20, 20, 0],
                        columns: [
                            {
                                style: 'tableExample',
                                table: {
                                    widths: [80, 80],
                                    heights: [15, 15],
                                    body: [
                                        [
                                            { border: [false, false, false, true], text: '小計', alignment: 'left' },
                                            { border: [false, false, false, true], text: data?.subTotal ? formatMoney(data?.subTotal) : 0, alignment: 'right' }
                                        ],
                                        [
                                            { border: [false, false, false, true], text: '消費税(10%)', alignment: 'left' },
                                            { border: [false, false, false, true], text: data?.tax ? formatMoney(data?.tax) : 0, alignment: 'right' }
                                        ],
                                    ]
                                },
                            },
                            {
                                stack: [
                                    { text: 'チョキ株式会社', margin: [0, 0, 0, 5], fontSize: 12 },
                                    ...companyInfo
                                ]
                            }
                        ]
                    }
                ],
                styles: {
                    header: {
                        fontSize: 18,
                        bold: true,
                        margin: [0, 0, 0, 10]
                    },
                    tableExample: {
                        margin: [0, 0, 0, 0]
                    }
                },
                filename: `receipt-monthly.pdf`
            };
            await PDFService.createPDFWithSetupFonts(config);
        } catch (error) {
            console.error('PDF作成エラー:', error);
            alert('PDF作成に失敗しました。');
        }
    };

  return (
    <div className="p-4 md:py-6 md:px-1 lg:p-6">
      <div className="page-heading">
        <div className="flex">
          <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">領収書</h2>
          <button id="download-pdf" className="p-2 pl-4 text-[#1D9987] hover:text-[#1D9987]/80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" onClick={handleExportPDF}>
            <Download className="h-6 w-6" />
          </button>
        </div>
      </div>
      <hr className='mt-3 border-1 border-gray-500' />
      <div className="py-4">
        <div className="space-y-2">
            <p id="merchant-no" className="text-[18px] font-medium text-[#6F6F6E]">請求書番号: {invoiceNo}</p>
        </div>
      </div>
      <div className="pt-2">
        <p className="text-[24px] text-[#6F6F6E] font-bold">{user.agxStoreName} 御中</p>
      </div>

      <div className="flex justify-center items-center py-8">
        <div className="w-full max-w-2xl">
            <div className="flex justify-between items-center bg-white p-4 border-4 border-gray-500">
                <p className="text-[24px] font-bold text-[#6F6F6E]">合計金額</p>
                <div id="total-fee"><p id="receipt-total" className="text-[24px] font-bold text-[#6F6F6E]">¥ {formatNumber(data?.total)}</p></div>
            </div>
            <div className="pt-4 text-left">
              <p className="text-[18px] font-medium text-[#6F6F6E]">
                  <span id="sp1">但 月額費用として </span><br />
                  <span id="sp2">上記正に領収いたしました</span></p>
              <div className="mx-auto mt-2 w-full border-b-2 border-gray-500" />
            </div>
        </div>
      </div>
      <div className="flex justify-between items-start py-4">
        <div className="flex-1 w-1/2 flex flex-col items-center">
          <div className="flex w-full max-w-md justify-between items-center p-4 border-b-2 border-gray-500">
            <p className="text-[18px] font-medium text-[#6F6F6E]">小計</p>
            <div id="subtotal" className="text-[18px] font-medium text-[#6F6F6E]">{formatNumber(data?.subTotal || 0)}</div>
          </div>
          <div className="flex w-full max-w-md justify-between items-center p-4 border-b-2 border-gray-500">
            <p className="text-[18px] font-medium text-[#6F6F6E]">消費税(10%)</p>
            <div id="consumption-tax" className="text-[18px] font-medium text-[#6F6F6E]">{formatNumber(data?.tax || 0)}</div>
          </div>
        </div>
        <div className="flex-1 w-1/2 max-w-2xl flex flex-col items-start pl-[50px]">
          <div className="mb-4">
            <img id="image-health" src={ChoqiHealthHacking} alt="ChoQi Health Hacking" className="w-48 h-auto" />
          </div>
          <div className="font-medium text-[#6F6F6E] text-left bg-contain bg-no-repeat bg-right-top" style={{ backgroundImage: `url(${Secretkey})`, backgroundSize: '100px 100px' }}>
            <div id="company-name" className="text-[22px] font-bold mb-2">チョキ株式会社</div>
            {data?.invoiceCreatedDate > switchLayoutDate && (
            <div className="mb-2">
              <div className="text-[16px]">適格事業者登録番号</div>
              <div className="text-[16px]">(T6120001228218)</div>
            </div>
            )}
            <div id="postcode" className="text-[16px]">〒532-0003</div>
            <div id="addr1" className="text-[16px]">大阪府大阪市淀川区宮原1-6-1</div>
            <div id="addr2" className="text-[16px] mb-2">新大阪ブリックビル</div>
            <div id="tell" className="text-[16px]">TEL:06-6397-5210</div>
            <div id="fax" className="text-[16px]">FAX:06-6397-5211</div>
            <div id="mail" className="text-[16px]"><EMAIL></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceiptMonthly;
