import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { ChangeInfoRequest, ChangeInfoResponse, ChangePasswordRequest, ChangePasswordResponse } from "../types";

class AccountService {
      /**
   * Change user information
   * @param request - User information to change
  */
  changeInfo(request: ChangeInfoRequest) {
    return apiService.post<{data: ChangeInfoResponse}>(API_ENDPOINTS.AUTH.CHANGE_INFO,
      request
    );
  };

  /**
   * Change user password
   * @param request - Password change request
   * @return Promise resolving to the response
   * */
  changePassword(request: ChangePasswordRequest) {
    return apiService.post<{data: ChangePasswordResponse}>(API_ENDPOINTS.AUTH.CHANGE_PASSWORD,
      request
    );
  };

}

export const accountService = new AccountService();
export default accountService; 