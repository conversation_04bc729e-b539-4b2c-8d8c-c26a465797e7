import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { IMerchantCoreType } from "@/features/auth/types";

class ApplicationService {
    async create(application: IMerchantCoreType): Promise<any> {
        try {
            const response = await apiService.post<any>(API_ENDPOINTS.APPLICATION.CREATE, application);
            return response.data;
        } catch (error) {
            console.error('Application creation error:', error);
            throw error;
        }
    }
}
export const applicationService = new ApplicationService();