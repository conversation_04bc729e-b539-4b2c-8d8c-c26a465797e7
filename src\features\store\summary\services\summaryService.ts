import { apiService } from '../../../../services/api';
import { SummaryApiResponse, MonthlySummaryApiResponse } from '@/features/store/summary/types/summaryType';
import { API_ENDPOINTS } from '@/config/api-endpoints';

export const summaryService = {
  /**
   * Get summary data for charts and tables
   * @param merchantNo - Merchant number
   * @returns Promise with summary data
   */
  getSummaryData: async (merchantNo: string): Promise<SummaryApiResponse> => {
    try {
      const response: any = await apiService.get(API_ENDPOINTS.SUMMARY.GET_DATA(merchantNo));
      return response as SummaryApiResponse;
    } catch (error) {
      console.error('Error fetching summary data:', error);
    }
  },

  /**
   * Get monthly summary data
   * @param merchantNo - Merchant number
   * @returns Promise with monthly summary data
   */
  getMonthlySummaryData: async (merchantNo: string): Promise<MonthlySummaryApiResponse> => {
    try {
      const response: any = await apiService.get(API_ENDPOINTS.SUMMARY.GET_MONTHLY_DATA(merchantNo));
      return response as MonthlySummaryApiResponse;
    } catch (error) {
      console.error('Error fetching monthly summary data:', error);
    }
  },
};
