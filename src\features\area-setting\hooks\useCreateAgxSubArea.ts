import { useToast } from '@/hooks/use-toast';
import { useMutation } from '@tanstack/react-query';
import { areaSettingService } from '../services/areaSettingService';
import { AgxSubAreaParam } from '../type';

export const useCreateAgxSubArea = () => {
    const { toast } = useToast();
    const createSubArea =  useMutation({
        mutationFn: async (agxSubArea: AgxSubAreaParam) => {
            await areaSettingService.createSubArea(agxSubArea);
        },
        onSuccess: () => {
            toast({
                variant: "default",
                title: '保存が完了しました。',
                description: "",
            });
        },
        onError: () => {
            toast({
                variant: "destructive",
                title: '保存が完了しました。',
                description: "",
            });
        }
    });
    return {
        createAgxSubArea: createSubArea.mutate,
        createAgxSubAreaAsync: createSubArea.mutateAsync,
        isLoading: createSubArea.isPending,
        isError: createSubArea.isError,
        error: createSubArea.error,
        reset: createSubArea.reset,
    };
};
