import React from "react";

interface ProgressStepperProps {
  steps: string[];
  currentStep: number; // 1-based index
}

export const ProgressStepper: React.FC<ProgressStepperProps> = ({ steps, currentStep }) => {
  return (
    <div className="flex items-center justify-center w-full mb-8">
      <div className="flex items-center w-full max-w-2xl">
        {steps.map((label, idx) => {
          const stepNum = idx + 1;
          const isActive = stepNum === currentStep;
          const isCompleted = stepNum < currentStep;
          return (
            <React.Fragment key={label}>
              <div className="flex flex-col items-center min-w-[60px]">
                <div
                  className={
                    `flex items-center justify-center w-8 h-8 rounded-full border-2 ` +
                    (isActive
                      ? "bg-[#1D9987] text-white border-[#1D9987]"
                      : isCompleted
                      ? "bg-[#1D9987] text-white border-[#1D9987]"
                      : "bg-[#F6F6F6] text-[#6F6F6E] border-[#6F6F6E]")
                  }
                >
                  {isCompleted ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                  ) : (
                    <p className="text-base md:text-[20px]">{stepNum}</p>
                  )}
                </div>
                <span className={`mt-2 text-xs font-medium text-center ${isActive ? "text-[#1D9987]" : isCompleted ? "text-[#1D9987]" : "text-[#6F6F6E]"}`}>{label}</span>
              </div>
              {idx < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-2 ${isCompleted ? "bg-[#1D9987]" : "bg-[#6F6F6E]"}`}></div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}; 