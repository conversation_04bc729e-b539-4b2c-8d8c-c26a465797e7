import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import logo from '@/assets/images/グループ 3.svg';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="flex flex-col items-center justify-center text-center space-y-6 w-full max-w-[580px] mx-auto h-[60vh]">
        <div className="flex items-center justify-center space-x-2 mb-1">
          <img src={logo} alt="ChoQi Logo" className="h-[85px] object-contain" />
        </div>

        <h1 className="text-2xl font-bold text-gray-900 mb-1">
          アクセス権限がありません
        </h1>
        <p className="text-[#6F6F6E]">
          このページにアクセスする権限がありません。管理者にお問い合わせください。
        </p>
        <div className='w-full px-2'>
          <button
            onClick={handleGoBack}
            className="w-full h-[50px] bg-white text-black rounded-md text-[20px] font-medium shadow-md hover:bg-gray-100 transition-colors flex items-center justify-center cursor-pointer"
          >
            前のページに戻る
          </button>
        </div>
        <Link to="/" className="w-full px-2">
          <div
            className="w-full h-[50px] bg-[#19A492] text-white rounded-md text-[20px] font-medium shadow-md hover:bg-[#15b19d] transition-colors flex items-center justify-center cursor-pointer"
          >
            ホームに戻る
          </div>
        </Link>
      </div>
    </div>
  );
};

export default Unauthorized; 