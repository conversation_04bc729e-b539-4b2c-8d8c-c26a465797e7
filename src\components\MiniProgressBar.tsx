import React from 'react';

interface MiniProgressBarProps {
  progress: number;
  className?: string;
}

export const MiniProgressBar: React.FC<MiniProgressBarProps> = ({ progress, className = '' }) => {
  return (
    <div className={`bg-white border flex flex-col mt-4  max-md:mt-2 rounded-[11px] border-[rgba(112,112,112,1)] border-solid max-md:max-w-full max-md:pr-5 ${className}`}>
      <div 
        className="bg-[rgba(26,164,146,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] flex shrink-0 max-w-full h-[20px] rounded-[11px]"
        style={{ width: `${Math.min(progress, 100)}%`, minWidth: '100px' }}
      />
    </div>
  );
};
