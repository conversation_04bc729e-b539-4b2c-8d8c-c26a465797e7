import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { GetMerchantStatusResponse } from "../types";

interface CorporateInformationProps {
  merchantData: GetMerchantStatusResponse;
}
function CorporateInformation({ merchantData }: CorporateInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            法人名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporateName !== null
                ? merchantData?.agxCorporateName
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            法人名（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporatePhoneticName !== null
                ? merchantData?.agxCorporatePhoneticName
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            法人名（英字）
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporateEnglishName !== null
                ? merchantData?.agxCorporateEnglishName
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">法人番号</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporateNumber !== null
                ? merchantData?.agxCorporateNumber
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">郵便番号</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporatePostalCode !== null
                ? merchantData?.agxCorporatePostalCode
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">都道府県</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporatePrefecture !== null
                ? merchantData?.agxCorporatePrefecture
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">住所１</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporateAddress1 !== null
                ? merchantData?.agxCorporateAddress1
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所1（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporatePhoneticAddress1 !== null
                ? merchantData?.agxCorporatePhoneticAddress1
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">住所2</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporateAddress2 !== null
                ? merchantData?.agxCorporateAddress2
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所2（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporatePhoneticAddress2 !== null
                ? merchantData?.agxCorporatePhoneticAddress2
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">電話番号</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporatePhoneNumber !== null
                ? merchantData?.agxCorporatePhoneNumber
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">FAX番号</Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxCorporateFaxNumber !== null
                ? merchantData?.agxCorporateFaxNumber
                : ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default CorporateInformation;
