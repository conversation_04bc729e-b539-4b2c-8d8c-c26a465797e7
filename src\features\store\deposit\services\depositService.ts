import { apiService } from '@/services/api';
import { DepositDataResponse, DateResponse, DepositDetailAllResponse, DepositDetailResponse } from '../types/depositDetail';
import { API_ENDPOINTS } from '@/config/api-endpoints';

export const depositService = {
  // Get available dates for deposit
  getDateFromAgxPaymentManagement: async (merchantNo: string): Promise<DateResponse> => {
      const response = await apiService.get<DateResponse>(API_ENDPOINTS.STORE_DEPOSIT.DATES(merchantNo));
      return response;
  },

  // Get deposit data for specific date
  getDepositData: async (merchantNo: string, date: string): Promise<DepositDataResponse> => {
      const response = await apiService.get<DepositDataResponse>(API_ENDPOINTS.STORE_DEPOSIT.DATA(merchantNo, date));
      return response;
  },

  // Get all deposit details for a specific date
  getDataStoreDepositDetail: async (merchantNo: string, paymentDate: string): Promise<DepositDetailAllResponse> => {
      const response = await apiService.get<DepositDetailAllResponse>(API_ENDPOINTS.STORE_DEPOSIT.DETAIL_ALL(merchantNo,paymentDate));
      return response;
  },

  // Get specific deposit detail by breakdown
  getElementStoreDepositDetail: async (
    merchantNo: string,
    paymentBId: string,
    transactionType: string,
    datetime: string
  ): Promise<DepositDetailResponse> => {
      const response = await apiService.get<DepositDetailResponse>(
        API_ENDPOINTS.STORE_DEPOSIT.DETAIL_ELEMENT(merchantNo, paymentBId, transactionType, datetime)
      );
      return response;
  }
};
