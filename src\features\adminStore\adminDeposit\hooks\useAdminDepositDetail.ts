import { useQuery } from '@tanstack/react-query';
import { adminDepositService } from '../services/adminDepositService';
import { AdminDepositDetailData } from '../types';

/**
 * Hook for fetching admin deposit detail data using React Query
 */
export const useAdminDepositDetail = (
  agxMerchantNo: string,
  transferDate: string,
  transactionType?: string,
  merchantNo?: string,
  paymentBId?: string,
  area?: string,
  subArea?: string
) => {
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: [
      'admin-deposit-detail',
      agxMerchantNo,
      transferDate,
      transactionType || '',
      merchantNo || '',
      paymentBId || '',
      area || '',
      subArea || ''
    ],
    queryFn: () => adminDepositService.getDataAdminDepositDetail(
      agxMerchantNo,
      transferDate,
      transactionType || '',
      merchantNo || '',
      paymentBId || '',
      area || '',
      subArea || ''
    ),
    enabled: !!agxMerchantNo && !!transferDate,
    staleTime: 0,
    gcTime: 0
  });

  return {
    // Data
    data: data || null,

    // Loading state
    isLoading,

    // Error states
    error: error ? (error instanceof Error ? error.message : 'データの取得に失敗しました') : null,

    // Actions
    refetch
  };
};
