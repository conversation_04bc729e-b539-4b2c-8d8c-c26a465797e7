import { useState, useEffect } from 'react';
import logo from '@/assets/images/グループ 3.svg';
import { useParams, useNavigate } from 'react-router-dom';
import { merchantCore } from '@/constants/common.constant';
import { IMerchantCoreType } from '@/features/auth';
import Registration from '@/features/auth/components/register/Registration';

export const RegisterAccount = () => {
    // Get URL parameters
    const { type, token } = useParams();
    const navigate = useNavigate();

    // State management
    const [merchant, setMerchant] = useState<IMerchantCoreType>(merchantCore);

    // Determine if this is a token-based route
    const isTokenRoute = !!token && !!type;

    // Effect to extract email from token if available
    useEffect(() => {
        if (isTokenRoute) {
            setMerchant(prev => ({
                ...prev,
            }));
        }
    }, [isTokenRoute, token]);

    const updateMerchant = (data: any) => {
        setMerchant(prev => ({ ...prev, ...data }));
    };

    const handleNext = () => {
        // Navigate to merchant registration with the account data
        // You might want to pass the merchant data through route state or context
        navigate('/register-merchant');
    };

    const handlePrev = () => {
        // Navigate back to login or previous step
        navigate('/login');
    };

    return (
        <div className="bg-white flex flex-col overflow-hidden items-stretch pb-32 px-4 md:px-0">
            {/* Introduction Section */}
            <section className="self-center mt-1.5 w-full max-w-4xl max-md:max-w-full">
                <div className="flex justify-center pb-2 md:pb-4">
                    <img
                        src={logo}
                        alt="ChoQi Logo"
                        className="h-16 md:h-20 lg:h-full object-contain"
                    />
                </div>
                <div className="flex justify-center">
                    <span className="text-[28px] text-[rgba(112,112,112,1)] font-normal">チョキペイは医療機関専用のキャッシュレスサービスです</span>
                </div>
            </section>

            {/* Registration Component */}
            <Registration />
        </div>
    );
};
