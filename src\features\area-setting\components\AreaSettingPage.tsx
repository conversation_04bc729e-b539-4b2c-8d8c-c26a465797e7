import { useAuthStore } from '@/store';
import _ from 'lodash';
import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { areaSettingService } from '../services/areaSettingService';
import { useQueryAgxArea } from '../hooks/useQueryAgxArea';
import { useCreateAgxArea } from '../hooks/useCreateAgxArea';
import { useCreateAgxSubArea } from '../hooks/useCreateAgxSubArea';
import { useDeleteAgxArea } from '../hooks/useDeleteAgxArea';
import { useDeleteAgxSubArea } from '../hooks/useDeleteAgxSubArea';
import { AreaModal } from './AreaModal';
import { SubAreaModal } from './SubAreaModal';
import { Button } from '@/components/ui/button';
import { ConfirmDeleteModal } from './ConfirmDeleteModal';
import { LoadingSpinner } from '@/components/LoadingSpinner';

const initalState = {
  agxAreas: [],
  agxSubAreas: [],
  agxSubAreaModal: [],
  loading: true,
  error: false
}

export const AreaSettingPage = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user.agxMerchantNo;
  const [dataAreaSetting, setDataAreaSetting] = useState(initalState);
  const [agxArea, setAgxArea] = useState({
    agx_areaid: null,
    agxMerchantCreatedNo: agxMerchantNo,
    agxAreaName: '',
    agxSubAreaids: []
  })
  const [agxSubArea, setAgxSubArea] = useState({
    agx_sub_areaid: null,
    agxSubAreaName: '',
    agxMerchantCreatedNo: agxMerchantNo,
    agxMerchantNos: []
  })
  const [showAreaModal, setShowAreaModal] = useState(false);
  const [showSubAreaModal, setShowSubAreaModal] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState<{ type: 'area' | 'subarea' | null, id: string | null }>({ type: null, id: null });
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: GetAreaSettingResponse, isLoading, isError, error, refetch } = useQueryAgxArea({
    agxMerchantNo: user?.agxMerchantNo || "",
  });

  const { createAgxAreaAsync } = useCreateAgxArea();
  const { createAgxSubAreaAsync } = useCreateAgxSubArea();
  const { deleteAgxAreaAsync } = useDeleteAgxArea();
  const { deleteAgxSubAreaAsync } = useDeleteAgxSubArea();

  useEffect(() => {
    getDataAreaSetting();
  }, [GetAreaSettingResponse])

  const getDataAreaSetting = async () => {
    try {
      setDataAreaSetting({
        ...dataAreaSetting,
        agxAreas: GetAreaSettingResponse.agxAreas,
        agxSubAreas: GetAreaSettingResponse.agxSubAreas,
        agxSubAreaModal: GetAreaSettingResponse.agxSubAreaModal,
        loading: false,
        error: false
      });
    } catch (error) {
      setDataAreaSetting({
        ...dataAreaSetting,
        agxAreas: [],
        agxSubAreas: [],
        agxSubAreaModal: [],
        loading: true,
        error: true
      })
    }
  }

  const handleChangeArea = (e) => {
    const value = e.target.value.trim();
    setAgxArea({ ...agxArea, agxAreaName: value })
  }

  const handleChangeSubArea = (e) => {
    const value = e.target.value.trim();
    setAgxSubArea({ ...agxSubArea, agxSubAreaName: value })
  }

  const handleCreateOrUpdateArea = async () => {
    await createAgxAreaAsync(agxArea);
    await refetch();
    setShowAreaModal(false);
  }

  const handleCreateOrUpdateSubArea = async () => {
    await createAgxSubAreaAsync(agxSubArea);
    await refetch();
    setShowSubAreaModal(false);
  }

  const handleDeleteArea = (agxAreaid) => {
    setDeleteDialog({ type: 'area', id: agxAreaid });
    setIsDialogOpen(true);
  };

  const handleDeleteSubArea = (agxSubAreaid) => {
    setDeleteDialog({ type: 'subarea', id: agxSubAreaid });
    setIsDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deleteDialog.type === 'area' && deleteDialog.id) {
      await deleteAgxAreaAsync(deleteDialog.id);
      await refetch();
    } else if (deleteDialog.type === 'subarea' && deleteDialog.id) {
      await deleteAgxSubAreaAsync(deleteDialog.id);
      await refetch();
    }
    setIsDialogOpen(false);
    setDeleteDialog({ type: null, id: null });
  };

  const handleClearCheckBox = () => {
    const clist = document.getElementsByTagName("input");
    for (let i = 0; i < clist.length; ++i) { clist[i].checked = false; }
  }

  const handleClearDataArea = () => {
    const clist = document.getElementsByTagName("input");
    for (let i = 0; i < clist.length; ++i) { clist[i].checked = false; }
    // Reset state to clear all selections
    setAgxArea({
      agx_areaid: null,
      agxMerchantCreatedNo: agxMerchantNo,
      agxAreaName: '',
      agxSubAreaids: []
    })
  }

  const handleClearDataSubArea = () => {
    const clist = document.getElementsByTagName("input");
    for (let i = 0; i < clist.length; ++i) { clist[i].checked = false; }
    // Reset state to clear all selections
    setAgxSubArea({
      agx_sub_areaid: null,
      agxSubAreaName: '',
      agxMerchantCreatedNo: agxMerchantNo,
      agxMerchantNos: []
    })
  }

  const handleSlectAreaDetail = (agxAreaid, agxAreaName) => {
    handleClearCheckBox();
    const arrSubSelect = _.filter(dataAreaSetting?.agxSubAreas, ['agxAreaid', agxAreaid]);
    const arrSelected: { agxSubAreaid: string; isSelected: boolean; isHadArea: boolean }[] = [];
    arrSubSelect.forEach(subArea => {
      const checkbox = document.getElementById(subArea.agxSubAreaid) as HTMLInputElement | null;
      if (checkbox) {
        checkbox.checked = true;
      }
      arrSelected.push({
        agxSubAreaid: subArea.agxSubAreaid,
        isSelected: true,
        isHadArea: true,
      })
    });
    setAgxArea({
      ...agxArea,
      agx_areaid: agxAreaid,
      agxAreaName: agxAreaName,
      agxSubAreaids: arrSelected
    });
    setShowAreaModal(true);
  }

  const handleSelectSubArea = (
    e: React.ChangeEvent<HTMLInputElement>,
    subAreaId: string,
    agxAreaId: string
  ) => {
    const isChecked = e.target.checked;
    const checkbox = document.getElementById(subAreaId) as HTMLInputElement | null;
    if (checkbox) {
      checkbox.checked = isChecked;
    }
    const arrSelected = agxArea.agxSubAreaids || [];
    let updatedSubAreaids: { agxSubAreaid: string; isSelected: boolean; isHadArea: boolean }[];

    if (arrSelected.length > 0) {
      const filteredSubAreaSelected = arrSelected.filter(
        (item: { agxSubAreaid: string }) => item.agxSubAreaid !== subAreaId
      );
      filteredSubAreaSelected.push({
        agxSubAreaid: subAreaId,
        isSelected: isChecked,
        isHadArea: agxAreaId === agxArea.agx_areaid ? true : false,
      });
      updatedSubAreaids = filteredSubAreaSelected;
    } else {
      updatedSubAreaids = [
        {
          agxSubAreaid: subAreaId,
          isSelected: true,
          isHadArea: false,
        },
      ];
    }
    setAgxArea({ ...agxArea, agxSubAreaids: updatedSubAreaids });
  };

  const handleSelectSubAreaDetail = (subAreaid, subAreaName) => {
    // Clear any existing selections first
    handleClearCheckBox();

    // Find merchants that belong to this sub area
    const arrMerchantSelect = _.filter(dataAreaSetting?.agxSubAreaModal, ['agxSubAreaid', subAreaid]);
    const arrSelected: { agxMerchantNo: string; isSelected: boolean; isHadArea: boolean }[] = [];

    // Build the selected merchants array - React will handle the checkbox state
    arrMerchantSelect.forEach(merchant => {
      const checkbox = document.getElementById(merchant.agxMerchantNo) as HTMLInputElement | null;
      if (checkbox) {
        checkbox.checked = true;
      }
      arrSelected.push({
        agxMerchantNo: merchant.agxMerchantNo,
        isSelected: true,
        isHadArea: true,
      })
    });

    // Set the sub area state - this will automatically check the checkboxes via React
    setAgxSubArea({
      ...agxSubArea,
      agx_sub_areaid: subAreaid,
      agxSubAreaName: subAreaName,
      agxMerchantNos: arrSelected
    })

    // Open the modal
    setShowSubAreaModal(true);
  }

  const handleSelectMerchant = (
    event: React.ChangeEvent<HTMLInputElement>,
    merchantNo: string,
    subAreaId: string
  ) => {
    const isChecked = event.target.checked;
    const checkbox = document.getElementById(merchantNo) as HTMLInputElement | null;
    if (checkbox) {
      checkbox.checked = isChecked;
    }
    const arrSelected = agxSubArea.agxMerchantNos || [];

    let updatedMerchantNos: { agxMerchantNo: string; isSelected: boolean; isHadArea: boolean }[];

    if (arrSelected.length > 0) {
      const filteredMerchantSelected = arrSelected.filter(
        (item: { agxMerchantNo: string }) => item.agxMerchantNo !== merchantNo
      );
      filteredMerchantSelected.push({
        agxMerchantNo: merchantNo,
        isSelected: isChecked,
        isHadArea: subAreaId === agxSubArea.agx_sub_areaid ? true : false
      })
      updatedMerchantNos = filteredMerchantSelected;
    } else {
      updatedMerchantNos = [
        {
          agxMerchantNo: merchantNo,
          isSelected: true,
          isHadArea: false
        }
      ];
    }
    setAgxSubArea({ ...agxSubArea, agxMerchantNos: updatedMerchantNos });
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="pt-6 pb-2 px-2">
      <div className="max-full mx-auto px-4">
        {/* Heading */}
        <h1 className=" text-[#6F6F6E] text-[24px] font-bold mb-2">エリア・店舗の登録</h1>
        <p className="text-red-500 mb-4 text-[18px]">※登録が反映されるまでお時間がかかる場合がございます。</p>
        {/* Area Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h3 className="font-semibold mb-2 md:mb-0 text-[#6F6F6E] text-[18px]">エリア</h3>
          <Button
            className="bg-[#1D9987] hover:bg-[#1D9987]/80  text-white font-medium px-8 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-[18px]"
            onClick={() => { handleClearDataArea(); setShowAreaModal(true); }}
          >
            エリアの作成
          </Button>
        </div>
        <div className="overflow-x-auto mb-8">
          <table className="min-w-full bg-white rounded shadow overflow-hidden">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left bg-gray-100  text-[#6F6F6E] text-[18px]">エリア名</th>
                <th className="px-4 py-2 bg-gray-100"></th>
              </tr>
            </thead>
            <tbody>
              {dataAreaSetting?.agxAreas?.map((item, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-2">
                    <div
                      className=" text-[#6F6F6E] text-[18px] hover:underline cursor-pointer"
                      onClick={() => handleSlectAreaDetail(item.agx_areaid, item.agxAreaName)}
                    >
                      {item.agxAreaName}
                    </div>
                  </td>
                  <td className="px-4 py-2">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-3 px-8 rounded transition text-[18px]"
                      onClick={() => handleDeleteArea(item.agx_areaid)}
                    >
                      削除
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {/* SubArea Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h3 className="font-semibold mb-2 md:mb-0 text-[#6F6F6E] text-[18px]">サブエリア</h3>
          <Button
            className="bg-[#1D9987] hover:bg-[#1D9987]/80 text-white font-medium px-8 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-[18px]"
            onClick={() => { handleClearDataSubArea(); setShowSubAreaModal(true); }}
          >
            サブエリアの作成
          </Button>
        </div>
        <div className="overflow-x-auto mb-8">
          <table className="min-w-full bg-white rounded shadow overflow-hidden text-left">
            <thead>
              <tr>
                <th className="px-4 py-2 bg-gray-100  text-[#6F6F6E] text-[18px] w-1/3">サブエリア名</th>
                <th className="px-4 py-2 bg-gray-100  text-[#6F6F6E] text-[18px] w-1/3">エリア名</th>
                <th className="px-4 py-2 bg-gray-100 text-[#6F6F6E] text-[18px] w-1/3"></th>
              </tr>
            </thead>
            <tbody>
              {dataAreaSetting?.agxSubAreas?.map((item, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-2">
                    <div
                      className="text-[#6F6F6E] text-[18px] hover:underline cursor-pointer"
                      onClick={() => handleSelectSubAreaDetail(item.agxSubAreaid, item.agxSubAreaName)}
                    >
                      {item.agxSubAreaName}
                    </div>
                  </td>
                  <td className="px-4 py-2  text-[#6F6F6E] text-[18px]">{item.agxAreaName}</td>
                  <td className="px-4 py-2">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-3 px-8 rounded transition text-[18px]"
                      onClick={() => handleDeleteSubArea(item.agxSubAreaid)}
                    >
                      削除
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {/* Modals */}
        <AreaModal
          agxArea={agxArea}
          showAreaModal={showAreaModal}
          setShowAreaModal={setShowAreaModal}
          handleChangeArea={handleChangeArea}
          handleSelectSubArea={handleSelectSubArea}
          handleCreateOrUpdateArea={handleCreateOrUpdateArea}
          dataAreaSetting={dataAreaSetting}
        />
        <SubAreaModal
          agxSubArea={agxSubArea}
          dataAreaSetting={dataAreaSetting}
          showSubAreaModal={showSubAreaModal}
          setShowSubAreaModal={setShowSubAreaModal}
          handleChangeSubArea={handleChangeSubArea}
          handleSelectMerchant={handleSelectMerchant}
          handleCreateOrUpdateSubArea={handleCreateOrUpdateSubArea}
        />
        <ConfirmDeleteModal
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
          handleConfirmDelete={handleConfirmDelete}
        />
      </div>
    </div>
  )
}
