import axios, { AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';

// Interface cho error response
interface ErrorResponse {
  status: number;
  message: string;
  statusCode: number;
  timestamp: string;
}

// Base URL for API - you can change it according to the environment
const BASE_URL = import.meta.env.VITE_API_URL || '';

/**
 * Create axios instance
 */
const axiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

/**
 * Request interceptor - Add token to header
 */
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Get token from localStorage
    const token = localStorage.getItem('accessToken');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

/**
 * Response interceptor - Handle response and error
 */
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    const { response } = error;

    if (response) {
      const { status } = response;

      // Handle 401 (Unauthorized) and 403 (Forbidden)
      if ((status === 401 || status === 403) && !window.location.pathname.includes('/login')) {
        // Remove token from localStorage
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');

        // Redirect về trang login
        window.location.href = '/login';

        // Có thể sử dụng toast để thông báo lỗi
        console.error('Session expired. Please login again.');
      }

      // Xử lý các lỗi khác
      switch (status) {
        case 400:
          console.error('Bad Request');
          break;
        case 404:
          console.error('Not Found');
          break;
        case 500:
          console.error('Internal Server Error');
          break;
        default: {
          const errorData = response.data as ErrorResponse;
          console.error(`Error ${status}: ${errorData?.message || 'Unknown error'}`);
          break;
        }
      }
    } else if (error.request) {
      // Lỗi network
      console.error('Network Error: Please check your internet connection');
    } else {
      // Lỗi khác
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

/**
 * Set auth token to localStorage
 */
export const setAuthToken = (token: string) => {
  localStorage.setItem('accessToken', token);
};

/**
 * Remove auth token from localStorage
 */
export const removeAuthToken = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('auth-storage');
};

/**
 * Check if auth token exists in localStorage
 */
export const hasAuthToken = (): boolean => {
  return !!localStorage.getItem('accessToken');
};

/**
 * Check if the error is a network error
 */
export const isNetworkError = (error: unknown): boolean => {
  return axios.isAxiosError(error) && !error.response;
};

export default axiosInstance;
