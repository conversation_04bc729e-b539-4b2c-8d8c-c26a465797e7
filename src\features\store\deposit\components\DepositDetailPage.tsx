import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useDepositDetail } from '@/features/store/deposit/hooks/useDepositDetail';
import { formatNumber } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import { PDFTemplates } from '@/services/pdfTemplates';
import { CSVLink } from 'react-csv';
import { CSVExportDetailData } from '../types/depositDetail';

interface DepositDetailPageProps {
  merchantNo: string;
  paymentBId: string;
  transactionType: string;
  transferDate: string;
};

export const DepositDetailPage: React.FC<DepositDetailPageProps> = ({ merchantNo, paymentBId, transactionType, transferDate }) => {
  const { user } = useAuthStore();
  const agxStoreName = user?.agxStoreName || '';
  const {
    depositDetail,
    totalSalesAmount,
    isLoading,
    error
  } = useDepositDetail(
    merchantNo,
    paymentBId,
    transactionType || '',
    transferDate || ''
  );

  // CSV Export data
  const csvHeaders = [
    { label: '', key: 'agxTransactionDate' },
    { label: '', key: 'agxTransactionType' },
    { label: '', key: 'agxSalesAmount' },
    { label: '', key: 'agxPaymentDate' },
    { label: '', key: 'agxMemberId' }
  ];

  const getDataExport = (): CSVExportDetailData[] => {
    const result: CSVExportDetailData[] = [
      {
        agxTransactionDate: `加盟店ID: ${merchantNo}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: `店舗名: ${agxStoreName}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: `件数: ${depositDetail.totalElement}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: `売上額の合計: ${totalSalesAmount}`,
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: '',
        agxTransactionType: '',
        agxSalesAmount: '',
        agxPaymentDate: '',
        agxMemberId: ''
      },
      {
        agxTransactionDate: '利用日',
        agxTransactionType: '取引種別',
        agxSalesAmount: '売上額',
        agxPaymentDate: '振込日',
        agxMemberId: '会員番号'
      }
    ];

    depositDetail.data.forEach(item => {
      result.push({
        agxTransactionDate: item.agxTransactionDate,
        agxTransactionType: `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`,
        agxSalesAmount: item.agxSalesAmount.toString(),
        agxPaymentDate: item.agxPaymentDate,
        agxMemberId: item.agxMemberId
      });
    });

    return result;
  };

  const csvExport = {
    data: getDataExport(),
    headers: csvHeaders,
    filename: `deposit-detail-${transferDate}.csv`,
    separator: ',', // Sử dụng separator tùy chỉnh
    enclosingCharacter: '' // Bỏ dấu ngoặc kép bao quanh
  };

  const handleExportPDF = async () => {
    if (!depositDetail.data || depositDetail.data.length === 0) {
      alert('エクスポートするデータがありません。');
      return;
    }

    try {
      await PDFTemplates.generateDepositDetailPDF({
        detailData: depositDetail.data,
        merchantNo: merchantNo,
        transactionType: transactionType || '',
        datetime: transferDate || '',
        storeName: agxStoreName
      });
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF export failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="px-4 py-6 text-lg text-[#6F6F6E]">
      {/* Page Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-[#6F6F6E]">支払い明細</h1>
      </div>

      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Store Info */}
      <div className="mb-6">
        <div className="space-y-1">
          <div>加盟店ID: {merchantNo}</div>
          <div>店舗名: {agxStoreName}</div>
          <div>件数: {formatNumber(depositDetail.totalElement)}</div>
          <div>売上額の合計: {formatNumber(totalSalesAmount)}</div>
        </div>
      </div>

      {/* Export Buttons */}
      <div className="flex gap-4 mb-6">
        {/* @ts-expect-error : type mismatch due to version node */}
        <CSVLink
          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
          data={csvExport.data}
          headers={csvExport.headers}
          filename={csvExport.filename}
          separator={csvExport.separator}
          enclosingCharacter={csvExport.enclosingCharacter}
        >
          CSV Download
        </CSVLink>
        <Button
          variant="outline"
          onClick={handleExportPDF}
        >
          PDF Download
        </Button>
      </div>

      {/* Data Table */}
      <div className="overflow-x-auto">
        <Table className="w-full">
          <TableHeader>
            <TableRow className="border-b border-gray-200">
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">利用日</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">取引種別</TableHead>
              <TableHead className="px-4 py-2 text-right font-normal text-[#6F6F6E] text-lg">売上額</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">振込日</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">会員番号</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {depositDetail.data.length > 0 ? (
              depositDetail.data.map((item, index) => (
                <TableRow key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.agxTransactionDate}</TableCell>
                  <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">
                    {`${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`}
                  </TableCell>
                  <TableCell className="px-4 py-3 text-right text-[#6F6F6E] text-lg">{formatNumber(item.agxSalesAmount)}</TableCell>
                  <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.agxPaymentDate}</TableCell>
                  <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.agxMemberId}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="px-4 py-8 text-center text-[#6F6F6E]">
                  データがありません
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
