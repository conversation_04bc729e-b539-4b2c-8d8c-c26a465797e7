import { useEffect, useState } from "react";
import { adAddressService } from "../services/addressService";
import { AgxMerchantParams } from "../types";

interface UseAddressReturn {

    // States
    agxStorePostalCode: string;
    stateErrorPostalCode: boolean;
    agxStorePhoneNumber1: string;
    agxStorePhoneNumber2: string;
    agxStorePhoneNumber3: string;
    stateErrorPhoneNumber: boolean;
    agxStoreFaxNumber1: string;
    agxStoreFaxNumber2: string;
    agxStoreFaxNumber3: string;
    stateErrorFaxNumber: boolean;

    isLoadingAddress: boolean;
    handleFindAddress: (setValue: any, trigger: any) => Promise<void>;
    
    // Handlers
    handleChangeAgxStorePostalCode: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxStorePostalCode: (setValue: any, trigger: any) => void;
    handleChangeAgxStorePhoneNumber1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxStorePhoneNumber2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxStorePhoneNumber3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxStorePhoneNumber: (setValue: any, trigger: any) => void;
    handleChangeAgxStoreFaxNumber1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxStoreFaxNumber2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxStoreFaxNumber3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxStoreFaxNumber: (setValue: any, trigger: any) => void;
    setErrorStates: () => void;
    
    // Setters for external access
    setAgxStorePostalCode: (value: string) => void;
    setAgxStorePhoneNumber1: (value: string) => void;
    setAgxStorePhoneNumber2: (value: string) => void;
    setAgxStorePhoneNumber3: (value: string) => void;
    setAgxStoreFaxNumber1: (value: string) => void;
    setAgxStoreFaxNumber2: (value: string) => void;
    setAgxStoreFaxNumber3: (value: string) => void;
}

const useAddressShopInfo = (agxMerchantParams: AgxMerchantParams): UseAddressReturn => {
    // States for handling postal code
    const [agxStorePostalCode, setAgxStorePostalCode] = useState('');
    const [stateErrorPostalCode, setStateErrorPostalCode] = useState(false);
    
    // States for handling phone numbers
    const [agxStorePhoneNumber1, setAgxStorePhoneNumber1] = useState('');
    const [agxStorePhoneNumber2, setAgxStorePhoneNumber2] = useState('');
    const [agxStorePhoneNumber3, setAgxStorePhoneNumber3] = useState('');
    const [stateErrorPhoneNumber, setStateErrorPhoneNumber] = useState(false);
    
    // States for handling fax numbers
    const [agxStoreFaxNumber1, setAgxStoreFaxNumber1] = useState('');
    const [agxStoreFaxNumber2, setAgxStoreFaxNumber2] = useState('');
    const [agxStoreFaxNumber3, setAgxStoreFaxNumber3] = useState('');
    const [stateErrorFaxNumber, setStateErrorFaxNumber] = useState(false);

    const [isLoadingAddress, setIsLoadingAddress] = useState(false);

    const handleFindAddress = async (setValue: any, trigger: any) => {
        setIsLoadingAddress(true);
        try {
            const { data } = await adAddressService.getData(agxStorePostalCode);

            if (data) {
                setValue("agxStorePrefecture", data.kenName);
                setValue("agxStoreAddress1", data.cityTownName);
                setValue("agxStorePhoneticAddress1", data.cityTownFuri);
                
                await trigger(["agxStorePrefecture", "agxStoreAddress1", "agxStorePhoneticAddress1"]);
            } else {
                setValue("agxStorePrefecture", "");
                setValue("agxStoreAddress1", "");
                setValue("agxStorePhoneticAddress1", "");
                
            }
        } catch (error) {
            setValue("agxStorePrefecture", "");
            setValue("agxStoreAddress1", "");
            setValue("agxStorePhoneticAddress1", "");
            await trigger(["agxStorePrefecture", "agxStoreAddress1", "agxStorePhoneticAddress1"]);
        } finally {
            setIsLoadingAddress(false);
        }
    };

    // Handler for postal code input
    const handleChangeAgxStorePostalCode = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        if (value.trim().includes('-')) {
            if (value.trim().length <= 8) {
                setAgxStorePostalCode(value);
            }
        } else {
            if (value.trim().length <= 7) {
                setAgxStorePostalCode(value);
            }
        }
    };

    const handleSetDataAgxStorePostalCode = (setValue: any, trigger: any) => {
        setStateErrorPostalCode(true);
        let value = agxStorePostalCode;
        if (agxStorePostalCode.trim().length > 3 && !agxStorePostalCode.trim().includes('-')) {
            value = agxStorePostalCode.substring(0, 3) + '-' + agxStorePostalCode.substring(3, agxStorePostalCode.trim().length);
        }
        setAgxStorePostalCode(value);
        setValue("agxStorePostalCode", value);
        trigger(["agxStorePostalCode"]);
    };

    // Handlers for phone numbers
    const handleChangeAgxStorePhoneNumber1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxStorePhoneNumber1(e.target.value);
    };
    
    const handleChangeAgxStorePhoneNumber2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxStorePhoneNumber2(e.target.value);
    };
    
    const handleChangeAgxStorePhoneNumber3 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxStorePhoneNumber3(e.target.value);
    };

    const handleSetDataAgxStorePhoneNumber = (setValue: any, trigger: any) => {
        setStateErrorPhoneNumber(true);
        setValue("agxStorePhoneNumber1", agxStorePhoneNumber1.trim());
        setValue("agxStorePhoneNumber2", agxStorePhoneNumber2.trim());
        setValue("agxStorePhoneNumber3", agxStorePhoneNumber3.trim());
        setValue("agxStorePhoneNumber", `${agxStorePhoneNumber1.trim()}-${agxStorePhoneNumber2.trim()}-${agxStorePhoneNumber3.trim()}`);
        trigger(["agxStorePhoneNumber1", "agxStorePhoneNumber2", "agxStorePhoneNumber3"]);
    };

    // Handlers for fax numbers  
    const handleChangeAgxStoreFaxNumber1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxStoreFaxNumber1(e.target.value.trim());
    };
    
    const handleChangeAgxStoreFaxNumber2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxStoreFaxNumber2(e.target.value.trim());
    };
    
    const handleChangeAgxStoreFaxNumber3 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxStoreFaxNumber3(e.target.value.trim());
    };

    const handleSetDataAgxStoreFaxNumber = (setValue: any, trigger: any) => {
        setStateErrorFaxNumber(true);
        setValue("agxStoreFaxNumber1", agxStoreFaxNumber1?.trim());
        setValue("agxStoreFaxNumber2", agxStoreFaxNumber2?.trim());
        setValue("agxStoreFaxNumber3", agxStoreFaxNumber3?.trim());
        setValue("agxStoreFaxNumber", `${agxStoreFaxNumber1?.trim()}-${agxStoreFaxNumber2?.trim()}-${agxStoreFaxNumber3?.trim()}`);
        trigger(["agxStoreFaxNumber1", "agxStoreFaxNumber2", "agxStoreFaxNumber3"]);
    };

    const setErrorStates = () => {
        setStateErrorPostalCode(true);
        setStateErrorPhoneNumber(true);
        setStateErrorFaxNumber(true);
    };

    useEffect(() => {
        if(agxMerchantParams) {
            setAgxStorePostalCode(agxMerchantParams.agxStorePostalCode || "");
            
            // Handle store phone number
            if (agxMerchantParams?.agxStorePhoneNumber) {
                const phoneParts = agxMerchantParams.agxStorePhoneNumber.split("-");
                setAgxStorePhoneNumber1(phoneParts[0] || "");
                setAgxStorePhoneNumber2(phoneParts[1] || "");
                setAgxStorePhoneNumber3(phoneParts[2] || "");
            } else {
                setAgxStorePhoneNumber1("");
                setAgxStorePhoneNumber2("");
                setAgxStorePhoneNumber3("");
            }
            
            // Handle store fax number
            if (agxMerchantParams?.agxStoreFaxNumber) {
                const faxParts = agxMerchantParams.agxStoreFaxNumber.split("-");
                setAgxStoreFaxNumber1(faxParts[0] || "");
                setAgxStoreFaxNumber2(faxParts[1] || "");
                setAgxStoreFaxNumber3(faxParts[2] || "");
            } else {
                setAgxStoreFaxNumber1("");
                setAgxStoreFaxNumber2("");
                setAgxStoreFaxNumber3("");
            }
        }
    }, [agxMerchantParams]);

    return {
        // States
        agxStorePostalCode,
        stateErrorPostalCode,
        agxStorePhoneNumber1,
        agxStorePhoneNumber2,
        agxStorePhoneNumber3,
        stateErrorPhoneNumber,
        agxStoreFaxNumber1,
        agxStoreFaxNumber2,
        agxStoreFaxNumber3,
        stateErrorFaxNumber,
        isLoadingAddress,
        handleFindAddress,
        
        // Handlers
        handleChangeAgxStorePostalCode,
        handleSetDataAgxStorePostalCode,
        handleChangeAgxStorePhoneNumber1,
        handleChangeAgxStorePhoneNumber2,
        handleChangeAgxStorePhoneNumber3,
        handleSetDataAgxStorePhoneNumber,
        handleChangeAgxStoreFaxNumber1,
        handleChangeAgxStoreFaxNumber2,
        handleChangeAgxStoreFaxNumber3,
        handleSetDataAgxStoreFaxNumber,
        setErrorStates,
        
        // Setters for external access
        setAgxStorePostalCode,
        setAgxStorePhoneNumber1,
        setAgxStorePhoneNumber2,
        setAgxStorePhoneNumber3,
        setAgxStoreFaxNumber1,
        setAgxStoreFaxNumber2,
        setAgxStoreFaxNumber3,
    };
};

export default useAddressShopInfo;