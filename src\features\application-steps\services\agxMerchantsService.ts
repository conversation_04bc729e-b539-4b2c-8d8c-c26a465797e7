import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { AgxMerchantParams, AgxMerchantResponse } from "../types";

export const agxMerchantsService = {
    
    getAgxMerchants: async (merchantNo: string): Promise<AgxMerchantResponse> => {
        const response = await apiService.get(API_ENDPOINTS.APPLICATION_STEPS.AGX_MERCHANT_USER_ELEMENT(merchantNo)) as { data: AgxMerchantResponse };
        return response.data;
    },

    updateAgxMerchants: async (merchantNo: string, data: AgxMerchantParams, isSendMail: boolean): Promise<AgxMerchantResponse> => {
        const response = await apiService.put(API_ENDPOINTS.APPLICATION_STEPS.UPDATE_MERCHANT(merchantNo, isSendMail), data) as { data: AgxMerchantResponse };
        return response.data;
    },
};
