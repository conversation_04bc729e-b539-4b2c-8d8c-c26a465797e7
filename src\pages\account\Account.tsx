import { useState } from "react";
import ChangeNameForm from "@/features/account/components/ChangeNameForm";
import ChangeEmailForm from "@/features/account/components/ChangeEmailForm";
import ChangePasswordForm from "@/features/account/components/ChangePasswordForm";
import { EditMode } from "@/features/account/types";

const Account = () => {
  const [editMode, setEditMode] = useState<EditMode>(EditMode.None);

  return (
    <div className="px-3 lg:px-6 py-16 space-y-6">
      {/* <ChangeNameForm editMode={EditMode.None} setEditMode={setEditMode} /> */}
      {/* <ChangeNameForm /> */}
      <ChangeEmailForm editMode={editMode} setEditMode={setEditMode} />
      <ChangePasswordForm editMode={editMode} setEditMode={setEditMode} />
    </div>
  );
}

export default Account;
