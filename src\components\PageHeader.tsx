  import React from 'react';
  import { ProgressBar } from '@/components/ProgressBar';

interface PageHeaderProps {
  title: string;
  progress?: number;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  progress
}) => {
  return (
    <div className="mb-8 md:mb-12 lg:mb-[54px]">
      <h1 className="font-normal text-[#707070] mb-4 md:mb-6 pl-[30px] text-[1.75rem]">
        {title}
      </h1>
      {progress !== undefined && (
        <ProgressBar progress={progress} />
      )}
    </div>
  );
}; 