import Box from "@/components/ui/box";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormButtons } from "./ui/FormButtons";
import { ThreePartNumberInput, PrefectureSelect } from "./ui/AddressInputs";
import { STEP } from "@/constants/common.constant";
import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import useAddressShopInfo from "../hooks/useAddressShopInfo";
import { shopInfoSchema, type ShopInfoFormData } from "@/features/application-steps/schemas/shopInfo.schema";
import ConfirmDialog from "@/components/ConfirmDialog";
import { useAuthStore } from "@/store";
import { FormInput } from "./ui/Field";


const ShopInfo = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {

    const [copyAddressFromCorporate, setCopyAddressFromCorporate] = useState(false);
    const [copyAddressFromRepresentative, setCopyAddressFromRepresentative] = useState(false);
    const [show, setShow] = useState(false);
    // save data store
    const [originalStoreData, setOriginalStoreData] = useState<Partial<AgxMerchantParams> | null>(null);
    const {  updateUser } = useAuthStore()

    const {
        register,
        handleSubmit,
        formState: { errors },
        trigger,
        setValue,
        getValues,
        watch
    } = useForm<ShopInfoFormData>({
        resolver: zodResolver(shopInfoSchema),
        mode: "onBlur",
        reValidateMode: "onBlur",
        defaultValues: {
            agxStoreName: "",
            agxStorePhoneticName: "",
            agxStoreEnglishName: "",
            agxUrl: "",
            agxBrandName: "",
            agxBusinessDate: "",
            agxStoreAddressCopyFlag1: false,
            agxStoreAddressCopyFlag2: false,
            agxStorePhoneNumber1: "",
            agxStorePhoneNumber2: "",
            agxStorePhoneNumber3: "",
            agxStoreFaxNumber1: "",
            agxStoreFaxNumber2: "",
            agxStoreFaxNumber3: "",
        }
    });

    // use address hook
    const {
        // States
        agxStorePostalCode,
        stateErrorFaxNumber,
        agxStorePhoneNumber1,
        agxStorePhoneNumber2,
        agxStorePhoneNumber3,
        stateErrorPhoneNumber,
        agxStoreFaxNumber1,
        agxStoreFaxNumber2,
        agxStoreFaxNumber3,
        isLoadingAddress,
        handleFindAddress,

        // Handlers
        handleChangeAgxStorePostalCode,
        handleSetDataAgxStorePostalCode,
        handleChangeAgxStorePhoneNumber1,
        handleChangeAgxStorePhoneNumber2,
        handleChangeAgxStorePhoneNumber3,
        handleSetDataAgxStorePhoneNumber,
        handleChangeAgxStoreFaxNumber1,
        handleChangeAgxStoreFaxNumber2,
        handleChangeAgxStoreFaxNumber3,
        handleSetDataAgxStoreFaxNumber,

        setAgxStorePostalCode,
        setAgxStorePhoneNumber1,
        setAgxStorePhoneNumber2,
        setAgxStorePhoneNumber3,
        setAgxStoreFaxNumber1,
        setAgxStoreFaxNumber2,
        setAgxStoreFaxNumber3,
    } = useAddressShopInfo(agxMerchantParams);

    // Wrapper handlers to sync react-hook-form with hook state
    const handleStorePhoneNumber1Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxStorePhoneNumber1", value);
        handleChangeAgxStorePhoneNumber1(e);
    };

    const handleStorePhoneNumber2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxStorePhoneNumber2", value);
        handleChangeAgxStorePhoneNumber2(e);
    };

    const handleStorePhoneNumber3Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxStorePhoneNumber3", value);
        handleChangeAgxStorePhoneNumber3(e);
    };

    const handleStoreFaxNumber1Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxStoreFaxNumber1", value);
        handleChangeAgxStoreFaxNumber1(e);
    };

    const handleStoreFaxNumber2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxStoreFaxNumber2", value);
        handleChangeAgxStoreFaxNumber2(e);
    };

    const handleStoreFaxNumber3Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxStoreFaxNumber3", value);
        handleChangeAgxStoreFaxNumber3(e);
    };

    const handleChangeAgxStoreAddressCopyFlag1 = (checked: boolean) => {
        setCopyAddressFromCorporate(checked);

        if (checked) {
            // disable checkbox if it is checked
            if (copyAddressFromRepresentative) {
                setCopyAddressFromRepresentative(false);
                setValue("agxStoreAddressCopyFlag2", false);
            }

            // Copy from corporate information
            const corporateData = agxMerchantParams;
            if (corporateData) {
                // Copy postal code
                setValue("agxStorePostalCode", corporateData.agxCorporatePostalCode || "");

                // Copy prefecture and address
                setValue("agxStorePrefecture", corporateData.agxCorporatePrefecture || "");
                setValue("agxStoreAddress1", corporateData.agxCorporateAddress1 || "");
                setValue("agxStorePhoneticAddress1", corporateData.agxCorporatePhoneticAddress1 || "");
                setValue("agxStoreAddress2", corporateData.agxCorporateAddress2 || "");
                setValue("agxStorePhoneticAddress2", corporateData.agxCorporatePhoneticAddress2 || "");

                // Copy phone number
                if (corporateData.agxCorporatePhoneNumber) {
                    const phoneParts = corporateData.agxCorporatePhoneNumber.split("-");
                    setValue("agxStorePhoneNumber1", phoneParts[0] || "");
                    setValue("agxStorePhoneNumber2", phoneParts[1] || "");
                    setValue("agxStorePhoneNumber3", phoneParts[2] || "");
                } else {
                    setValue("agxStorePhoneNumber1", "");
                    setValue("agxStorePhoneNumber2", "");
                    setValue("agxStorePhoneNumber3", "");
                }

                // Copy fax number
                if (corporateData.agxCorporateFaxNumber) {
                    const faxParts = corporateData.agxCorporateFaxNumber.split("-");
                    setValue("agxStoreFaxNumber1", faxParts[0] || "");
                    setValue("agxStoreFaxNumber2", faxParts[1] || "");
                    setValue("agxStoreFaxNumber3", faxParts[2] || "");
                } else {
                    setValue("agxStoreFaxNumber1", "");
                    setValue("agxStoreFaxNumber2", "");
                    setValue("agxStoreFaxNumber3", "");
                }

                // update state in useAddressShopInfo hook
                if (corporateData.agxCorporatePostalCode) {
                    setAgxStorePostalCode(corporateData.agxCorporatePostalCode);
                }
                if (corporateData.agxCorporatePhoneNumber) {
                    const phoneParts = corporateData.agxCorporatePhoneNumber.split("-");
                    setAgxStorePhoneNumber1(phoneParts[0] || "");
                    setAgxStorePhoneNumber2(phoneParts[1] || "");
                    setAgxStorePhoneNumber3(phoneParts[2] || "");
                }
                if (corporateData.agxCorporateFaxNumber) {
                    const faxParts = corporateData.agxCorporateFaxNumber.split("-");
                    setAgxStoreFaxNumber1(faxParts[0] || "");
                    setAgxStoreFaxNumber2(faxParts[1] || "");
                    setAgxStoreFaxNumber3(faxParts[2] || "");
                }
            }
        }
        trigger([
            "agxStorePostalCode", 
            "agxStorePrefecture", 
            "agxStoreAddress1", 
            "agxStorePhoneticAddress1", 
            "agxStoreAddress2", 
            "agxStorePhoneticAddress2",
            "agxStorePhoneNumber1",
            "agxStorePhoneNumber2",
            "agxStorePhoneNumber3",
            "agxStoreFaxNumber1",
            "agxStoreFaxNumber2",
            "agxStoreFaxNumber3"
        ])

        setValue("agxStoreAddressCopyFlag1", checked);
    };

    const handleChangeAgxStoreAddressCopyFlag2 = (checked: boolean) => {
        setCopyAddressFromRepresentative(checked);

        if (checked) {
            // disable checkbox if it is checked
            if (copyAddressFromCorporate) {
                setCopyAddressFromCorporate(false);
                setValue("agxStoreAddressCopyFlag1", false);
            }

            // Copy from representative information
            const representativeData = agxMerchantParams;

            if (representativeData) {
                // Copy postal code
                setValue("agxStorePostalCode", representativeData.agxRepresentativePostalCode || "");

                // Copy prefecture and address
                setValue("agxStorePrefecture", representativeData.agxRepresentativePrefecture || "");
                setValue("agxStoreAddress1", representativeData.agxRepresentativeAddress1 || "");
                setValue("agxStorePhoneticAddress1", representativeData.agxRepresentativePhoneticAddress1 || "");
                setValue("agxStoreAddress2", representativeData.agxRepresentativeAddress2 || "");
                setValue("agxStorePhoneticAddress2", representativeData.agxRepresentativePhoneticAddress2 || "");

                // Copy phone number
                if (representativeData.agxRepresentativePhoneNumber) {
                    const phoneParts = representativeData.agxRepresentativePhoneNumber.split("-");
                    setValue("agxStorePhoneNumber1", phoneParts[0] || "");
                    setValue("agxStorePhoneNumber2", phoneParts[1] || "");
                    setValue("agxStorePhoneNumber3", phoneParts[2] || "");
                } else {
                    setValue("agxStorePhoneNumber1", "");
                    setValue("agxStorePhoneNumber2", "");
                    setValue("agxStorePhoneNumber3", "");
                }

                // Copy fax number
                if (representativeData.agxRepresentativeFaxNumber) {
                    const faxParts = representativeData.agxRepresentativeFaxNumber.split("-");
                    setValue("agxStoreFaxNumber1", faxParts[0] || "");
                    setValue("agxStoreFaxNumber2", faxParts[1] || "");
                    setValue("agxStoreFaxNumber3", faxParts[2] || "");
                } else {
                    setValue("agxStoreFaxNumber1", "");
                    setValue("agxStoreFaxNumber2", "");
                    setValue("agxStoreFaxNumber3", "");
                }

                // update state in useAddressShopInfo hook
                if (representativeData.agxRepresentativePostalCode) {
                    setAgxStorePostalCode(representativeData.agxRepresentativePostalCode);
                }
                if (representativeData.agxRepresentativePhoneNumber) {
                    const phoneParts = representativeData.agxRepresentativePhoneNumber.split("-");
                    setAgxStorePhoneNumber1(phoneParts[0] || "");
                    setAgxStorePhoneNumber2(phoneParts[1] || "");
                    setAgxStorePhoneNumber3(phoneParts[2] || "");
                }
                if (representativeData.agxRepresentativeFaxNumber) {
                    const faxParts = representativeData.agxRepresentativeFaxNumber.split("-");
                    setAgxStoreFaxNumber1(faxParts[0] || "");
                    setAgxStoreFaxNumber2(faxParts[1] || "");
                    setAgxStoreFaxNumber3(faxParts[2] || "");
                }
            }
        }

        trigger([
            "agxStorePostalCode", 
            "agxStorePrefecture", 
            "agxStoreAddress1", 
            "agxStorePhoneticAddress1", 
            "agxStoreAddress2", 
            "agxStorePhoneticAddress2",
            "agxStorePhoneNumber1",
            "agxStorePhoneNumber2",
            "agxStorePhoneNumber3",
            "agxStoreFaxNumber1",
            "agxStoreFaxNumber2",
            "agxStoreFaxNumber3"
        ])

        setValue("agxStoreAddressCopyFlag2", checked);
    };

    const onSubmit = async () => {

        const isValid = await trigger();
        if (!isValid) {
            return false;
        }

        const formValues = getValues();

        // create full phone number from parts
        const phoneNumber1 = formValues.agxStorePhoneNumber1?.trim() || "";
        const phoneNumber2 = formValues.agxStorePhoneNumber2?.trim() || "";
        const phoneNumber3 = formValues.agxStorePhoneNumber3?.trim() || "";
        const agxStorePhoneNumber = (phoneNumber1 || phoneNumber2 || phoneNumber3) 
            ? `${phoneNumber1}-${phoneNumber2}-${phoneNumber3}` 
            : "";

        // create full fax number from parts
        const faxNumber1 = formValues.agxStoreFaxNumber1?.trim() || "";
        const faxNumber2 = formValues.agxStoreFaxNumber2?.trim() || "";
        const faxNumber3 = formValues.agxStoreFaxNumber3?.trim() || "";
        const agxStoreFaxNumber = (faxNumber1 || faxNumber2 || faxNumber3) 
            ? `${faxNumber1}-${faxNumber2}-${faxNumber3}` 
            : "";

        const updatedValues = {
            ...formValues,
            ...(agxStorePhoneNumber && { agxStorePhoneNumber }),
            ...(agxStoreFaxNumber && { agxStoreFaxNumber })
        };

        setAgxMerchantParams({
            ...(agxMerchantParams || {}),
            ...updatedValues,
        } as AgxMerchantParams);

        setStep(STEP.BANK);
    };

    const showConfirmDialog = async () => {
        const isValid = await trigger();
        if (!isValid) {
            return false;
        }
        setShow(true);
    }

    const onSave = async () => {
        const isValid = await trigger();
        if (!isValid) {
            return false;
        }

        const formValues = getValues();
        
        // create full phone number from parts
        const phoneNumber1 = formValues.agxStorePhoneNumber1?.trim() || "";
        const phoneNumber2 = formValues.agxStorePhoneNumber2?.trim() || "";
        const phoneNumber3 = formValues.agxStorePhoneNumber3?.trim() || "";
        const agxStorePhoneNumber = (phoneNumber1 || phoneNumber2 || phoneNumber3) 
            ? `${phoneNumber1}-${phoneNumber2}-${phoneNumber3}` 
            : "";

        // create full fax number from parts
        const faxNumber1 = formValues.agxStoreFaxNumber1?.trim() || "";
        const faxNumber2 = formValues.agxStoreFaxNumber2?.trim() || "";
        const faxNumber3 = formValues.agxStoreFaxNumber3?.trim() || "";
        const agxStoreFaxNumber = (faxNumber1 || faxNumber2 || faxNumber3) 
            ? `${faxNumber1}-${faxNumber2}-${faxNumber3}` 
            : "";
        
        const updatedValues = {
            ...formValues,
            ...(agxStorePhoneNumber && { agxStorePhoneNumber }),
            ...(agxStoreFaxNumber && { agxStoreFaxNumber })
        };
        
        updateMerchant({ ...(agxMerchantParams || {}), ...updatedValues } as AgxMerchantParams);
        updateUser({ 
            agxStoreName: updatedValues?.agxStoreName, 
            agxStoreEnglishName: updatedValues?.agxStoreEnglishName 
        })
        setShow(false);
    }

    const handleBack = async () => {
        // save current form values to agxMerchantParams before back
        const formValues = getValues();
        
        // create full phone number from parts
        const phoneNumber1 = formValues.agxStorePhoneNumber1?.trim() || "";
        const phoneNumber2 = formValues.agxStorePhoneNumber2?.trim() || "";
        const phoneNumber3 = formValues.agxStorePhoneNumber3?.trim() || "";
        const agxStorePhoneNumber = (phoneNumber1 || phoneNumber2 || phoneNumber3) 
            ? `${phoneNumber1}-${phoneNumber2}-${phoneNumber3}` 
            : "";

        // create full fax number from parts
        const faxNumber1 = formValues.agxStoreFaxNumber1?.trim() || "";
        const faxNumber2 = formValues.agxStoreFaxNumber2?.trim() || "";
        const faxNumber3 = formValues.agxStoreFaxNumber3?.trim() || "";
        const agxStoreFaxNumber = (faxNumber1 || faxNumber2 || faxNumber3) 
            ? `${faxNumber1}-${faxNumber2}-${faxNumber3}` 
            : "";
        
        const updatedValues = {
            ...formValues,
            ...(agxStorePhoneNumber && { agxStorePhoneNumber }),
            ...(agxStoreFaxNumber && { agxStoreFaxNumber })
        };

        setAgxMerchantParams({
            ...(agxMerchantParams || {}),
            ...updatedValues,
        } as AgxMerchantParams);

        updateUser({ 
            agxStoreName: updatedValues?.agxStoreName, 
            agxStoreEnglishName: updatedValues?.agxStoreEnglishName 
        })

        setStep(STEP.DAIHYOUSHA);
    };

    useEffect(() => {
        if (agxMerchantParams) {
            // save store data before set form values
            if (!originalStoreData) {
                setOriginalStoreData({
                    agxStorePostalCode: agxMerchantParams.agxStorePostalCode,
                    agxStorePrefecture: agxMerchantParams.agxStorePrefecture,
                    agxStoreAddress1: agxMerchantParams.agxStoreAddress1,
                    agxStoreAddress2: agxMerchantParams.agxStoreAddress2,
                    agxStorePhoneticAddress1: agxMerchantParams.agxStorePhoneticAddress1,
                    agxStorePhoneticAddress2: agxMerchantParams.agxStorePhoneticAddress2,
                    agxStorePhoneNumber: agxMerchantParams.agxStorePhoneNumber,
                    agxStoreFaxNumber: agxMerchantParams.agxStoreFaxNumber,
                });
            }

            setValue("agxStoreName", agxMerchantParams.agxStoreName || "");
            setValue("agxStorePhoneticName", agxMerchantParams.agxStorePhoneticName || "");
            setValue("agxStoreEnglishName", agxMerchantParams.agxStoreEnglishName || "");
            setValue("agxUrl", agxMerchantParams.agxUrl || "");
            setValue("agxBrandName", agxMerchantParams.agxBrandName || "");
            setValue("agxBusinessDate", agxMerchantParams.agxBusinessDate || "");
            setValue("agxStorePostalCode", agxMerchantParams.agxStorePostalCode || "");
            setValue("agxStorePrefecture", agxMerchantParams.agxStorePrefecture || "");
            setValue("agxStoreAddress1", agxMerchantParams.agxStoreAddress1 || "");
            setValue("agxStoreAddress2", agxMerchantParams.agxStoreAddress2 || "");
            setValue("agxStorePhoneticAddress1", agxMerchantParams.agxStorePhoneticAddress1 || "");
            setValue("agxStorePhoneticAddress2", agxMerchantParams.agxStorePhoneticAddress2 || "");
            
            // set form values and sync with hook state for store phone number
            const phoneNumber1 = agxMerchantParams.agxStorePhoneNumber?.split("-")[0] || "";
            const phoneNumber2 = agxMerchantParams.agxStorePhoneNumber?.split("-")[1] || "";
            const phoneNumber3 = agxMerchantParams.agxStorePhoneNumber?.split("-")[2] || "";
            setValue("agxStorePhoneNumber1", phoneNumber1);
            setValue("agxStorePhoneNumber2", phoneNumber2);
            setValue("agxStorePhoneNumber3", phoneNumber3);
            // sync with hook state
            setAgxStorePhoneNumber1(phoneNumber1);
            setAgxStorePhoneNumber2(phoneNumber2);
            setAgxStorePhoneNumber3(phoneNumber3);
            
            // set form values and sync with hook state for store fax number
            const faxNumber1 = agxMerchantParams.agxStoreFaxNumber?.split("-")[0] || "";
            const faxNumber2 = agxMerchantParams.agxStoreFaxNumber?.split("-")[1] || "";
            const faxNumber3 = agxMerchantParams.agxStoreFaxNumber?.split("-")[2] || "";
            setValue("agxStoreFaxNumber1", faxNumber1);
            setValue("agxStoreFaxNumber2", faxNumber2);
            setValue("agxStoreFaxNumber3", faxNumber3);
            // sync with hook state
            setAgxStoreFaxNumber1(faxNumber1);
            setAgxStoreFaxNumber2(faxNumber2);
            setAgxStoreFaxNumber3(faxNumber3);
            
            setValue("agxStoreAddressCopyFlag1", agxMerchantParams.agxStoreAddressCopyFlag1 || false);
            setValue("agxStoreAddressCopyFlag2", agxMerchantParams.agxStoreAddressCopyFlag2 || false);
            setCopyAddressFromCorporate(agxMerchantParams.agxStoreAddressCopyFlag1 || false);
            setCopyAddressFromRepresentative(agxMerchantParams.agxStoreAddressCopyFlag2 || false);
            setValue("agxRegularHoliday", agxMerchantParams.agxRegularHoliday || "");
            setValue("agxBusinesssHours", agxMerchantParams.agxBusinesssHours || "");
            if (agxMerchantParams.agxStorePostalCode) {
                setAgxStorePostalCode(agxMerchantParams.agxStorePostalCode);
            }
        }
    }, [agxMerchantParams, setValue, setAgxStorePhoneNumber1, setAgxStorePhoneNumber2, setAgxStorePhoneNumber3, setAgxStoreFaxNumber1, setAgxStoreFaxNumber2, setAgxStoreFaxNumber3]);

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <Box className="mx-auto mb-32 md:mb-32 lg:mb-32 mt-4 md:mt-6 lg:mt-[41px] space-y-4 md:space-y-6 lg:space-y-[41px]">
                <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 items-start relative">
                    <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required pt-0 md:pt-2 text-[#6F6F6E]" />
                    <Box className="flex-1 flex flex-col text-[#C44546] text-sm md:text-lg lg:text-[1.375rem]">
                        <p>こちらに記載頂いた情報はレシートなどに印字されます。</p>
                        <p>個人情報などはご入力しないようにご注意ください。</p>
                    </Box>
                </Box>

                <FormInput
                    label="店舗名"
                    name="agxStoreName"
                    placeholder="店舗名（全角）"
                    register={register}
                    errors={errors}
                    required
                />

                <FormInput
                    label="店舗名（カナ)"
                    name="agxStorePhoneticName"
                    placeholder="テンポメイ（全角カナ）"
                    register={register}
                    errors={errors}
                    required
                />

                <FormInput
                    label="英語表記名称"
                    name="agxStoreEnglishName"
                    placeholder="Tempohyoki（半角英数字）"
                    register={register}
                    errors={errors}
                    required
                />

                <FormInput
                    label="WebサイトURL"
                    name="agxUrl"
                    placeholder="Https://tempomei.com（半角英数字）"
                    register={register}
                    errors={errors}
                />

                <FormInput
                    label="ブランド名"
                    name="agxBrandName"
                    placeholder="ブランド名（全角）"
                    register={register}
                    errors={errors}
                />

                <FormInput
                    label="定休日"
                    name="agxRegularHoliday"
                    placeholder="土、日、祝（全角）"
                    register={register}
                    errors={errors}
                />

                <FormInput
                    label="営業時間"
                    name="agxBusinesssHours"
                    placeholder="９：００〜１８：００（全角）"
                    register={register}
                    errors={errors}
                />

                <Box className={`flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 items-start relative ${agxMerchantParams?.agxBusinessForm === 283260000 ? '' : 'hidden'}`}>
                    <Box className="w-full md:w-[22.5%] pt-0 flex justify-start md:justify-end">
                        {/* Mobile layout: checkbox + text inline */}
                        <Box className="flex items-center gap-3 md:hidden">
                            <Checkbox
                                className={`w-[18px] h-[18px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0`}
                                checked={copyAddressFromCorporate}
                                onCheckedChange={(checked) => {
                                    handleChangeAgxStoreAddressCopyFlag1(checked as boolean);
                                }}
                            />
                            <Label className="text-[rgba(112,112,112,1)] cursor-pointer text-sm" onClick={() => {
                                handleChangeAgxStoreAddressCopyFlag1(!copyAddressFromCorporate);
                            }}>
                                法人住所をコピーする
                            </Label>
                        </Box>
                        {/* Desktop layout: only checkbox */}
                        <Checkbox
                            className={`w-[22px] h-[22px] lg:w-[27px] lg:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 hidden md:block`}
                            checked={copyAddressFromCorporate}
                            onCheckedChange={(checked) => {
                                handleChangeAgxStoreAddressCopyFlag1(checked as boolean);
                            }}
                        />
                    </Box>
                    <Box className="w-full md:flex-1 hidden md:block">
                        <Label className="text-[rgba(112,112,112,1)] cursor-pointer text-base lg:text-xl xl:text-[1.75rem] pt-0" onClick={() => {
                            handleChangeAgxStoreAddressCopyFlag1(!copyAddressFromCorporate);
                        }}>
                            法人住所をコピーする
                        </Label>
                    </Box>
                </Box>

                <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 items-start relative">
                    <Box className="w-full md:w-[22.5%] pt-0 flex justify-start md:justify-end">
                        {/* Mobile layout: checkbox + text inline */}
                        <Box className="flex items-center gap-3 md:hidden">
                            <Checkbox
                                className={`w-[18px] h-[18px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0`}
                                checked={copyAddressFromRepresentative}
                                onCheckedChange={(checked) => {
                                    handleChangeAgxStoreAddressCopyFlag2(checked as boolean);
                                }}
                            />
                            <Label className="text-[rgba(112,112,112,1)] cursor-pointer text-sm" onClick={() => {
                                handleChangeAgxStoreAddressCopyFlag2(!copyAddressFromRepresentative);
                            }}>
                                代表者住所をコピーする
                            </Label>
                        </Box>
                        {/* Desktop layout: only checkbox */}
                        <Checkbox
                            className={`w-[22px] h-[22px] lg:w-[27px] lg:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 hidden md:block`}
                            checked={copyAddressFromRepresentative}
                            onCheckedChange={(checked) => {
                                handleChangeAgxStoreAddressCopyFlag2(checked as boolean);
                            }}
                        />
                    </Box>
                    <Box className="w-full md:flex-1 hidden md:block">
                        <Label className="text-[rgba(112,112,112,1)] cursor-pointer text-base lg:text-xl xl:text-[1.75rem] pt-0" onClick={() => {
                            handleChangeAgxStoreAddressCopyFlag2(!copyAddressFromRepresentative);
                        }}>
                            代表者住所をコピーする
                        </Label>
                    </Box>
                </Box>

                <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                    <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                        郵便番号<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                    </Label>
                    <Box className="w-full md:w-[250px] lg:w-[388px] flex flex-col">
                        <input
                            data-pattern="^(\d{7}|\d{3}-\d{4})$"
                            {...register("agxStorePostalCode")}
                            onChange={handleChangeAgxStorePostalCode}
                            onBlur={() => handleSetDataAgxStorePostalCode(setValue, trigger)}
                            maxLength={8}
                            disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                            value={agxStorePostalCode}
                            className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                            placeholder="000-0000"
                        />
                        <div className="min-h-[16px] md:min-h-[20px] mt-1">
                            {errors.agxStorePostalCode && (
                                <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                                    {errors.agxStorePostalCode.message}
                                </span>
                            )}
                        </div>
                    </Box>
                    <Box className="w-full md:flex-1">
                        <Button
                            className="bg-[#A5A4A6] h-[40px] md:h-[50px] lg:h-[66px] w-full md:w-[200px] lg:w-[323px] text-sm md:text-base lg:text-[1.75rem] hover:bg-[#1D9987] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 rounded-lg transition-colors shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                            onClick={() => handleFindAddress(setValue, trigger)}
                            disabled={isLoadingAddress || !agxStorePostalCode || copyAddressFromCorporate || copyAddressFromRepresentative}
                            type="button"
                        >
                            {isLoadingAddress ? "検索中..." : "住所情報を入力"}
                        </Button>
                    </Box>
                </Box>

                {/* Select prefecture */}
                <PrefectureSelect
                    label="都道府県"
                    required
                    value={watch("agxStorePrefecture")}
                    onChange={(e) => setValue("agxStorePrefecture", e.target.value)}
                    error={errors.agxStorePrefecture?.message}
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                <FormInput
                    label="市町村"
                    name="agxStoreAddress1"
                    placeholder="〇〇市〇〇区〇〇（全角）"
                    register={register}
                    errors={errors}
                    required
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                <FormInput
                    label="市町村（カナ)"
                    name="agxStorePhoneticAddress1"
                    placeholder="マルマルシマルマルクマルマル（全角カナ）"
                    register={register}
                    errors={errors}
                    required
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                <FormInput
                    label="丁目番地建物名"
                    name="agxStoreAddress2"
                    placeholder="１丁目２ー３（全角）"
                    register={register}
                    errors={errors}
                    required
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                <FormInput
                    label="丁目番地建物名（カナ)"
                    name="agxStorePhoneticAddress2"
                    placeholder="１チョウメ２ー３（全角カナ）"
                    register={register}
                    errors={errors}
                    required
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                {/* Phone number */}
                <ThreePartNumberInput
                    label="電話番号"
                    required
                    value1={agxStorePhoneNumber1}
                    value2={agxStorePhoneNumber2}
                    value3={agxStorePhoneNumber3}
                    onChange1={handleStorePhoneNumber1Change}
                    onChange2={handleStorePhoneNumber2Change}
                    onChange3={handleStorePhoneNumber3Change}
                    onBlur={() => handleSetDataAgxStorePhoneNumber(
                        setValue,
                        trigger,
                    )}
                    error={errors.agxStorePhoneNumber1?.message || errors.agxStorePhoneNumber2?.message || errors.agxStorePhoneNumber3?.message}
                    showError={stateErrorPhoneNumber || !!(errors.agxStorePhoneNumber1 || errors.agxStorePhoneNumber2 || errors.agxStorePhoneNumber3)}
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                {/* FAX */}
                <ThreePartNumberInput
                    label="FAX"
                    required={false}
                    value1={agxStoreFaxNumber1}
                    value2={agxStoreFaxNumber2}
                    value3={agxStoreFaxNumber3}
                    onChange1={handleStoreFaxNumber1Change}
                    onChange2={handleStoreFaxNumber2Change}
                    onChange3={handleStoreFaxNumber3Change}
                    onBlur={() => handleSetDataAgxStoreFaxNumber(
                        setValue,
                        trigger,
                    )}
                    error={errors.agxStoreFaxNumber1?.message || errors.agxStoreFaxNumber2?.message || errors.agxStoreFaxNumber3?.message}
                    showError={stateErrorFaxNumber || !!(errors.agxStoreFaxNumber1 || errors.agxStoreFaxNumber2 || errors.agxStoreFaxNumber3)}
                    disabled={copyAddressFromCorporate || copyAddressFromRepresentative}
                />

                {/* Form buttons */}
                <FormButtons
                    onSave={showConfirmDialog}
                    onNext={onSubmit}
                    isSubmitting={isUpdating}
                    showBackButton={true}
                    onBack={handleBack}
                />
            </Box>
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                title="入力内容を一時保存します。"
                cancelLabel="戻る"
                confirmLabel="一時保存"
                confirmVariant="danger"
                onCancel={() => setShow(false)}
                onConfirm={onSave}
            />
        </form>
    )
}

export default ShopInfo;