import { useMutation, useQuery } from "@tanstack/react-query"
import { agxMerchantsService } from "../services/agxMerchantsService";
import { AgxMerchantParams, AgxMerchantResponse } from "../types";
import { useAuthStore } from "@/store";
import { toast } from "sonner";

type UpdateMerchantInput = {
    data: AgxMerchantParams;
    isSendMail?: boolean;
  };

export const useAgxMerchants = () => {

    const { user } = useAuthStore();

    const merchantNo = btoa(user?.agxMerchantNo || '')

    const { data: merchant, isLoading, error } = useQuery<AgxMerchantResponse>  ({
        queryKey: ["merchant"],
        queryFn: () => {
            return agxMerchantsService.getAgxMerchants(merchantNo);
        },
        staleTime: 0,
        gcTime: 0,
    });

    const { mutateAsync: updateMerchantAsync, isPending: isUpdating } = useMutation<AgxMerchantResponse, Error, UpdateMerchantInput>({
        mutationFn: (data) => {
            return agxMerchantsService.updateAgxMerchants(merchantNo, data.data, data.isSendMail)
        },
        onSuccess: () => {
            toast.success("更新は成功しました。情報の確認には1～2日かかりますのでご了承ください。");
        },
        onError: () => {
            toast.error("更新に失敗しました。もう一度お試しください。");
        },
    })

    // Wrapper function that can be awaited
    const updateMerchant = async (data: AgxMerchantParams, isSendMail?: boolean): Promise<void> => {
        await updateMerchantAsync({ data, isSendMail });
    };

    return {
        user,
        merchant,
        isLoading,
        isUpdating,
        error,
        updateMerchant,
    };
};