import { PDFService, PDFConfig } from './pdfService';
import { formatNumber, formatDateJapan, formatDatePDF } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import { DepositDetailItem, DepositDetailAllData, DepositData } from '@/features/store/deposit/types/depositDetail';
import { AdminDepositData, AdminDepositDetailData } from '@/features/adminStore/adminDeposit/types';

export interface DepositPDFData {
  merchantNo: string;
  storeName: string;
  transferDate: string;
  data: any;
}

export class PDFTemplates {
  /**
   * Generate PDF for Admin Deposit (multi-store management)
   */
  static async generateAdminDepositPDF(params: {
    data: any;
    transferDate: string;
    merchantNos: string[];
    storeName: string;
  }): Promise<void> {
    const { data, transferDate, merchantNos, storeName } = params;
    const dateTitle = formatDatePDF(new Date());
    const displayMerchantNos = merchantNos.join(', ');

    // Build table data
    const tableHeaders = [
      '利用日', '取引種別', '件数', '売上額', '手数料率', '手数料', '税', '振込額'
    ];

    const tableData = data.paymentBreakdowns?.map((item: any) => [
      formatDateJapan(item.agxTransactionDate),
      `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`,
      formatNumber(item.agxNumberOfSales),
      formatNumber(item.agxSalesAmount),
      `${item.agxTotalFeeRate}%`,
      formatNumber(item.agxTotalFee),
      `(${item.tax})`,
      formatNumber(item.agxPaymentAmount)
    ]) || [];

    const content = [
      ...PDFService.createDepositHeader(
        '振込明細（多店舗管理）',
        displayMerchantNos,
        storeName,
        formatDateJapan(transferDate),
        dateTitle
      ),
      {
        text: `総振込額: ${formatNumber(data.total || 0)}`,
        margin: [0, 0, 0, 10],
        fontSize: 12,
        bold: true
      },
      PDFService.createTable(
        tableHeaders,
        tableData,
        ['auto', 'auto', 40, '*', '*', '*', 60, '*']
      )
    ];

    const config: PDFConfig = {
      content,
      filename: `admin-deposit-${transferDate}.pdf`
    };

    await PDFService.createPDF(config);
  }

  /**
   * Generate PDF for Store Deposit (Crepico/Paygate)
   */
  static async generateStoreDepositPDF(params: {
    depositData: DepositData;
    transferDate: string;
    merchantNo: string;
    storeName: string;
    type: string;
  }): Promise<void> {
    const { depositData, transferDate, merchantNo, storeName } = params;
    const dateTitle = formatDatePDF(new Date());
    const switchLayoutDate = "2023-11-05";

    // Helper function to get summary table data
    const getRecordTotalPDF = () => {
      if (depositData?.merchantPayments?.length > 0) {
        if (depositData?.merchantPayments[0]?.agxInvoiceFlg !== 283260002) {
          const result = [[
            { text: '売上件数', alignment: 'right' },
            { text: '売上金額', alignment: 'right' },
            { text: '手数料額', alignment: 'right' },
            { text: '（内消費税額）', alignment: 'center' },
            { text: '月額費用', alignment: 'right' },
            { text: '振込額', alignment: 'left' },
            { text: '※', alignment: 'center' }
          ]];
          result.push([
            { text: formatNumber(depositData?.merchantPayments[0]?.agxNumberOfSales), alignment: 'right' },
            { text: formatNumber(depositData?.merchantPayments[0]?.agxSalesAmount), alignment: 'right' },
            { text: formatNumber(depositData?.merchantPayments[0]?.agxTotalFee), alignment: 'right' },
            { text: "(" + ((Number(depositData?.merchantPayments[0].agxSettlementCompanyTax) + Number(depositData?.merchantPayments[0].agxInHouseTax))) + ")", alignment: 'center' },
            { text: formatNumber(depositData?.total), alignment: 'right' },
            { text: formatNumber(depositData?.merchantPayments[0]?.agxPaymentAmount), alignment: 'left' },
            { text: depositData?.merchantPayments[0]?.agxInvoiceFlg === ********* ? '★' : '', alignment: 'center' }
          ]);
          return result;
        } else {
          const result = [[
            { text: '売上件数', alignment: 'right' },
            { text: '売上金額', alignment: 'right' },
            { text: '手数料額', alignment: 'right' },
            { text: '（内消費税額）', alignment: 'center' },
            { text: '振込額', alignment: 'left' },
            { text: '※', alignment: 'center' }
          ]];
          result.push([
            { text: formatNumber(depositData?.merchantPayments[0]?.agxNumberOfSales), alignment: 'right' },
            { text: formatNumber(depositData?.merchantPayments[0]?.agxSalesAmount), alignment: 'right' },
            { text: formatNumber(depositData?.merchantPayments[0]?.agxTotalFee), alignment: 'right' },
            { text: "(" + ((Number(depositData?.merchantPayments[0].agxSettlementCompanyTax) + Number(depositData?.merchantPayments[0].agxInHouseTax))) + ")", alignment: 'center' },
            { text: formatNumber(depositData?.merchantPayments[0]?.agxPaymentAmount), alignment: 'left' },
            { text: Number(depositData?.merchantPayments[0]?.agxInvoiceFlg) === ********* ? '★' : '', alignment: 'center' }

          ]);
          return result;
        }
      } else {
        const result = [[
          { text: '売上件数', alignment: 'right' },
          { text: '売上金額', alignment: 'right' },
          { text: '手数料額', alignment: 'right' },
          { text: '（内消費税額）', alignment: 'right' },
          { text: '振込額', alignment: 'right' },
          { text: '※', alignment: 'center' }
        ]];
        return result;
      }
    };

    // Helper function to get detail table data
    const getRecordDetailPDF = () => {
      const result = [[
        { text: '取引区分', alignment: 'left'},
        { text: '売上件数', alignment: 'right'},
        { text: '売上金額', alignment: 'right'},
        { text: '手数料率', alignment: 'right'},
        { text: '手数料額', alignment: 'right'},
        { text: '（内消費税額）', alignment: 'right'},
        { text: '振込額', alignment: 'right'}
      ]];
      depositData?.paymentBreakdowns?.forEach(e => {
        result.push([
          { text: e.agxTransactionType ? `${mapTransactionType.get(e.agxTransactionType)}${e.groupCodeName}` : null, alignment: 'left'},
          { text: formatNumber(e.agxNumberOfSales), alignment: 'right'},
          { text: formatNumber(e.agxSalesAmount), alignment: 'right'},
          { text: Number(e.agxTotalFeeRate).toFixed(2) + '%', alignment: 'right'},
          { text: formatNumber(e.agxTotalFee), alignment: 'right'},
          { text: "(" + formatNumber(e.tax) + ")", alignment: 'right'},
          { text: formatNumber(e.agxPaymentAmount), alignment: 'right'}
        ]);
      });
      return result;
    };

    // Helper function to get extra detail table for newer dates
    const getExtraDetailPDF = () => {
      const result = [];

      result.push([
        { text: '非課税小計', alignment: 'left', fontSize: 11 },
        { text: formatNumber(depositData.subTotalNonTax), alignment: 'right', fontSize: 11 },
      ]);

      result.push([
        { text: '10%小計（税込）', alignment: 'left', fontSize: 11 },
        { text: formatNumber(depositData.subTotalInclTax10), alignment: 'right', fontSize: 11 },
      ]);

      result.push([
        { text: '内消費税額', alignment: 'left', fontSize: 11 },
        { text: formatNumber(depositData.subTotalConsumptionTax), alignment: 'right', fontSize: 11 },
      ]);

      result.push([
        { text: '合計（税込）', alignment: 'left', fontSize: 11 },
        { text: formatNumber(depositData.subTotalTaxIncl), alignment: 'right', fontSize: 11 },
      ]);

      return result;
    };

    const recordsDetail = getRecordDetailPDF();
    const recordsTotal = getRecordTotalPDF();
    const extraTable = getExtraDetailPDF();

    const date = formatDateJapan(transferDate);

    const content: any[] = [
      { text: dateTitle, alignment: 'right', margin: [0, 0, 0, 5], fontSize: 10 },
      { text: '振込一覧', fontSize: 22, alignment: 'center', margin: [0, 0, 0, 5] },
      {
        margin: [0, 20, 0, 0],
        columns: [
          {
            width: '*',
            stack: [
              { text: '振込日：' + date, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `店舗名: ${storeName}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `チョキペイ加盟店番号: ${merchantNo}`, margin: [0, 0, 0, 3], fontSize: 11 }
            ]
          },
          {
            width: 'auto',
            stack: []
          }
        ]
      },
      {
        margin: [0, 20, 0, 0],
        table: {
          body: recordsTotal
        }
      },
      {
        width: '*',
        stack: [
          { text: '※毎月初の振込は、売上金額より月額費用を差し引いております。売上金額が月額費用未満であった場合、', fontSize: 10, margin: [0, 10, 0, 3] },
          { text: '「★」でお知らせします（別途月額費用を、弊社宛にお支払いする必要があります）。', fontSize: 10, margin: [0, 0, 0, 3] }
        ]
      },
      {
        margin: [0, 20, 0, 0],
        table: {
          widths: [ 'auto', 40, 'auto', '*', '*', '*', '*'],
          body: recordsDetail
        },
        fontSize : 8,
        layout: 'lightHorizontalLines'
      }
    ];

    // Add qualified business registration number and extra table for newer dates
    if(transferDate > switchLayoutDate) {
      content[2].columns[1].stack.push(
        { text: '適格事業者登録番号', margin: [0, 0, 0, 3], fontSize: 11 },
        { text: '(T6120001228218)', margin: [0, 0, 0, 3], fontSize: 11 }
      );

      content.push(
        {
          margin: [0, 20, 0, 0],
          columns: [
            {
              width: '*',
              stack: []
            },
            {
              width: 'auto',
              stack: [
                {
                  table: {
                    body: extraTable
                  },
                  layout: 'noBorders'
                }
              ]
            }
          ],
        }
      );
    }

    // Add bank information
    content[2].columns[1].stack.push(
      { text: '振込先金融機関', margin: [0, 0, 0, 3], fontSize: 11 },
      { text: `金融機関名: ${depositData?.merchantPayments[0]?.agxBankName ? depositData?.merchantPayments[0]?.agxBankName : ''}`, margin: [0, 0, 0, 3], fontSize: 11 },
      { text: `支店名: ${depositData?.merchantPayments[0]?.agxBranchName ? depositData?.merchantPayments[0]?.agxBranchName : ''}`, margin: [0, 0, 0, 3], fontSize: 11 },
      { text: `口座種別: ${depositData?.merchantPayments[0]?.agxAcccountType ? depositData?.merchantPayments[0]?.agxAcccountType : ''}`, margin: [0, 0, 0, 3], fontSize: 11 },
      { text: `口座番号: ${depositData?.merchantPayments[0]?.agxAccountNo ? depositData?.merchantPayments[0]?.agxAccountNo : ''}`, margin: [0, 0, 0, 3], fontSize: 11 },
      { text: `名義人: ${depositData?.merchantPayments[0]?.agxAccountHolder ? depositData?.merchantPayments[0]?.agxAccountHolder : ''}`, margin: [0, 0, 0, 3], fontSize: 11 }
    );

    const config: PDFConfig = {
      content,
      filename: `deposit-${transferDate}.pdf`
    };

    await PDFService.createPDF(config);
  }

  /**
   * Generate PDF for Deposit Detail (specific transaction type)
   */
  static async generateDepositDetailPDF(params: {
    detailData: DepositDetailItem[];
    merchantNo: string;
    transactionType: string;
    datetime: string;
    storeName: string;
  }): Promise<void> {
    const { detailData, merchantNo, transactionType, datetime, storeName } = params;
    const dateTitle = formatDatePDF(new Date());
    const totalSales = detailData.reduce((sum, item) => sum + item.agxSalesAmount, 0);

    // Build table data with proper formatting like the old code
    const tableBody = [
      [
        { text: '利用日', alignment: 'left', fontSize: 11 },
        { text: '取引種別', alignment: 'left', fontSize: 11 },
        { text: '売上額', alignment: 'right', fontSize: 11 },
        { text: '振込日', alignment: 'left', fontSize: 11 },
        { text: '会員番号', alignment: 'left', fontSize: 11 }
      ]
    ];

    detailData.forEach(item => {
      tableBody.push([
        { text: item.agxTransactionDate, alignment: 'left', fontSize: 11 },
        { text: `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`, alignment: 'left', fontSize: 11 },
        { text: formatNumber(item.agxSalesAmount), alignment: 'right', fontSize: 11 },
        { text: item.agxPaymentDate, alignment: 'left', fontSize: 11 },
        { text: item.agxMemberId, alignment: 'left', fontSize: 11 }
      ]);
    });

    const content = [
      { text: dateTitle, alignment: 'right', margin: [0, 0, 0, 5], fontSize: 11 },
      { text: '振込一覧', fontSize: 22, alignment: 'center', margin: [0, 0, 0, 5] },
      {
        margin: [0, 20, 0, 0],
        columns: [
          {
            width: '*',
            stack: [
              { text: `加盟店ID: ${merchantNo}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `店舗名: ${storeName}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `件数: ${formatNumber(detailData.length)}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `売上額の合計: ${formatNumber(totalSales)}`, margin: [0, 0, 0, 3], fontSize: 11 }
            ]
          }
        ]
      },
      {
        margin: [0, 20, 0, 0],
        table: {
          body: tableBody
        },
        layout: 'lightHorizontalLines'
      }
    ];

    const config: PDFConfig = {
      pageSize: 'A4',
      pageOrientation: 'portrait',
      defaultStyle: {
        font: 'IPAEXGothic'
      },
      content,
      filename: `deposit-detail-${transactionType}-${datetime}.pdf`
    };

    await PDFService.createPDF(config);
  }

  /**
   * Generate PDF for Deposit Detail All (all transactions for a date)
   */
  static async generateDepositDetailAllPDF(params: {
    detailData: DepositDetailAllData;
    paymentDate: string;
    merchantNo: string;
    storeName: string;
  }): Promise<void> {
    const { detailData, paymentDate, merchantNo, storeName } = params;
    const dateTitle = formatDatePDF(new Date());

    // Build table data with proper formatting like the old code
    const tableBody = [
      [
        { text: '利用日', alignment: 'left', fontSize: 11 },
        { text: '取引種別', alignment: 'left', fontSize: 11 },
        { text: '売上額', alignment: 'right', fontSize: 11 },
        { text: '振込日', alignment: 'left', fontSize: 11 },
        { text: '会員番号', alignment: 'left', fontSize: 11 }
      ]
    ];

    detailData.data.forEach(item => {
      tableBody.push([
        { text: item.agxTransactionDate, alignment: 'left', fontSize: 11 },
        { text: `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`, alignment: 'left', fontSize: 11 },
        { text: formatNumber(item.agxSalesAmount), alignment: 'right', fontSize: 11 },
        { text: item.agxPaymentDate, alignment: 'left', fontSize: 11 },
        { text: item.agxMemberId, alignment: 'left', fontSize: 11 }
      ]);
    });

    const content = [
      { text: dateTitle, alignment: 'right', margin: [0, 0, 0, 5], fontSize: 11 },
      { text: '振込一覧', fontSize: 22, alignment: 'center', margin: [0, 0, 0, 5] },
      {
        margin: [0, 0, 0, 0],
        columns: [
          {
            width: '*',
            stack: [
              { text: `加盟店ID: ${merchantNo}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `店舗名: ${storeName}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `件数: ${formatNumber(detailData.total)}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `売上額の合計: ${formatNumber(detailData.totalSales)}`, margin: [0, 0, 0, 3], fontSize: 11 }
            ]
          }
        ]
      },
      {
        margin: [0, 20, 0, 0],
        table: {
          body: tableBody
        },
        layout: 'lightHorizontalLines'
      }
    ];

    const config: PDFConfig = {
      pageSize: 'A4',
      pageOrientation: 'portrait',
      defaultStyle: {
        font: 'IPAEXGothic'
      },
      content,
      filename: `deposit-detail-${paymentDate}.pdf`
    };

    await PDFService.createPDF(config);
  }

  /**
   * Generate PDF for Admin Deposit List (multi-store management)
   */
  static async generateAdminDepositListPDF(params: {
    data: AdminDepositData;
    transferDate: string;
    areaName: string;
    subAreaName: string;
    merchantName: string;
    switchLayoutDate: string;
  }): Promise<void> {
    const { data, transferDate, areaName, subAreaName, merchantName, switchLayoutDate } = params;
    const dateTitle = formatDatePDF(new Date());
    const transferDateFormatted = formatDateJapan(transferDate);

    // Helper function to format numbers
    const formatNum = (num: number | undefined | null): string => {
      if (num === undefined || num === null) return '0';
      return num.toLocaleString();
    };

    // Build total summary table
    const totalRecord = data.agxMerchantPayments?.[data.agxMerchantPayments.length - 1];
    const totalTable = [
      [
        { text: '売上件数', alignment: 'left' },
        { text: '売上金額', alignment: 'left' },
        { text: '手数料額', alignment: 'left' },
        { text: '（内消費税）', alignment: 'left' },
        { text: '月額費用', alignment: 'left' },
        { text: '振込額', alignment: 'left' }
      ],
      [
        { text: formatNum(totalRecord?.numberOfSales), alignment: 'right' },
        { text: formatNum(totalRecord?.salesAmount), alignment: 'right' },
        { text: formatNum(totalRecord?.totalFee), alignment: 'right' },
        { text: `(${formatNum(totalRecord?.sumTax)})`, alignment: 'center' },
        { text: formatNum(totalRecord?.invoiceAmount), alignment: 'right' },
        { text: formatNum(totalRecord?.paymentAmount), alignment: 'right' }
      ]
    ];

    // Build merchant payments table
    const merchantPaymentsHeaders = [
      { text: '加盟店番号', alignment: 'left' },
      { text: '加盟店名', alignment: 'left' },
      { text: '売上件数', alignment: 'left' },
      { text: '売上金額', alignment: 'left' },
      { text: '手数料額', alignment: 'left' },
      { text: '（内消費税）', alignment: 'center' },
      { text: '月額費用', alignment: 'left' },
      { text: '振込額', alignment: 'left' }
    ];

    const merchantPaymentsData = data.agxMerchantPayments?.slice(0, -1).map(item => [
      { text: item.merchantNo || '', alignment: 'left' },
      { text: item.storeName || '', alignment: 'left' },
      { text: formatNum(item.numberOfSales), alignment: 'right' },
      { text: formatNum(item.salesAmount), alignment: 'right' },
      { text: formatNum(item.totalFee), alignment: 'right' },
      { text: `(${formatNum(item.sumTax)})`, alignment: 'right' },
      { text: formatNum(item.invoiceAmount), alignment: 'right' },
      { text: formatNum(item.paymentAmount), alignment: 'right' }
    ]) || [];

    // Build payment breakdowns table
    const breakdownHeaders = [
      { text: '取引区分', alignment: 'left' },
      { text: '加盟店番号', alignment: 'left' },
      { text: '売上件数', alignment: 'right' },
      { text: '売上金額', alignment: 'left' },
      { text: '手数料率', alignment: 'left' },
      { text: '手数料額', alignment: 'left' },
      { text: '（内消費税額）', alignment: 'right' },
      { text: '振込額', alignment: 'left' }
    ];

    const breakdownData = data.agxPaymentBreakdowns?.map(item => [
      { text: `${mapTransactionType?.get(item?.agxTransactionType)}${item.groupCodeName}`, alignment: 'left' },
      { text: `${item.agxStoreName}-${item.agxMerchantNo}`, alignment: 'left' },
      { text: formatNum(item.agxNumberOfSales), alignment: 'right' },
      { text: formatNum(item.agxSalesAmount), alignment: 'right' },
      { text: `${formatNum(item.agxTotalFeeRate)}%`, alignment: 'right' },
      { text: formatNum(item.agxTotalFee), alignment: 'right' },
      { text: `(${formatNum(item.agxInHouseTax)})`, alignment: 'right' },
      { text: formatNum(item.agxPaymentAmount), alignment: 'right' }
    ]) || [];

    // Build content
    const content: any[] = [
      { text: dateTitle, alignment: 'right', margin: [0, 0, 0, 5] },
      { text: '振込一覧', fontSize: 24, alignment: 'center', margin: [0, 0, 0, 5] },
      {
        margin: [0, 20, 0, 0],
        columns: [
          {
            width: '*',
            stack: [
              { text: '振込日 ：' + transferDateFormatted, margin: [0, 0, 0, 3] },
              { text: `エリア名 ： ${areaName}`, margin: [0, 0, 0, 3] },
              { text: `サブエリア名 ： ${subAreaName}`, margin: [0, 0, 0, 3] },
              { text: `加盟店名 ： ${merchantName}`, margin: [0, 0, 0, 3] }
            ]
          }
        ]
      },
      {
        margin: [0, 20, 0, 0],
        table: {
          body: totalTable
        }
      },
      {
        margin: [0, 20, 0, 0],
        table: {
          body: [merchantPaymentsHeaders, ...merchantPaymentsData]
        },
        fontSize: 8,
        layout: 'lightHorizontalLines'
      },
      {
        margin: [0, 40, 0, 0],
        table: {
          widths: ['auto', 'auto', 40, '*', '*', '*', 60, '*'],
          body: [breakdownHeaders, ...breakdownData]
        },
        fontSize: 8,
        layout: 'lightHorizontalLines'
      }
    ];

    // Add extra table for newer dates
    if (transferDate > switchLayoutDate) {
      const extraTable = [
        [
          { text: '非課税小計', alignment: 'left', fontSize: 11 },
          { text: formatNum(data.subTotalNonTax), alignment: 'right', fontSize: 11 }
        ],
        [
          { text: '10%小計（税込）', alignment: 'left', fontSize: 11 },
          { text: formatNum(data.subTotalInclTax10), alignment: 'right', fontSize: 11 }
        ],
        [
          { text: '内消費税額', alignment: 'left', fontSize: 11 },
          { text: formatNum(data.subTotalConsumptionTax), alignment: 'right', fontSize: 11 }
        ],
        [
          { text: '合計（税込）', alignment: 'left', fontSize: 11 },
          { text: formatNum(data.subTotalTaxIncl), alignment: 'right', fontSize: 11 }
        ]
      ];

      content.push({
        margin: [0, 20, 0, 0],
        columns: [
          { width: '*', stack: [] },
          {
            width: 'auto',
            table: {
              body: extraTable
            },
            layout: 'noBorders'
          }
        ]
      });
    }

    const config: PDFConfig = {
      content,
      filename: `deposit-${transferDate}.pdf`
    };

    await PDFService.createPDF(config);
  }

  /**
   * Generate PDF for Admin Deposit Detail (multi-store management)
   */
  static async generateAdminDepositDetailPDF(params: {
    data: AdminDepositDetailData;
    transferDate: string;
    merchantNos: string;
  }): Promise<void> {
    const { data, transferDate, merchantNos } = params;
    const dateTitle = formatDatePDF(new Date());

    // Helper function to format numbers
    const formatNum = (num: number | undefined | null): string => {
      if (num === undefined || num === null) return '0';
      return num.toLocaleString();
    };

    // Build table headers
    const tableHeaders = [
      { text: '利用日', alignment: 'left' },
      { text: '加盟店ID', alignment: 'left' },
      { text: '加盟店名', alignment: 'left' },
      { text: '取引種別', alignment: 'left' },
      { text: '売上額', alignment: 'right' },
      { text: '振込日', alignment: 'left' },
      { text: '会員番号', alignment: 'left' }
    ];

    // Build table data
    const tableData = data.data?.map(item => [
      { text: item.transactionDate, alignment: 'left' },
      { text: item.merchantNo, alignment: 'left' },
      { text: item.storeName, alignment: 'left' },
      { text: `${mapTransactionType.get(item.transactionType)}${item.groupCodeName}`, alignment: 'left' },
      { text: formatNum(item.salesAmount), alignment: 'right' },
      { text: item.paymentDate, alignment: 'left' },
      { text: item.memberId, alignment: 'left' }
    ]) || [];

    const content = [
      { text: dateTitle, alignment: 'right', margin: [0, 0, 0, 5] },
      { text: '振込詳細（多店舗管理）', fontSize: 24, alignment: 'center', margin: [0, 20, 0, 5] },
      {
        text: `加盟店ID: ${merchantNos}`,
        alignment: 'left',
        margin: [0, 0, 0, 5]
      },
      { text: `件数: ${formatNum(data.total)}`, alignment: 'left', margin: [0, 0, 0, 5] },
      { text: `売上額の合計: ${formatNum(data.totalSales)}`, alignment: 'left', margin: [0, 0, 0, 5] },
      {
        margin: [0, 20, 0, 0],
        table: {
          widths: [50, 50, 'auto', 50, 50, 50, '*'],
          body: [tableHeaders, ...tableData]
        },
        layout: 'lightHorizontalLines'
      }
    ];

    const config: PDFConfig = {
      content,
      filename: `deposit-detail-${transferDate}.pdf`
    };

    await PDFService.createPDF(config);
  }
}

export default PDFTemplates;
