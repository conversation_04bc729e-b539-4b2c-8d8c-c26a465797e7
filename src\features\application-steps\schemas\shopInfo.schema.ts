import { z } from "zod";
import { validateAscii, validatePattern, validateWideKana, validateWideString } from "../utils/validate";

// Schema validation cho form shop info
export const shopInfoSchema = z.object({
    agxStoreName: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxStorePhoneticName: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxStoreEnglishName: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return !validateAscii(value);
        }, {
            message: "※半角英数字で入力してください。"
        }),
    agxUrl: z.string().optional(),
    agxBrandName: z.string().trim()
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxBusinessDate: z.string().trim()
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxRegularHoliday: z.string().trim().optional(),
    agxBusinesssHours: z.string().trim().optional(),
    agxStoreAddressCopyFlag1: z.boolean().optional(),
    agxStoreAddressCopyFlag2: z.boolean().optional(),
    agxStorePostalCode: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .regex(/^(\d{7}|\d{3}-\d{4})$/, {
            message: "※書式が不正です。"
        }),
    agxStorePrefecture: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        }),
    agxStoreAddress1: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxStoreAddress2: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxStorePhoneticAddress1: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxStorePhoneticAddress2: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxStorePhoneNumber1: z.string().trim().optional(),
    agxStorePhoneNumber2: z.string().trim().optional(),
    agxStorePhoneNumber3: z.string().trim().optional(),
    agxStoreFaxNumber1: z.string().trim().optional(),
    agxStoreFaxNumber2: z.string().trim().optional(),
    agxStoreFaxNumber3: z.string().trim().optional(),
})
// Validate store phone number
.refine((data) => {
    const allThreeFilled = (
        data.agxStorePhoneNumber1 && data.agxStorePhoneNumber1.trim() !== "" &&
        data.agxStorePhoneNumber2 && data.agxStorePhoneNumber2.trim() !== "" &&
        data.agxStorePhoneNumber3 && data.agxStorePhoneNumber3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxStorePhoneNumber1"],
})
.refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxStorePhoneNumber1 || "")
        && pat.test(data.agxStorePhoneNumber2 || "")
        && pat.test(data.agxStorePhoneNumber3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxStorePhoneNumber1"]
})
// Validate fax number
.refine((data) => {
    const hasAtLeastOneFax = (
        (data.agxStoreFaxNumber1 && data.agxStoreFaxNumber1.trim() !== "") ||
        (data.agxStoreFaxNumber2 && data.agxStoreFaxNumber2.trim() !== "") ||
        (data.agxStoreFaxNumber3 && data.agxStoreFaxNumber3.trim() !== "")
    );

    if (!hasAtLeastOneFax) {
        return true;
    }

    const allThreeFilled = (
        data.agxStoreFaxNumber1 && data.agxStoreFaxNumber1.trim() !== "" &&
        data.agxStoreFaxNumber2 && data.agxStoreFaxNumber2.trim() !== "" &&
        data.agxStoreFaxNumber3 && data.agxStoreFaxNumber3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxStoreFaxNumber1"],
})
.refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxStoreFaxNumber1 || "")
        && pat.test(data.agxStoreFaxNumber2 || "")
        && pat.test(data.agxStoreFaxNumber3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxStoreFaxNumber1"]
})
.superRefine((data, ctx) => {
    if (data.agxUrl?.trim() !== "") {
      const url = data.agxUrl?.trim() || '';
  
      if (!url) {
        ctx.addIssue({
          path: ['agxUrl'],
          code: z.ZodIssueCode.custom,
          message: "※URLは必須です。"
        });
      } else if (validateAscii(url)) {
        ctx.addIssue({
          path: ['agxUrl'],
          code: z.ZodIssueCode.custom,
          message: "※半角英数字で入力してください。"
        });
      } else if (!validatePattern(url, '^(|https?://.+)$')) {
        ctx.addIssue({
          path: ['agxUrl'],
          code: z.ZodIssueCode.custom,
          message: "※書式が不正です。"
        });
      }
    }
  });

export type ShopInfoFormData = z.infer<typeof shopInfoSchema>; 