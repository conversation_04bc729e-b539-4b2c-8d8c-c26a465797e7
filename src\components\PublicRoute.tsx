import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { AccountTypes } from '@/types/globalType';

interface PublicRouteProps {
  children: React.ReactNode;
}

const ACCOUNT_TYPE_ROUTES: Record<AccountTypes, string> = {
  [AccountTypes.ADMIN_STORE]: '/overview',
  [AccountTypes.STORE]: '/overview',
  [AccountTypes.APPLICATION]: '/register-merchant',
  [AccountTypes.APPLICATION_STEPS]: '/application-steps',
  [AccountTypes.APPLICATION_COMPLETE]: '/overview',
  [AccountTypes.APPLICATION_CANCEL]: '/overview',
  [AccountTypes.APPLICATION_ACCEPTED_CORRECTIONS]: '/store-edit',
  [AccountTypes.APPLICATION_REJECTED]: '/rejected',
} as const;

const DEFAULT_ROUTE = '/overview';

export const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuthStore();
  const hasToken = !!localStorage.getItem('accessToken');
  const location = useLocation();

  // If user is authenticated, redirect to appropriate page
  if (isAuthenticated && hasToken && user) {
    const route = user.statusAccount 
      ? ACCOUNT_TYPE_ROUTES[user.statusAccount] || DEFAULT_ROUTE
      : DEFAULT_ROUTE;
      
    // Only redirect from specific auth pages to prevent infinite redirects on 404
    if(location.pathname === '/' || location.pathname === '/login' || location.pathname === '/signup' || location.pathname === '/mail/register') {
      return <Navigate to={route} replace />;
    }
  }

  return <>{children}</>;
}; 