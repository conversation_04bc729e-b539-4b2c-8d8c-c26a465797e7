import React from 'react';
import { useParams } from 'react-router-dom';
import { useStoreReceipt } from '@/features/store/invoice-receipt/hooks/useStoreReceipt';
import { ReceiptMonthlyLayout } from '@/features/store/invoice-receipt/components/ReceiptMonthlyLayout';

const CrepicoReceipt: React.FC = () => {
    const { invoiceNo } = useParams<{ invoiceNo: string }>();
    const { data, loading } = useStoreReceipt(invoiceNo);

    return <ReceiptMonthlyLayout data={data} loading={loading} />
};

export default CrepicoReceipt;