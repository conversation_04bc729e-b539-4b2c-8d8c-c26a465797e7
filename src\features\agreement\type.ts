export interface AgreementResponse {
  data: AgreementData;
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

export interface AgreementData {
  settlementCard: boolean;
  settlementTraffic: boolean;
  settlementNanaco: boolean;
  settlementWaon: boolean;
  settlementEdy: boolean;
  settlementAid: boolean;
  settlementQrCode: boolean;
  jcbExistsMembershipFlag: boolean;
  settlementPackage1: boolean;
  settlementPackage2: boolean;
}