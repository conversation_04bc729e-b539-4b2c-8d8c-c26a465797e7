import { z } from "zod";
import { validateHumanName, validatePattern, validate<PERSON><PERSON>K<PERSON>, validateWideString } from "../utils/validate";

const startYear = 1912;
const endYear = (new Date().getFullYear()) * 1;

// Schema validation cho form additional info
export const additionalSchema = z.object({
    agxContactLastName: z.string().optional(),
    agxContactFirstName: z.string().optional(),
    agxContactPhoneticLastName: z.string().optional(),
    agxContactPhoneticFirstName: z.string().optional(),
    agxContactPhone1: z.string().optional(),
    agxContactPhone2: z.string().optional(),
    agxContactPhone3: z.string().optional(),
    agxContactPhoneNumber: z.string().optional(),
    foundingYear: z.string().optional(),
    foundingMonth: z.string().optional(),
    agxContactEmail: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine(
            (value) => {
                return validatePattern(value, /^[A-Za-z0-9]{1}[A-Za-z0-9_.-]*@{1}[A-Za-z0-9_.-]{1,}\.[A-Za-z0-9]{1,}$/);
            }, {
            message: "※書式が不正です。"
        }),
    agxCapital: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validatePattern(value, /^\d*$/);
        }, {
            message: "※半角数値で入力してください。"
        }),
    agxNumberOfEmployees: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validatePattern(value, /^\d*$/);
        }, {
            message: "※半角数値で入力してください。"
        }),
    agxMonthlySales: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validatePattern(value, /^\d*$/);
        }, {
            message: "※半角数値で入力してください。"
        }),
})
// Validate contact name
.refine((data) => {
    if (data.agxContactLastName && data.agxContactFirstName) {
        return true;
    }
    return false;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxContactLastName"]
})
.refine((data) => {
    const agxContactName = data.agxContactLastName?.trim() + " " + data.agxContactFirstName?.trim();
    return validateWideString(agxContactName, true);
}, {
    message: "※全角文字で入力してください。",
    path: ["agxContactLastName"]
})
.refine((data) => {
    const agxContactName = data.agxContactLastName?.trim() + " " + data.agxContactFirstName?.trim();
    return validateHumanName(agxContactName);
}, {
    message: "※姓と名の間に空白を入れてください。",
    path: ["agxContactLastName"]
})
// Validate contact phonetic name
.refine((data) => {
    if (data.agxContactPhoneticLastName && data.agxContactPhoneticFirstName) {
        return true;
    }
    return false;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxContactPhoneticLastName"]
})
.refine((data) => {
    const agxContactPhoneticName = data.agxContactPhoneticLastName?.trim() + " " + data.agxContactPhoneticFirstName?.trim();
    return validateWideKana(agxContactPhoneticName, true);
}, {
    message: "※全角カタカナで入力してください。",
    path: ["agxContactPhoneticLastName"]
})
// Validate contact phone number
.refine((data) => {

    const allThreeFilled = (
        data.agxContactPhone1 && data.agxContactPhone1.trim() !== "" &&
        data.agxContactPhone2 && data.agxContactPhone2.trim() !== "" &&
        data.agxContactPhone3 && data.agxContactPhone3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxContactPhone1"],
})
.refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxContactPhone1 || "")
        && pat.test(data.agxContactPhone2 || "")
        && pat.test(data.agxContactPhone3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxContactPhone1"]
})

export type AdditionalFormData = z.infer<typeof additionalSchema>; 