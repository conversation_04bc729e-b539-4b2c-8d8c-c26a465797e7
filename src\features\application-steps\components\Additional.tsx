import Box from "@/components/ui/box";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormButtons } from "./ui/FormButtons";
import { STEP } from "@/constants/common.constant";
import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { additionalSchema, type AdditionalFormData } from "../schemas/additional.schema";
import ConfirmDialog from "@/components/ConfirmDialog";

const startYear = 1912;
const endYear = (new Date().getFullYear()) * 1;

const Additional = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {

    const [show, setShow] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        trigger,
        setValue,
        getValues
    } = useForm<AdditionalFormData>({
        resolver: zod<PERSON><PERSON><PERSON><PERSON>(additionalSchema),
        mode: "onBlur",
        defaultValues: {
            agxContactPhoneNumber: "",
            agxCapital: "0",
            agxNumberOfEmployees: "0",
            agxMonthlySales: "0",
            agxContactLastName: "",
            agxContactFirstName: "",
            agxContactPhoneticLastName: "",
            agxContactPhoneticFirstName: "",
            agxContactPhone1: "",
            agxContactPhone2: "",
            agxContactPhone3: "",
            agxContactEmail: "",
        }
    });

    const onSubmit = async (data: AdditionalFormData) => {
        const isValid = await trigger();
        if (!isValid) {
            return;
        }

        const updatedValues = {
            ...data,
            agxContactName: data.agxContactLastName + " " + data.agxContactFirstName,
            agxContactPhoneticName: data.agxContactPhoneticLastName + " " + data.agxContactPhoneticFirstName,
            agxFoundingDate: data.foundingYear + "-" + data.foundingMonth + "-01",
        };

        setAgxMerchantParams({
            ...agxMerchantParams,
            ...updatedValues,
        } as AgxMerchantParams);

        setStep(STEP.ADDITIONAL1);
    };

    const handleChangeAgxContactPhoneNumber = async (phone1: string, phone2: string, phone3: string) => {
        const cleanPhone1 = phone1.trim();
        const cleanPhone2 = phone2.trim();
        const cleanPhone3 = phone3.trim();

        setValue("agxContactPhoneNumber", `${cleanPhone1}-${cleanPhone2}-${cleanPhone3}`);
        setValue("agxContactPhone1", cleanPhone1);
        setValue("agxContactPhone2", cleanPhone2);
        setValue("agxContactPhone3", cleanPhone3);

        await trigger(['agxContactPhone1', 'agxContactPhone2', 'agxContactPhone3', 'agxContactPhoneNumber']);
    };

    const showConfirmDialog = async () => {
        const isValid = await trigger();
        if (!isValid) {
            return false;
        }
        setShow(true);
    }

    const onSave = async () => {

        const updatedValues = {
            ...getValues(),
            agxContactName: getValues().agxContactLastName + " " + getValues().agxContactFirstName,
            agxContactPhoneticName: getValues().agxContactPhoneticLastName + " " + getValues().agxContactPhoneticFirstName,
            agxFoundingDate: getValues().foundingYear + "-" + getValues().foundingMonth + "-01",
        };

        updateMerchant({ ...agxMerchantParams, ...updatedValues } as AgxMerchantParams);
        setShow(false);
    };

    const handleBack = () => {
        // Lưu dữ liệu form hiện tại vào agxMerchantParams trước khi back
        const updatedValues = {
            ...getValues(),
            agxContactName: getValues().agxContactLastName + " " + getValues().agxContactFirstName,
            agxContactPhoneticName: getValues().agxContactPhoneticLastName + " " + getValues().agxContactPhoneticFirstName,
        };

        setAgxMerchantParams({
            ...agxMerchantParams,
            ...updatedValues,
        } as AgxMerchantParams);

        setStep(STEP.BANK);
    };

    useEffect(() => {
        if (agxMerchantParams) {
            if (agxMerchantParams.agxContactName) {
                setValue("agxContactLastName", agxMerchantParams.agxContactName.split(" ")[0]);
                setValue("agxContactFirstName", agxMerchantParams.agxContactName.split(" ")[1]);
            }
            if (agxMerchantParams.agxContactPhoneticName) {
                setValue("agxContactPhoneticLastName", agxMerchantParams.agxContactPhoneticName.split(" ")[0]);
                setValue("agxContactPhoneticFirstName", agxMerchantParams.agxContactPhoneticName.split(" ")[1]);
            }
            if (agxMerchantParams.agxContactEmail) {
                setValue("agxContactEmail", agxMerchantParams.agxContactEmail);
            }
            if (agxMerchantParams.agxContactPhoneNumber) {
                setValue("agxContactPhoneNumber", agxMerchantParams.agxContactPhoneNumber);
                setValue("agxContactPhone1", agxMerchantParams.agxContactPhoneNumber.split("-")[0]);
                setValue("agxContactPhone2", agxMerchantParams.agxContactPhoneNumber.split("-")[1]);
                setValue("agxContactPhone3", agxMerchantParams.agxContactPhoneNumber.split("-")[2]);
            }
            if (agxMerchantParams.agxCapital) {
                setValue("agxCapital", agxMerchantParams.agxCapital.toString());
            }
            if (agxMerchantParams.agxNumberOfEmployees) {
                setValue("agxNumberOfEmployees", agxMerchantParams.agxNumberOfEmployees.toString());
            }
            if (agxMerchantParams.agxMonthlySales) {
                setValue("agxMonthlySales", agxMerchantParams.agxMonthlySales.toString());
            }
        }
    }, [agxMerchantParams, setValue]);

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box className="mx-auto mb-16 sm:mb-32 mt-6 sm:mt-[41px] space-y-4 sm:space-y-6 lg:space-y-[41px] px-4 sm:px-0">
                    <h1 className="font-normal text-[#707070] mb-4 md:mb-6 text-base sm:text-lg md:text-xl lg:text-[1.75rem] px-0 sm:pl-[30px]">
                        連絡先｜ご担当者情報（チョキペイからの連絡先）
                    </h1>

                    {/* Representative name */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                        お名前
                        <span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-col sm:flex-row gap-3 sm:gap-4 flex-1">
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxContactLastName")}
                                    maxLength={15}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="姓（全角）"
                                />
                                <div className="min-h-[20px] mt-1">
                                    {(errors.agxContactLastName) && (
                                        <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                            {errors.agxContactLastName?.message}
                                        </span>
                                    )}
                                </div>
                            </Box>
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxContactFirstName")}
                                    maxLength={15}
                                    onBlur={() => trigger(["agxContactFirstName", "agxContactLastName"])}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="名（全角）"
                                />
                            </Box>
                        </Box>
                    </Box>

                    {/* Representative name phonetic */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                        お名前（カナ）<span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-col sm:flex-row gap-3 sm:gap-4 flex-1">
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxContactPhoneticLastName")}
                                    maxLength={15}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="セイ（全角カナ）"
                                />
                                <div className="min-h-[20px] mt-1">
                                    {(errors.agxContactPhoneticLastName || errors.agxContactPhoneticFirstName) && (
                                        <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                            {errors.agxContactPhoneticLastName?.message || errors.agxContactPhoneticFirstName?.message}
                                        </span>
                                    )}
                                </div>
                            </Box>
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxContactPhoneticFirstName")}
                                    maxLength={15}
                                    onBlur={() => trigger(["agxContactPhoneticFirstName", "agxContactPhoneticLastName"])}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="メイ（全角カナ）"
                                />
                            </Box>
                        </Box>
                    </Box>

                    {/* Email */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            メールアドレス<span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex-1 flex flex-col">
                            <input
                                {...register("agxContactEmail")}
                                maxLength={100}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="メールアドレス（半角英数字）"
                                type="email"
                            />
                            <div className="min-h-[20px] mt-1">
                                {(errors.agxContactEmail) && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                        {errors.agxContactEmail?.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                    </Box>

                    {/* Phone number */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            電話番号
                            <span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex-1 flex flex-col">
                            <Box className="flex flex-row gap-1 sm:gap-2 lg:gap-6 items-center">
                                <Box className="w-[70px] sm:w-[80px] lg:w-[96px]">
                                    <input
                                        className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-1 sm:px-2 lg:px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                        placeholder="000"
                                        {...register("agxContactPhone1")}
                                        onBlur={(e) => handleChangeAgxContactPhoneNumber(e.target.value, getValues("agxContactPhone2"), getValues("agxContactPhone3"))}
                                        maxLength={5}
                                    />
                                </Box>
                                <Box className="w-[10px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] flex items-center justify-center">−</Box>
                                <Box className="w-[70px] sm:w-[80px] lg:w-[96px]">
                                    <input
                                        className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-1 sm:px-2 lg:px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                        placeholder="0000"
                                        {...register("agxContactPhone2")}
                                        onBlur={(e) => handleChangeAgxContactPhoneNumber(getValues("agxContactPhone1"), e.target.value, getValues("agxContactPhone3"))}
                                        maxLength={5}
                                    />
                                </Box>
                                <Box className="w-[10px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] flex items-center justify-center">−</Box>
                                <Box className="w-[70px] sm:w-[80px] lg:w-[96px]">
                                    <input
                                        className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-1 sm:px-2 lg:px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                        placeholder="0000"
                                        {...register("agxContactPhone3")}
                                        onBlur={(e) => handleChangeAgxContactPhoneNumber(getValues("agxContactPhone1"), getValues("agxContactPhone2"), e.target.value)}
                                        maxLength={5}
                                    />
                                </Box>
                            </Box>
                            <div className="min-h-[20px] mt-1">
                                {errors.agxContactPhone1 && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                        {errors.agxContactPhone1?.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                    </Box>

                    <hr className="border-t border-[#707070]" />

                    <h1 className="font-normal text-[#707070] mb-4 md:mb-6 text-base sm:text-lg md:text-xl lg:text-[1.75rem] px-0 sm:pl-[30px]">
                        付帯情報 ｜法人の方は法人の情報をご記載ください。
                    </h1>

                    {/* Representative name */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            資本金<span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-row gap-3 sm:gap-4 items-start flex-1">
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxCapital")}
                                    maxLength={10}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="0000 (半角数字)"
                                />
                                <div className="min-h-[20px] mt-1">
                                    {(errors.agxCapital) && (
                                        <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                            {errors.agxCapital?.message}
                                        </span>
                                    )}
                                </div>
                            </Box>
                            <Label className="text-base sm:text-lg md:text-xl lg:text-[1.75rem] required pt-2 text-[#6F6F6E] !items-start !text-left md:pt-[13px] lg:pt-[13px]">
                                万円<sub className="text-[1.125rem] pl-5 sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*個人の方は0をご入力ください</sub>
                            </Label>
                        </Box>
                    </Box>

                    {/* Representative name phonetic */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            従業員数<span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-row gap-3 sm:gap-4 items-start flex-1">
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxNumberOfEmployees")}
                                    maxLength={10}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="0000（半角数字）"
                                />
                                <div className="min-h-[20px] mt-1">
                                    {(errors.agxNumberOfEmployees) && (
                                        <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                            {errors.agxNumberOfEmployees?.message}
                                        </span>
                                    )}
                                </div>
                            </Box>
                            <Label className="text-base sm:text-lg md:text-xl lg:text-[1.75rem] required pt-2 text-[#6F6F6E] !items-start !text-left md:pt-[13px] lg:pt-[13px]">
                                人 
                            </Label>
                        </Box>
                    </Box>

                    {/* 設立年月日 */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            設立年月日<span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-col sm:flex-row gap-3 sm:gap-4 lg:gap-6 xl:gap-20 flex-1">
                            <Box className="w-full sm:max-w-[291px] flex-1 relative flex flex-col">
                                <select
                                    {...register("foundingYear")}
                                    className="
                                    w-full h-[40px] md:h-[50px] lg:h-[66px]
                                    rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                    border border-solid border-[#707070]
                                    px-3 md:px-4 lg:px-[17px]
                                    py-0 md:py-0 lg:py-0
                                    pr-10 md:pr-12 lg:pr-14
                                    text-sm md:text-base lg:text-[1.75rem] 
                                    text-[#707070] bg-white
                                    leading-[40px] md:leading-[50px] lg:leading-[66px]
                                    focus:border-[#1D9987] focus:outline-none transition-colors 
                                    appearance-none
                                    disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                                >
                                    {Array.from({ length: endYear - startYear + 1 }, (_, index) => startYear + index).map((year, index) => (
                                        <option value={year} key={index}>{year}年</option>
                                    ))}
                                </select>
                                {/* Custom dropdown arrow */}
                                <div className="absolute inset-y-0 right-0 flex top-[-10px] sm:top-[-15px] items-center pr-4 pointer-events-none">
                                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="min-h-[20px] mt-1">
                                    {errors.foundingYear && (
                                        <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                            {errors.foundingYear.message}
                                        </span>
                                    )}
                                </div>
                            </Box>
                            <Box className="w-full sm:max-w-[291px] flex-1 relative flex flex-col">
                                <select
                                    {...register("foundingMonth")}
                                    className="
                                    w-full h-[40px] md:h-[50px] lg:h-[66px]
                                    rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                    border border-solid border-[#707070]
                                    px-3 md:px-4 lg:px-[17px]
                                    py-0 md:py-0 lg:py-0
                                    pr-10 md:pr-12 lg:pr-14
                                    text-sm md:text-base lg:text-[1.75rem] 
                                    text-[#707070] bg-white
                                    leading-[40px] md:leading-[50px] lg:leading-[66px]
                                    focus:border-[#1D9987] focus:outline-none transition-colors 
                                    appearance-none
                                    disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                                >
                                    <option value="01">1月</option>
                                    <option value="02">2月</option>
                                    <option value="03">3月</option>
                                    <option value="04">4月</option>
                                    <option value="05">5月</option>
                                    <option value="06">6月</option>
                                    <option value="07">7月</option>
                                    <option value="08">8月</option>
                                    <option value="09">9月</option>
                                    <option value="10">10月</option>
                                    <option value="11">11月</option>
                                    <option value="12">12月</option>
                                </select>
                                {/* Custom dropdown arrow */}
                                <div className="absolute inset-y-0 top-[-5px] sm:top-[-15px] right-0 flex items-center pr-4 pointer-events-none">
                                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </Box>
                        </Box>
                    </Box>

                    {/* 月商 */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            月商<span className="text-[#FF0000] text-base sm:text-lg md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-row gap-3 sm:gap-4 items-start flex-1">
                            <Box className="w-full sm:max-w-[291px] flex-1 flex flex-col">
                                <input
                                    {...register("agxMonthlySales")}
                                    maxLength={10}
                                    className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-sm sm:text-base md:text-lg lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                    placeholder="0000（半角数字）"
                                />
                                <div className="min-h-[20px] mt-1">
                                    {(errors.agxMonthlySales) && (
                                        <span className="block font-bold text-[rgba(255,0,0,1)] text-sm">
                                            {errors.agxMonthlySales?.message}
                                        </span>
                                    )}
                                </div>
                            </Box>
                            <Label className="text-base sm:text-lg md:text-xl lg:text-[1.75rem] required pt-2 text-[#6F6F6E] !items-start !text-left md:pt-[13px] lg:pt-[13px]">
                                万円
                            </Label>
                        </Box>
                    </Box>

                    {/* Form buttons */}
                    <FormButtons
                        onSave={showConfirmDialog}
                        onNext={() => handleSubmit(onSubmit)}
                        onBack={handleBack}
                        isSubmitting={isUpdating}
                        showBackButton={true}
                    />
                </Box>
                <ConfirmDialog
                    open={show}
                    onOpenChange={setShow}
                    onConfirm={onSave}
                    title="入力内容を一時保存します。"
                    confirmLabel="一時保存"
                    confirmVariant="danger"
                    cancelLabel="戻る"
                    onCancel={() => setShow(false)}
                />
            </form>
        </>
    )
}

export default Additional;