import { useQuery } from '@tanstack/react-query';
import { invoiceService } from '../services/invoiceReceiptService';
import { CreditCardMonthlyFeeType } from '../types';

export const useGetDataCreditCardMonthlyFee = (merchantNo: string, yearMonth: string, type: string) => {
  return useQuery({
    queryKey: ['admin-credit-card-monthly-fee', merchantNo, yearMonth],
    queryFn: async (): Promise<CreditCardMonthlyFeeType[]> => {
      if (!merchantNo || !yearMonth) {
        throw new Error('Merchant number and year month are required');
      }
      const response = await invoiceService.getDataCreditCardMonthlyFee(merchantNo, yearMonth, type);
      return response.data;
    },
    enabled: !!merchantNo && !!yearMonth,
  });
};
