import { useParams } from 'react-router-dom';
import { formatNumber } from '@/utils/dateUtils';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useAuthStore } from '@/store';
import { useGetDataMonthlyCostByStore } from '@/features/adminStore/invoice-receipt/hooks/useGetDataMonthlyCostByStore';
import { Download } from 'lucide-react';
import PDFService, { blobToBase64, PDFConfig } from '@/services/pdfService';
import { axiosPDF } from "@/features/store/invoice-receipt/utils";

function MonthlyCostByStore() {
  const { month, merchantNo } = useParams<{ month: string, merchantNo: string }>();

  // Use the hook to get data
  const { data, isLoading } = useGetDataMonthlyCostByStore(merchantNo, month);
  
  // Handle loading state
  if (isLoading) {
    return <LoadingSpinner />;
  }

    const getRecordDetailPDF = () => {
        const result = [[{
            text: '品目',
            style: 'tableHeader',
            alignment: 'center'
        },
        {
            text: '数量',
            style: 'tableHeader',
            alignment: 'center'
        },
        {
            text: '単価',
            style: 'tableHeader',
            alignment: 'center'
        },
        {
            text: '金額',
            style: 'tableHeader',
            alignment: 'center'
        }]];
        
        for (const index in data?.monthlyCostByStore) {
            const cols = [];
            cols.push(
                { text: data?.monthlyCostByStore[index].name, alignment: 'left' },
                { text: formatNumber(data?.monthlyCostByStore[index].quantity), alignment: 'right' },
                { text: formatNumber(data?.monthlyCostByStore[index].unitPrice), alignment: 'right' },
                { text: formatNumber(data?.monthlyCostByStore[index].amount), alignment: 'right' }
            );
            result.push(cols);
        }
        
        // Add empty rows for spacing
        for (let i = 0; i < 6; i++) {
            result.push([
                { text: '\n', alignment: 'right' } as any, 
                { text: '\n', alignment: 'right' } as any, 
                { text: '\n', alignment: 'right' } as any, 
                { text: '\n', alignment: 'right' } as any
            ]);
        }
        
        //Table detail footer
        result.push([
            {
                text: '\n',
                border: [false, false, false, false],
            } as any,
            {
                text: '小計',
                border: [true, true, false, false],
            } as any,
            {
                text: '\n',
                border: [false, true, false, false],
            } as any,
            {
                text: formatNumber(data?.subTotal),
                alignment: 'right'
            } as any,
        ]);
        result.push([
            {
                text: '\n',
                border: [false, false, false, false],
            } as any,
            {
                text: '消費税(10%) ',
                border: [true, true, false, false],
            } as any,
            {
                text: '\n',
                border: [false, true, false, false],
            } as any,
            {
                text: formatNumber(data?.tax),
                alignment: 'right'
            } as any,
        ]);
        result.push([
            {
                text: '\n',
                border: [false, false, false, false],
            } as any,
            {
                text: '合計',
                bold: true,
                border: [true, true, false, true],
            } as any,
            {
                text: '\n',
                border: [false, true, false, true],
            } as any,
            {
                text: formatNumber(data?.total),
                bold: true,
                alignment: 'right'
            } as any,
        ]);
        return result;
    }

    const handleExportPDF = async () => {
        try {
            // Check if data exists first
            if (!data) {
                alert('データが見つかりません。');
                return;
            }

            // Setup fonts first and wait for them to be ready
            const responseYugothib = await axiosPDF.get('yugothib.ttf');
            if (responseYugothib.status !== 200) {
                alert('フォントの読み込みに失敗しました。');
                return;
            }
            const fontYugothib = await blobToBase64(responseYugothib.data);

            const responseArial = await axiosPDF.get('arial.ttf');
            if (responseArial.status !== 200) {
                alert('フォントの読み込みに失敗しました。');
                return;
            }
            const fontArial = await blobToBase64(responseArial.data);

            // Setup fonts and wait for completion
            await PDFService.setupMultipleFonts(fontYugothib, fontArial);

            const details = getRecordDetailPDF();

            const config: PDFConfig = {
                pageSize: { width: 640, height: 900 } as any,
                pageOrientation: 'portrait',
                defaultStyle: {
                    font: 'yugothib'
                },
                background: function (page) {
                    if (page === 1) {
                        return [];
                    }
                },
                content: [
                    { text: data?.storeName, alignment: 'left', margin: [20, 0, 0, 5] },
                    {
                        style: 'tableMargin',
                        table: {
                            headerRows: 1,
                            widths: [220, 70, 95, '*'],
                            body: details
                        },
                        layout: {
                            fillColor: function (rowIndex, node, columnIndex) {
                                return (rowIndex === 0) ? '#757575' : null;
                            }
                        }
                    },
                ],
                styles: {
                    header: {
                        fontSize: 18,
                        bold: true,
                        margin: [0, 0, 0, 10]
                    },
                    tableMargin: {
                        margin: [20, 10, 20, 15]
                    },
                    tableHeader: {
                        bold: true,
                        fontSize: 13,
                        color: 'white'
                    }
                },
                filename: `${data?.storeName}_invoice_transaction.pdf`
            };

            // Create PDF after fonts are properly loaded
            await PDFService.createPDFWithSetupFonts(config);
            
        } catch (error) {
            console.error('PDF作成エラー:', error);
            alert('PDF作成に失敗しました。');
        }
    };

    return (
      <div className="p-4 md:py-6 md:px-1 lg:p-6">
        <div className="page-heading">
          <div className="flex">
            <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">{data?.storeName || ''}</h2>
            <button id="download-pdf" className="p-2 pl-4 text-[#1D9987] hover:text-[#1D9987]/80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" onClick={handleExportPDF}>
              <Download className="h-6 w-6" />
            </button>
          </div>
          <hr className='mt-3 border-1 border-gray-500' />
        </div>
        <div className="py-6">
          <div className="rounded-md">
            <table className="table w-full font-medium">
              <thead>
                  <tr className="bg-[#757575] border border-[#6F6F6E]">
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4">品目</th>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4">数量</th>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4">単価</th>
                      <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4">金額</th>
                  </tr>
              </thead>
              <tbody>
                {data?.monthlyCostByStore?.length > 0 ? (
                  data?.monthlyCostByStore?.map((item, index) => (
                  <tr className="border border-[#6F6F6E] text-[16px] text-[#6F6F6E]" key={index}>
                      <td className="text-left border border-[#6F6F6E] py-2 px-4">
                          {item.name}
                      </td>
                      <td className="text-right border border-[#6F6F6E] py-2 px-4">{formatNumber(item.quantity)}</td>
                      <td className="text-right border border-[#6F6F6E] py-2 px-4">{formatNumber(item.unitPrice)}</td>
                      <td className="text-right border border-[#6F6F6E] py-2 px-4">{formatNumber(item.amount)}</td>
                  </tr>
                ))
                ) : (
                  <tr>
                    <td colSpan={4} className="text-[#6F6F6E] text-center border border-[#6F6F6E] py-2 px-4">
                        請求書に商品がありません。
                    </td>
                  </tr>
                )}
                
                <tr className="border border-[#6F6F6E] text-[16px] text-[#6F6F6E]">
                    <td className="text-left border border-[#6F6F6E] py-2 px-4"></td>
                    <td  colSpan={2} className="text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4 ">小計</td>
                    <td className="text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4">
                        {formatNumber(data?.subTotal || 0)}</td>
                </tr>
                <tr className="border border-[#6F6F6E] text-[16px] text-[#6F6F6E]">
                    <td className="text-left border border-[#6F6F6E] py-2 px-4"></td>
                    <td colSpan={2} className="text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4">消費税(10%)</td>
                    <td className="text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4">
                        {formatNumber(data?.tax || 0)} </td>
                </tr>
                <tr className="border border-[#6F6F6E] text-[16px] text-[#6F6F6E]">
                    <td className="text-left border border-[#6F6F6E] py-2 px-4"></td>
                    <td colSpan={2} className="text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4  font-bold">
                        合計</td>
                    <td className="text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4 font-bold">
                        {formatNumber(data?.total || 0)}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
}

export default MonthlyCostByStore