import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { DepositPage } from '@/features/store/deposit/components/DepositPage';
import { STORE } from '@/types/globalType';

const StoreDepositCrepico: React.FC = () => {
  const { user } = useAuthStore();
  // Determine merchant number from params or auth store
  const agxMerchantNo = user?.memberType ? user.agxMerchantNo : user.agxNewMerchantNo || '';

  if (!agxMerchantNo) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">加盟店番号が見つかりません。</h2>
      </div>
    );
  }
  return <DepositPage agxMerchantNo={agxMerchantNo} type={STORE.CREPICO} />;
};

export default StoreDepositCrepico;
