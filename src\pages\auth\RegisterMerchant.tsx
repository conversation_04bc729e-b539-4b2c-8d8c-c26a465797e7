import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { MiniProgressBar } from '@/components/MiniProgressBar';
import { merchantCore, REGISTER } from '@/constants/common.constant';
import { IMerchantCoreType } from '@/features/auth';
import BusinessType from '@/features/auth/components/register/BusinessType';
import BusinessForm from '@/features/auth/components/register/BusinessForm';
import MedicalInstitutionCode from '@/features/auth/components/register/MedicalInstitutionCode';
import EmsEmployeeNo from '@/features/auth/components/register/EmsEmployeeNo';
import Confirmation from '@/features/auth/components/register/Confirmation';
import Success from '@/features/auth/components/register/Success';

export const RegisterMerchant = () => {
    // Get URL parameters
    const { type, token } = useParams();

    // Determine initial step based on URL
    const isTokenRoute = !!token && !!type;
    const initialStep = REGISTER.BUSINESS_TYPE;

    const [currentStep, setCurrentStep] = useState(initialStep);
    const [merchant, setMerchant] = useState<IMerchantCoreType>(merchantCore);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Effect to extract email from token if available
    useEffect(() => {
        if (isTokenRoute) {
            setMerchant(prev => ({
                ...prev,
            }));
        }
    }, [isTokenRoute, token]);

    // Helper to determine what content to show
    const showIntro = false; // No intro section needed for merchant registration
    const showTitle = ![REGISTER.SUCCESS].includes(currentStep);
    const showProgressBar = ![REGISTER.SUCCESS].includes(currentStep);

    // Helper to get the page title
    const getPageTitle = () => {
        if (currentStep === REGISTER.BUSINESS_TYPE ||
            currentStep === REGISTER.ENTITY_TYPE ||
            currentStep === REGISTER.MEDICAL_CODE) {
            return 'はじめに3つのご質問です';
        }

        if (currentStep === REGISTER.REFERRAL_CODE) return '紹介コードはお持ちですか？';
        if (currentStep === REGISTER.CONFIRMATION) return 'チョキペイIDを作成する';

        return '';
    };

    const updateMerchant = (data: any) => {
        setMerchant(prev => ({ ...prev, ...data }));
    };

    const nextStep = () => {
        if (currentStep < REGISTER.SUCCESS) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > REGISTER.BUSINESS_TYPE) {
            setCurrentStep(currentStep - 1);
        }
    };

    const renderProgressBar = () => {
        const progressWidth = ((currentStep - REGISTER.BUSINESS_TYPE) / (REGISTER.CONFIRMATION - REGISTER.BUSINESS_TYPE)) * 100;
        return (
            <MiniProgressBar progress={progressWidth} className="w-[80%] xl:w-[26%] max-md:w-full max-w-full bg-gray-300 rounded-full mb-1 mx-auto" />
        );
    };

    const renderStep = () => {
        switch (currentStep) {
            case REGISTER.BUSINESS_TYPE:
                return (
                    <BusinessType
                        data={merchant}
                        onNext={nextStep}
                        onUpdate={updateMerchant}
                    />
                );
            case REGISTER.ENTITY_TYPE:
                return (
                    <BusinessForm
                        data={merchant}
                        onNext={nextStep}
                        onPrev={prevStep}
                        onUpdate={updateMerchant}
                    />
                );
            case REGISTER.MEDICAL_CODE:
                return (
                    <MedicalInstitutionCode
                        data={merchant}
                        onNext={nextStep}
                        onPrev={prevStep}
                        onUpdate={updateMerchant}
                    />
                );
            case REGISTER.REFERRAL_CODE:
                return (
                    <EmsEmployeeNo
                        data={merchant}
                        onNext={nextStep}
                        onPrev={prevStep}
                        onUpdate={updateMerchant}
                    />
                );
            case REGISTER.CONFIRMATION:
                return (
                    <Confirmation
                        data={merchant}
                        onNext={nextStep}
                        onPrev={prevStep}
                        isSubmitting={isSubmitting}
                        onUpdate={updateMerchant}
                    />
                );
            case REGISTER.SUCCESS:
                return <Success />;
            default:
                return null;
        }
    };

    return (
        <div className="bg-white flex flex-col overflow-hidden items-stretch pb-32 px-4 md:px-0">
            {/* Title Section */}
            {showTitle && (
                <div className="text-center space-y-4">
                    <div className="flex items-center justify-center space-x-2 mb-4">
                        <h1 className="text-[40px] max-md:text-[30px] text-[rgba(112,112,112,1)] font-normal">
                            {getPageTitle()}
                        </h1>
                    </div>
                </div>
            )}

            {/* Progress Bar */}
            {showProgressBar && renderProgressBar()}

            {/* Error message */}
            {error && (
                <div className="text-red-500 text-center mb-4">
                    {error}
                </div>
            )}

            {/* Step Content */}
            {renderStep()}
        </div>
    );
};

