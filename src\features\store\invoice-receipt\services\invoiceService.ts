import apiService from "@/services/api";
import { API_ENDPOINTS } from "@/config/api-endpoints";

import { InvoiceApiResponse } from "@/features/store/invoice-receipt/types";

// Types cho các response khác
interface AgxInvoiceResponse {
  data: Array<{
    year?: number;
    month?: number;
    total?: number;
    invoiceUrl?: string;
    receiptUrl?: string;
    deadlineDate?: string;
  }>;
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

// Type cho chi tiết invoice paygate
export interface InvoiceDetailResponse {
  data: {
    invoiceCreatedDate: string;
    invoiceDeadlineDate: string;
    storeName: string;
    invoiceDetail: Array<{
      name: string;
      quantity: number;
      unitPrice: number;
      amount: number;
    }>;
    subTotal: number;
    tax: number;
    total: number;
  };
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

interface AgxInvoiceDetailResponse {
  data: {
    deadlineDate: string;
    invoiceStatus: number;
    invoiceUrl: string;
    receiptUrl: string;
    total: number;
    agxStatus?: number;
  };
}

interface GmoInfoResponse {
  data: {
    memberId: string | null;
    cardNo: string | null;
  };
}

interface GmoPaymentResponse {
  data: {
    LinkUrl: string;
  };
}

class InvoiceService {
  async getData(invoiceNo: string): Promise<InvoiceDetailResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.GET_DATA(invoiceNo)) as InvoiceDetailResponse;
    return response;
  }

  // Lấy danh sách invoice hàng tháng (tương đương getAgxInvoiceService)
  async getAgxInvoice(merchantNo: string): Promise<AgxInvoiceResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.GET_INVOICE_RECEIPT(merchantNo)) as AgxInvoiceResponse;
    return response;
  }

  // Lấy chi tiết invoice terminal (tương đương getAgxInvoiceDetailService)
  async getAgxInvoiceDetail(merchantNo: string): Promise<AgxInvoiceDetailResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.GET_AGX_INVOICE_RECEIPT_DETAIL(merchantNo)) as AgxInvoiceDetailResponse;
    return response;
  }

  // Lấy thông tin GMO member
  async getGmoInfo(merchantNo: string): Promise<GmoInfoResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.GET_GMO_INFO(merchantNo)) as GmoInfoResponse;
    return response;
  }

  // Lấy URL thanh toán GMO cho registration
  async getGmoPaymentUrl(merchantNo: string): Promise<GmoPaymentResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.GET_GMO_PAYMENT_URL(merchantNo)) as GmoPaymentResponse;
    return response;
  }

  // Lấy URL chỉnh sửa thẻ tín dụng GMO
  async getGmoLinkplusUrl(memberId: string, cardNo: string): Promise<GmoPaymentResponse> {
    const response = await apiService.post(API_ENDPOINTS.INVOICE_RECEIPT.GET_GMO_LINK_PLUS_URL(memberId, cardNo)) as GmoPaymentResponse;
    return response;
  }

  // Method cũ để tương thích
  async getAgxInvoiceDetailService(merchantNo: string): Promise<InvoiceApiResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.AGX_INVOICE_DETAIL(merchantNo)) as InvoiceApiResponse;
    return response;
  }
}

export const invoiceService = new InvoiceService();