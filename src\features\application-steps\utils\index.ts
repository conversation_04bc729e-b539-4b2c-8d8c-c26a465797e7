import { STEP } from "@/constants/common.constant";
import axios from "axios";

export const findDataBank = async (url: string, param: string) => {
    let data = null;
    await axios.get(`${url}${param}?apikey=bk_115b13555fff4af6b06b5d72b69b1f8c`)
    .then((response) => {
        if (response.status === 200) {
            data = response.data;
        }
    }).catch((error) => {
        console.error(error);
    });
    return data;
}

export const replaceHalfToFull = (str: string): string => {
    return str.replace(/[!-~]/g, (char) => {
      return String.fromCharCode(char.charCodeAt(0) + 0xFEE0);
    });
  };

export const formatFee = (amount: number): string => {
    return amount.toLocaleString("ja-JP", { 
      style: "currency", 
      currency: "JPY" 
    }).substring(1); // Remove the ¥ symbol
  };

export const calculateFees = (
  numberOfTerminals: number,
  isPackage1: boolean,
  isPackage2: boolean
) => {
  // Logic identical to ChoqiPayCrepico.tsx: isCrepico = !package1 && !package2 ? true : package1  
  const isCrepico = !isPackage1 && !isPackage2 ? true : isPackage1;
  const moneyCrepico = numberOfTerminals * (
    (isCrepico ? moneyData.money_Credit_Card_And_QRcode : 0) + 
    (isPackage2 ? moneyData.money_All_Crepico : 0)
  );
  
  return {
    initialFee: numberOfTerminals * moneyData.money_core,
    monthlyFee: moneyCrepico
  };
};

export const moneyData = {
    money_core: 99800,
    money_card: 600,
    money_Traffic: 400,
    money_nanaco: 400,
    money_WAON: 400,
    money_Edy: 400,
    money_iD: 400,
    money_QUICPay: 400,
    money_QR: 600,
    money_Core_Crepico: 99800,
    money_Credit_Card_And_QRcode: 500,
    money_All_Crepico: 1000
  };

export const title = (step: number) => {
  switch (step) {
    case STEP.HOUJIN:
      return "加盟店申し込み ｜ 法人情報";
    case STEP.DAIHYOUSHA:
      return "加盟店申し込み ｜ 代表者情報";
    case STEP.SHOPINFO:
      return "加盟店申し込み ｜ 店舗情報";
    case STEP.BANK:
      return "加盟店申し込み ｜ 銀行情報";
    case STEP.ADDITIONAL:
      return "加盟店申し込み ｜ 付帯情報";
    case STEP.ADDITIONAL1:
      return "加盟店申し込み ｜ セキュリティ情報";
    case STEP.CHOQIPAY:
      return "加盟店申し込み ｜ チョキペイ情報";
    case STEP.KIYAKU:
      return "加盟店申し込み ｜ 規約確認";
    case STEP.CONFIRM:
      return "加盟店申し込み ｜ 内容確認";
  }
}

export const progress = (step: number) => {
  switch (step) {
    case STEP.HOUJIN:
      return 10;
    case STEP.DAIHYOUSHA:
      return 20;
    case STEP.SHOPINFO:
      return 30;
    case STEP.BANK:
      return 40;
    case STEP.ADDITIONAL:
      return 50;
    case STEP.ADDITIONAL1:
      return 60;
    case STEP.CHOQIPAY:
      return 70;
    case STEP.KIYAKU:
      return 80;
    case STEP.CONFIRM:
      return 90;
  }
}

export const gender = (gender: number) => {
  switch (Number(gender)) {
    case *********:
      return '男性';
    case *********:
      return '女性';
    default:
      return '不明';
  }
}

export const formatPhoneNumber = (value?: string): string => {
  if (!value) return '';
  const parts = value.split('-');
  if (parts.length !== 3 || parts.some(p => !p.trim())) return '';
  return parts.join(' - ');
}
