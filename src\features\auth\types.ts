export interface LoginParams {
  username: string;
  password: string;
}

export interface SignupParams {
  username?: string;
  email: string;
  password: string;
  memberType?: string;
}

export interface User {
  id?: string | null;
  username?: string | null;
  email?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  roles?: string[] | null;
  statusAccount?: number | null;
  memberType?: boolean | null;
  agxMerchantNo?: string | null;
  agxNewMerchantNo?: string | null;
  agxOldMerchantNo?: string | null;
  agxStoreName?: string | null;
  accountName?: string | null;
  agxStoreEnglishName?: string | null;
  lastSuccessfulLogin?: string | null;
  merchantRegistrationDate?: string | null;
  requireVerification?: boolean | null;
  verificationId?: string | null;
}

export interface AuthResponse {
  token?: string | null;
  type?: string | null;
  id?: string | null;
  username?: string | null;
  email?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  roles?: string[] | null;
  statusAccount?: number | null;
  memberType?: boolean | null;
  agxMerchantNo?: string | null;
  agxNewMerchantNo?: string | null;
  agxOldMerchantNo?: string | null;
  agxStoreName?: string | null;
  accountName?: string | null;
  agxStoreEnglishName?: string | null;
  lastSuccessfulLogin?: string | null;
  merchantRegistrationDate?: string | null;
  requireVerification?: boolean | null;
  verificationId?: string | null;
}

export interface MailRegisterParams {
  email: string;
  type: string;
}

export interface MailRegisterResponse {
  status: number;
  message: string;
  statusCode: number;
  timestamp: string;
}

export interface CommonResponse<T> {
  data: T;
  hasNext: boolean;
  totalElements: number;
  totalPage: number;
}

export interface IBusinessType {
  id: number;
  label: string;
}

export interface IEntityType {
  id: number;
  label: string;
}

export interface IMerchantCoreType {
  agxMerchantNo: string | null;
  agxMerchantid: string;
  agxContactId: string;
  agxBusinessType: number;
  agxBusinessForm: number;
  agxMedicalInstitutionCode: string;
  agxEmsEmployeeNo: string;
  agxCorporateName: string;
  agxCorporatePhoneticName: string;
  agxCorporateEnglishName: string;
  agxCorporateNumber: string;
  agxCorporatePostalCode: string;
  agxCorporatePrefecture: string;
  agxCorporateAddress1: string;
  agxCorporateAddress2: string;
  agxCorporatePhoneticPrefecture: string;
  agxCorporatePhoneticAddress1: string;
  agxCorporatePhoneticAddress2: string;
  agxCorporatePhoneNumber: string;
  agxCorporateFaxNumber: string;
  agxRepresentativeName: string;
  agxRepresentativePhoneticName: string;
  agxRepresentativeGender: number;
  agxRepresentativeBirthday: string;
  agxRepresentativeAddressCopyFlag: boolean;
  agxRepresentativePostalCode: string;
  agxRepresentativePrefecture: string;
  agxRepresentativeAddress1: string;
  agxRepresentativeAddress2: string;
  agxRepresentativePhoneticPrefecture: string;
  agxRepresentativePhoneticAddress1: string;
  agxRepresentativePhoneticAddress2: string;
  agxRepresentativePhoneNumber: string;
  agxRepresentativeFaxNumber: string;
  agxStoreName: string;
  agxStorePhoneticName: string;
  agxStoreEnglishName: string;
  agxUrl: string;
  agxBrandName: string;
  agxBusinessDate: string;
  agxRegularHoliday: string;
  agxBusinesssHours: string;
  agxStoreAddressCopyFlag1: boolean;
  agxStoreAddressCopyFlag2: boolean;
  agxStorePostalCode: string;
  agxStorePrefecture: string;
  agxStoreAddress1: string;
  agxStoreAddress2: string;
  agxStorePhoneticPrefecture: string;
  agxStorePhoneticAddress1: string;
  agxStorePhoneticAddress2: string;
  agxStorePhoneNumber: string;
  agxStoreFaxNumber: string;
  agxBankNo: string;
  agxBankName: string;
  agxBankType: number;
  agxBranchNo: string;
  agxBranchName: string;
  agxBranchType: number;
  agxBankPhoneticName: string;
  agxBranchPhoneticName: string;
  agxAccountType: number;
  agxAccountNo: string;
  agxAccountHolder: string;
  agxContactName: string;
  agxContactEmail: string;
  agxContactPhoneticName: string;
  agxContactPhoneNumber: string;
  agxCapital: number;
  agxNumberOfEmployees: string;
  agxFoundingDate: string | null;
  agxMonthlySales: number;
  agxDoorToDoorSales: boolean;
  agxTelemarketingSales: boolean;
  agxPyramidScheme: boolean;
  agxBusinessOpportunityRelatedSales: boolean;
  agxSpecifiedContinuousServices: boolean;
  agxCardInformationRetentionStatus: number;
  agxNoRetainingCardInfoDate: string | null;
  agxPcidssStatus: number;
  agxPcidssExpectedComplianceDate: string | null;
  agxThreeDSecureStatus: number;
  agxThreeDSecureDate: string | null;
  agxSecurityCodeCheckStatus: number;
  agxSecurityCodeCheckDate: string | null;
  agxIllegalDeliveryDestinationStatus: number;
  agxIllegalDeliveryDestinationDate: string | null;
  agxBehaviorAnalysisStatus: number;
  agxBehaviorAnalysisDate: string | null;
  agxOtherMeasuresStatus: number;
  agxOtherMeasuresDate: string | null;
  agxOtherMeasuresDescription: string;
  agxNumberOfTerminal: number;
  agxColorOfTerminal: number;
  agxSettlementCard: boolean;
  agxSettlementJcb: boolean;
  agxSettlementTraffic: boolean;
  agxSettlementNanaco: boolean;
  agxSettlementWaon: boolean;
  agxSettlementEdy: boolean;
  agxSettlementAid: boolean;
  agxSettlementQuicpay: boolean;
  agxSettlementQrCode: boolean;
  memberType: boolean | null;
  agxSettlementPackage1: boolean;
  agxSettlementPackage2: boolean;
  agxApplicationStatus: number | null;
}
