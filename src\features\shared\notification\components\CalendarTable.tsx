import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CalendarTableProps } from '@/features/shared/notification/types';
import calendarIcon from '@/assets/images/グループ 1379.svg';

export const CalendarTable: React.FC<CalendarTableProps> = ({ data, isAdmin = false }) => {
  return (
    <div className='px-4'>
      <div className="flex items-center gap-2 mb-4">
        <img src={calendarIcon} alt="Calendar" className="w-8 h-8" />
        <h2 className="text-[#6F6F6E] text-[22px]">
          振込カレンダー
        </h2>
      </div>

      <div className="overflow-x-auto sm:overflow-visible xl:ml-10 xl:w-[550px] text-[#6F6F6E] font-size-[17px] ">
        <div className='flex flex-col'>
          {/* Header row */}
          <div className='flex flex-row gap-4 xl:gap-8'>
            <div className="py-3 px-0 text-center bg-white border-b border-gray-500 flex-[2] xl:px-8">
              決済期間
            </div>
            <div className="py-3 px-0 text-center bg-white border-b border-gray-500 flex-[1] xl:px-4">
              振込日
            </div>
          </div>
          {data.map((item, index) => (
            <div key={index} className='flex flex-row gap-4 xl:gap-8'>
              <div className="py-3 px-0 text-center border-b border-gray-500 flex-[2] xl:px-8">
                {item.period}
              </div>
              <div className={`py-3 px-0 text-center border-b border-gray-500 flex-1 ${item.isHighlighted ? "text-red-500" : ""} xl:px-4`}>
                {item.transferDate}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
