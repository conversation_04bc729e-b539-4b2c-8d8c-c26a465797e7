import { MAIL_REGISTER } from '@/constants/common.constant';
import { useState } from 'react';
import SendEmail from '@/features/auth/components/register/SendMail';
import EmailSent from '@/features/auth/components/register/EmailSent';

const MailRegister = () => {
    const [currentStep, setCurrentStep] = useState(MAIL_REGISTER.SEND_MAIL);
    const [email, setEmail] = useState<string>("");
    const [error, setError] = useState<string | null>(null);

    const updateEmail = (data: string) => {
        setEmail(data || "");
    };

    const nextStep = () => {
        setCurrentStep(currentStep + 1);
    };

    const prevStep = () => {
        if (currentStep > MAIL_REGISTER.SEND_MAIL) {
            setCurrentStep(currentStep - 1);
        }
    };

    const renderStep = () => {
        switch (currentStep) {
            case MAIL_REGISTER.SEND_MAIL:
                return (
                    <SendEmail
                        data={email}
                        onNext={nextStep}
                        onPrev={prevStep}
                        onUpdate={updateEmail}
                    />
                );
            case MAIL_REGISTER.SEND_MAIL_SUCCESS:
                return (
                    <EmailSent email={email} onNext={nextStep} />
                );
            default:
                return null;
        }
    };

    return (
        <div className="bg-white flex flex-col overflow-hidden items-stretch pb-32 px-4 md:px-0">
            {error && (
                <div className="text-red-500 text-center mb-4">
                    {error}
                </div>
            )}
            {renderStep()}
        </div>
    );
};

export default MailRegister;
