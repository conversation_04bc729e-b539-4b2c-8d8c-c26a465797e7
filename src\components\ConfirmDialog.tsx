import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,  
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import clsx from "clsx";

export interface ConfirmDialogProps {
  /**
   * Controls the visibility of the dialog.
   */
  open: boolean;
  /**
   * Callback fired when the dialog requests to change its open state.
   * Forward this to your local `setState` if you want two‑way binding.
   */
  onOpenChange?: (open: boolean) => void;
  /**
   * The headline shown at the top of the dialog.
   */
  title: React.ReactNode;
  /**
   * Optional body text placed under the title.
   */
  message?: React.ReactNode;
  /**
   * Label for the cancel button (defaults to "Cancel").
   */
  cancelLabel?: string;
  /**
   * Label for the confirm button (defaults to "OK").
   */
  confirmLabel?: string;
  /**
   * Called when the user presses the cancel button.
   */
  onCancel?: () => void;
  /**
   * Called when the user presses the confirm button.
   */
  onConfirm: () => void;
  /**
   * Visual style of the confirm button.
   * - "primary" → app primary brand color (default)
   * - "danger"  → red destructive action
   * - "secondary" → gray neutral action
   */
  confirmVariant?: "primary" | "danger" | "secondary";
  /**
   * Extra Tailwind classes to control the dialog width when you need a size
   * other than the default 372px.
   */
  widthClass?: string;
}

/**
 * Reusable confirmation dialog based on shadcn/ui `Dialog` primitives.
 *
 * ```tsx
 * const [open, setOpen] = useState(false);
 *
 * <ConfirmDialog
 *   open={open}
 *   onOpenChange={setOpen}
 *   title={"ログアウトします\nよろしいですか？"}
 *   cancelLabel="戻る"
 *   confirmLabel="ログアウト"
 *   confirmVariant="danger"
 *   onCancel={() => setOpen(false)}
 *   onConfirm={handleLogout}
 * />
 * ```
 */
const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  onOpenChange,
  title,
  message,
  cancelLabel = "Cancel",
  confirmLabel = "OK",
  onCancel,
  onConfirm,
  confirmVariant = "primary",
  widthClass = "w-[372px]",
}) => {
  const confirmClasses = React.useMemo(() => {
    switch (confirmVariant) {
      case "danger":
        return "bg-[#c94e4e] hover:bg-[#c93838] text-white";
      case "secondary":
        return "bg-gray-400 hover:bg-gray-500 text-white";
      default:
        // "primary"
        return "bg-primary hover:bg-primary/90 text-white";
    }
  }, [confirmVariant]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={clsx(
          "p-6 lg:rounded-[25px] rounded-[25px] bg-white h-[315px] [&>button]:hidden",
          widthClass
        )}
      >
        <DialogHeader className="flex items-center justify-center flex-1">
          <DialogTitle className="whitespace-pre-wrap text-[20px] text-[#6F6F6E] font-normal text-center leading-relaxed">
            {title}
          </DialogTitle>
        </DialogHeader>

        {message && (
          <p className="text-center text-sm text-muted-foreground mb-4">
            {message}
          </p>
        )}

        <DialogFooter className="!flex !flex-row !gap-3 !justify-center !items-center !w-full">
          <Button
            variant="secondary"
            onClick={onCancel}
            className="bg-gray-400 hover:bg-gray-500 text-white rounded-xl text-[16px] font-[400]"
            style={{ width: "122px", height: "42px" }}
          >
            {cancelLabel}
          </Button>

          <Button
            onClick={onConfirm}
            className={clsx(
              confirmClasses,
              "rounded-xl text-[16px] font-[400]"
            )}
            style={{ width: "122px", height: "42px" }}
          >
            {confirmLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmDialog;