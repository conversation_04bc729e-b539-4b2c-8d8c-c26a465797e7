import { z } from "zod";
import { validate<PERSON><PERSON>i, validate<PERSON>ideK<PERSON>, validateWideString } from "../utils/validate";

// Schema validation for legal entity form
export const houjinSchema = z.object({
    agxCorporateName: z.string().trim().nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxCorporatePhoneticName: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxCorporateEnglishName: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return !validateAscii(value);
        }, {
            message: "※半角英数字で入力してください。"
        }),
    agxCorporateNumber: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((val) => /^\d*$/.test(val), {
            message: "※半角数字で入力してください。",
        })
        .refine((value) => {
            return value.length === 13;
        }, {
            message: "※13文字で入力してください。"
        }),
    agxCorporatePostalCode: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .regex(/^(\d{7}|\d{3}-\d{4})$/, {
            message: "※書式が不正です。"
        }),
    agxCorporatePrefecture: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        }),
    agxCorporateAddress1: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxCorporateAddress2: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideString(value, true);
        }, {
            message: "※全角文字で入力してください。"
        }),
    agxCorporatePhoneticAddress1: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxCorporatePhoneticAddress2: z.string().trim()
        .nonempty({
            message: "※必須項目です、値を入力してください。"
        })
        .refine((value) => {
            return validateWideKana(value, true);
        }, {
            message: "※全角カタカナで入力してください。"
        }),
    agxCorporatePhoneNumber1: z.string().trim().optional(),
    agxCorporatePhoneNumber2: z.string().trim().optional(),
    agxCorporatePhoneNumber3: z.string().trim().optional(),
    agxCorporateFaxNumber1: z.string().trim().optional(),
    agxCorporateFaxNumber2: z.string().trim().optional(),
    agxCorporateFaxNumber3: z.string().trim().optional(),
}).refine((data) => {

    const allThreeFilled = (
        data.agxCorporatePhoneNumber1 && data.agxCorporatePhoneNumber1.trim() !== "" &&
        data.agxCorporatePhoneNumber2 && data.agxCorporatePhoneNumber2.trim() !== "" &&
        data.agxCorporatePhoneNumber3 && data.agxCorporatePhoneNumber3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxCorporatePhoneNumber1"],
}).refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxCorporatePhoneNumber1 || "")
        && pat.test(data.agxCorporatePhoneNumber2 || "")
        && pat.test(data.agxCorporatePhoneNumber3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxCorporatePhoneNumber1"]
})
.refine((data) => {
    const hasAtLeastOneFax = (
        (data.agxCorporateFaxNumber1 && data.agxCorporateFaxNumber1.trim() !== "") ||
        (data.agxCorporateFaxNumber2 && data.agxCorporateFaxNumber2.trim() !== "") ||
        (data.agxCorporateFaxNumber3 && data.agxCorporateFaxNumber3.trim() !== "")
    );

    if (!hasAtLeastOneFax) {
        return true;
    }

    const allThreeFilled = (
        data.agxCorporateFaxNumber1 && data.agxCorporateFaxNumber1.trim() !== "" &&
        data.agxCorporateFaxNumber2 && data.agxCorporateFaxNumber2.trim() !== "" &&
        data.agxCorporateFaxNumber3 && data.agxCorporateFaxNumber3.trim() !== ""
    );

    return allThreeFilled;
}, {
    message: "※必須項目です、値を入力してください。",
    path: ["agxCorporateFaxNumber1"],
}).refine((data) => {
    // eslint-disable-next-line no-useless-escape
    const pat = /^[\d\-\(\) ]*$/;
    return pat.test(data.agxCorporateFaxNumber1 || "")
        && pat.test(data.agxCorporateFaxNumber2 || "")
        && pat.test(data.agxCorporateFaxNumber3 || "");
}, {
    message: "※書式が不正です。",
    path: ["agxCorporateFaxNumber1"]
});

export type HoujinFormData = z.infer<typeof houjinSchema>; 