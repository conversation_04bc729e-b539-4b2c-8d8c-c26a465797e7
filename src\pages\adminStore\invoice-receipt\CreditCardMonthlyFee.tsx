import { useParams } from 'react-router-dom';
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { formatNumber } from '@/utils/dateUtils';
import { axiosPDF } from "@/features/store/invoice-receipt/utils";
import { useAuthStore } from '@/store';
import { useGetDataCreditCardMonthlyFee } from '@/features/adminStore/invoice-receipt/hooks/useGetDataCreditCardMonthlyFee';
import PDFService, { blobToBase64, PDFConfig } from '@/services/pdfService';
import { Download } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const CreditCardMonthlyFee = () => {
  const { user } = useAuthStore();
  const { month, type } = useParams<{ month: string, type: string }>();

  // Use the hook to get data
  const { data, isLoading } = useGetDataCreditCardMonthlyFee(user.agxMerchantNo, month, type);

  // Handle loading state
  if (isLoading) {
    return <LoadingSpinner />;
  }

  const getRecordDetailPDF = () => {
      const result = [[{
          text: '加盟店番号',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '店舗名',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '数量',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '単価',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '金額',
          style: 'tableHeader',
          alignment: 'center'
      }]];

      for (const index in data) {
          const cols = [];
          cols.push({ text: data[index].merchantNo, alignment: 'left' },
              { text: data[index].storeName, alignment: 'left' },
              { text: formatNumber(data[index].quantity), alignment: 'right' },
              { text: formatNumber(data[index].price), alignment: 'right' },
              { text: formatNumber(data[index].amount), alignment: 'right' }
          );
          result.push(cols);
      }
      return result;
  }

  const handleExportPDF = async () => {
      try {
        // Check if data exists first
        if (!data || data.length === 0) {
            alert('データが見つかりません。');
            return;
        }

        // Setup fonts first and wait for them to be ready
        const responseYugothib = await axiosPDF.get('yugothib.ttf');
        if (responseYugothib.status !== 200) {
            alert('フォントの読み込みに失敗しました。');
            return;
        }
        const fontYugothib = await blobToBase64(responseYugothib.data);

        const responseArial = await axiosPDF.get('arial.ttf');
        if (responseArial.status !== 200) {
            alert('フォントの読み込みに失敗しました。');
            return;
        }
        const fontArial = await blobToBase64(responseArial.data);

        // Setup fonts and wait for completion
        await PDFService.setupMultipleFonts(fontYugothib, fontArial);

        const details = getRecordDetailPDF();

        const config: PDFConfig = {
            pageSize: { width: 640, height: 900 } as any,
            pageOrientation: 'portrait',
            defaultStyle: {
                font: 'yugothib'
            },
            background: function (page) {
                if (page === 1) {
                    return [];
                }
            },
            content: [
                { text: type, fontSize: 20, alignment: 'right', margin: [0, 20, 0, 0] },
                {
                    margin: [0, 20, 0, 0],
                    table: {
                        headerRows: 1,
                        widths: [100, 180, 70, 70, '*'],
                        body: details
                    },
                    layout: {
                        fillColor: function (rowIndex, node, columnIndex) {
                            return (rowIndex === 0) ? '#757575' : null;
                        }
                    },
                    style: 'tableMargin',
                },
            ],
            styles: {
                header: {
                    fontSize: 18,
                    bold: true,
                    margin: [0, 0, 0, 10]
                },
                tableMargin: {
                    margin: [20, 10, 20, 15]
                },
                tableHeader: {
                    bold: true,
                    fontSize: 13,
                    color: 'white'
                }
            },
            filename: `${type}_merchant.pdf`
        };

          // Create PDF after fonts are properly loaded
        await PDFService.createPDFWithSetupFonts(config);   
      } catch (error) {
          console.error('PDF作成エラー:', error);
          alert('PDF作成に失敗しました。');
      }
  };
    
  return (
    <div className="p-4 md:py-6 md:px-1 lg:p-6">
      <div className="page-heading">
        <div className="flex">
          <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">{type}</h2>
          <button id="download-pdf" className="p-2 pl-4 text-[#1D9987] hover:text-[#1D9987]/80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" onClick={handleExportPDF}>
            <Download className="h-6 w-6" />
          </button>
        </div>
        <hr className='mt-3 border-1 border-gray-500' />
      </div>
      <div className="py-6">
        <div className="rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='text-[18px] font-medium text-[#6F6F6E]'>加盟店番号</TableHead>
                <TableHead className='text-[18px] font-medium text-[#6F6F6E]'>店舗名</TableHead>
                <TableHead className='text-[18px] font-medium text-[#6F6F6E] text-right'>数量</TableHead>
                <TableHead className='text-[18px] font-medium text-[#6F6F6E] text-right'>単価</TableHead>
                <TableHead className='text-[18px] font-medium text-[#6F6F6E] text-right'>金額</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.length > 0 ? (
                data.map((item, index) => (
                  <TableRow key={index} className='text-[16px] text-[#6F6F6E]'>
                    <TableCell className='font-medium'>{item.merchantNo}</TableCell>
                    <TableCell>{item.storeName}</TableCell>
                    <TableCell className='text-right'>{formatNumber(item.quantity)}</TableCell>
                    <TableCell className='text-right'>{formatNumber(item.price)}</TableCell>
                    <TableCell className='text-right'>{formatNumber(item.amount)}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className='text-[#6F6F6E] text-center'>
                    データがありません。
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}

export default CreditCardMonthlyFee;