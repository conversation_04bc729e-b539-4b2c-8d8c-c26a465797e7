import React from 'react';

interface CopyOptions {
  copyFromCorporate: boolean;
  copyFromRepresentative: boolean;
}

interface CopyOptionsFieldsProps {
  value: CopyOptions;
  onChange: (value: CopyOptions) => void;
}

export const CopyOptionsFields: React.FC<CopyOptionsFieldsProps> = ({
  value,
  onChange
}) => {
  const handleCopyFromCorporateChange = (copyFromCorporate: boolean) => {
    onChange({ ...value, copyFromCorporate });
  };

  const handleCopyFromRepresentativeChange = (copyFromRepresentative: boolean) => {
    onChange({ ...value, copyFromRepresentative });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <input
          type="checkbox"
          id="copyFromCorporate"
          checked={value.copyFromCorporate}
          onChange={(e) => handleCopyFromCorporateChange(e.target.checked)}
          className="w-4 h-4 text-[#4ECDC4] bg-gray-100 border-gray-300 rounded focus:ring-[#4ECDC4] focus:ring-2"
        />
        <label htmlFor="copyFromCorporate" className="text-sm md:text-base text-gray-700">
          法人住所をコピーする
        </label>
      </div>
      
      <div className="flex items-center gap-3">
        <input
          type="checkbox"
          id="copyFromRepresentative"
          checked={value.copyFromRepresentative}
          onChange={(e) => handleCopyFromRepresentativeChange(e.target.checked)}
          className="w-4 h-4 text-[#4ECDC4] bg-gray-100 border-gray-300 rounded focus:ring-[#4ECDC4] focus:ring-2"
        />
        <label htmlFor="copyFromRepresentative" className="text-sm md:text-base text-gray-700">
          代表者住所をコピーする
        </label>
      </div>
    </div>
  );
}; 