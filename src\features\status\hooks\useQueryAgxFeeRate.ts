import { useQuery } from "@tanstack/react-query";
import agxFeeRateService from "../services/agxFeeRateService";

export const useQueryAgxFeeRate = ({agxBusinessType}: {agxBusinessType: number}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['agxFeeRate', agxBusinessType],
    queryFn: async () => agxFeeRateService.getAllData(agxBusinessType)  
  });

  return {
    data, isLoading, isError, error, refetch
  };
}