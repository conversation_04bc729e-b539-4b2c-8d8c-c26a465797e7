import { Card, CardContent } from "@/components/ui/card";
import { convertPhoneAndFax } from "@/utils/helper";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";

interface StoreInformationProps {
  merchantData: GetMerchantStatusResponse;
}

function StoreInformation({ merchantData }: StoreInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-14">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            店舗名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            店舗名（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            英語表記名称<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreEnglishName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E] pr-3">
            WEBサイトURL
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxUrl ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E] pr-3">
            ブランド名
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxBrandName ?? ""}
            </div>
          </div>
        </div>

        {/* <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">営業日</Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxBusinessDate ?? ""}
            </div>
          </div>
        </div> */}

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E] pr-3">定休日</Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxRegularHoliday ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E] pr-3">営業時間</Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxBusinesssHours ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            郵便番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePostalCode ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            都道府県<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePrefecture ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            市町村<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            市町村（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePhoneticAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            丁目番地建物名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            丁目番地建物名（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePhoneticAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            電話番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md pl-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[0] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[1] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md flex items-center">
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[1] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[2] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md flex items-center">
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E] pr-3">FAX</Label>
          <div className="col-span-6 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md pl-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[0] || ""}
            </div>
            <span>{convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[1] ? "-" : ""}</span>
            <div className="w-auto h-7 rounded-md flex items-center">
              {convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[1] || ""}
            </div>
            <span>{convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[2] ? "-" : ""}</span>
            <div className="w-auto h-7 rounded-md flex items-center">
              {convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[2] || ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default StoreInformation;
