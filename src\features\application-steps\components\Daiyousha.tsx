import Box from "@/components/ui/box";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormButtons } from "./ui/FormButtons";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import useAddressDaiyousha from "../hooks/userAddressDaiyousha";
import { PrefectureSelect, ThreePartNumberInput } from "./ui/AddressInputs";
import { STEP } from "@/constants/common.constant";
import { Checkbox } from "@/components/ui/checkbox";
import { DaiyoushaFormData, daiyoushaSchema } from "../schemas";
import ConfirmDialog from "@/components/ConfirmDialog";
import { gender } from "../utils";
import { FormInput } from "./ui/Field";

const startYear = 1912;
const endYear = (new Date().getFullYear()) * 1;

const Daiyousha = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {

    const [copyAddressFromCorporate, setCopyAddressFromCorporate] = useState(false);
    const [show, setShow] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        trigger,
        setValue,
        watch,
        getValues,
    } = useForm<DaiyoushaFormData>({
        resolver: zodResolver(daiyoushaSchema),
        mode: "onBlur",
        reValidateMode: "onBlur",
        defaultValues: {
            agxRepresentativeLastName: "",
            agxRepresentativeFirstName: "",
            agxRepresentativePhoneticLastName: "",
            agxRepresentativePhoneticFirstName: "",
            agxRepresentativeGender: 283260000,
            representativeBirthdayYear: "1973",
            representativeBirthdayMonth: "01",
            representativeBirthdayDay: "01",
            agxRepresentativeAddressCopyFlag: false,
            agxRepresentativeAddress1: "",
            agxRepresentativeAddress2: "",
            agxRepresentativePhoneticAddress1: "",
            agxRepresentativePhoneticAddress2: "",
            agxRepresentativePhoneNumber1: "",
            agxRepresentativePhoneNumber2: "",
            agxRepresentativePhoneNumber3: "",
            agxRepresentativeFaxNumber1: "",
            agxRepresentativeFaxNumber2: "",
            agxRepresentativeFaxNumber3: "",
        }
    });

    const onSubmit = async () => {

        const isValid = await trigger();
        if (!isValid) {
            return false;
        }

        const formValues = getValues();

        // Gắn lastName và firstName thành fullName nếu cả 2 đều có giá trị
        const lastName = formValues.agxRepresentativeLastName?.trim() || "";
        const firstName = formValues.agxRepresentativeFirstName?.trim() || "";
        const phoneticLastName = formValues.agxRepresentativePhoneticLastName?.trim() || "";
        const phoneticFirstName = formValues.agxRepresentativePhoneticFirstName?.trim() || "";

        // Tạo chuỗi số điện thoại đầy đủ từ các phần riêng lẻ
        const phoneNumber1 = formValues.agxRepresentativePhoneNumber1?.trim() || "";
        const phoneNumber2 = formValues.agxRepresentativePhoneNumber2?.trim() || "";
        const phoneNumber3 = formValues.agxRepresentativePhoneNumber3?.trim() || "";
        const agxRepresentativePhoneNumber = (phoneNumber1 || phoneNumber2 || phoneNumber3) 
            ? `${phoneNumber1}-${phoneNumber2}-${phoneNumber3}` 
            : "";

        // Tạo chuỗi số fax đầy đủ từ các phần riêng lẻ
        const faxNumber1 = formValues.agxRepresentativeFaxNumber1?.trim() || "";
        const faxNumber2 = formValues.agxRepresentativeFaxNumber2?.trim() || "";
        const faxNumber3 = formValues.agxRepresentativeFaxNumber3?.trim() || "";
        const agxRepresentativeFaxNumber = (faxNumber1 || faxNumber2 || faxNumber3) 
            ? `${faxNumber1}-${faxNumber2}-${faxNumber3}` 
            : "";

        const updatedValues = {
            ...formValues,
            ...(lastName && firstName && { agxRepresentativeName: `${lastName} ${firstName}` }),
            ...(phoneticLastName && phoneticFirstName && { agxRepresentativePhoneticName: `${phoneticLastName} ${phoneticFirstName}` }),
            ...(agxRepresentativePhoneNumber && { agxRepresentativePhoneNumber }),
            ...(agxRepresentativeFaxNumber && { agxRepresentativeFaxNumber })
        };

        setAgxMerchantParams({
            ...agxMerchantParams,
            ...updatedValues,
        } as AgxMerchantParams);

        setStep(STEP.SHOPINFO);
    };


    const showConfirmDialog = async () => {
        const isValid = await trigger();
        if (!isValid) {
            return false;
        }
        setShow(true);
    }

    const onSave = async () => {

        const isValid = await trigger();
        if (!isValid) {
            return false;
        }

        const formValues = getValues();
        const lastName = formValues.agxRepresentativeLastName?.trim() || "";
        const firstName = formValues.agxRepresentativeFirstName?.trim() || "";
        const phoneticLastName = formValues.agxRepresentativePhoneticLastName?.trim() || "";
        const phoneticFirstName = formValues.agxRepresentativePhoneticFirstName?.trim() || "";
        
        // Tạo chuỗi số điện thoại đầy đủ từ các phần riêng lẻ
        const phoneNumber1 = formValues.agxRepresentativePhoneNumber1?.trim() || "";
        const phoneNumber2 = formValues.agxRepresentativePhoneNumber2?.trim() || "";
        const phoneNumber3 = formValues.agxRepresentativePhoneNumber3?.trim() || "";
        const agxRepresentativePhoneNumber = (phoneNumber1 || phoneNumber2 || phoneNumber3) 
            ? `${phoneNumber1}-${phoneNumber2}-${phoneNumber3}` 
            : "";

        // Tạo chuỗi số fax đầy đủ từ các phần riêng lẻ
        const faxNumber1 = formValues.agxRepresentativeFaxNumber1?.trim() || "";
        const faxNumber2 = formValues.agxRepresentativeFaxNumber2?.trim() || "";
        const faxNumber3 = formValues.agxRepresentativeFaxNumber3?.trim() || "";
        const agxRepresentativeFaxNumber = (faxNumber1 || faxNumber2 || faxNumber3) 
            ? `${faxNumber1}-${faxNumber2}-${faxNumber3}` 
            : "";
        
        const updatedValues = {
            ...formValues,
            ...(lastName && firstName && { agxRepresentativeName: `${lastName} ${firstName}` }),
            ...(phoneticLastName && phoneticFirstName && { agxRepresentativePhoneticName: `${phoneticLastName} ${phoneticFirstName}` }),
            ...(formValues.representativeBirthdayYear && formValues.representativeBirthdayMonth && formValues.representativeBirthdayDay && { agxRepresentativeBirthday: `${formValues.representativeBirthdayYear}-${formValues.representativeBirthdayMonth}-${formValues.representativeBirthdayDay}` }),
            ...(agxRepresentativePhoneNumber && { agxRepresentativePhoneNumber }),
            ...(agxRepresentativeFaxNumber && { agxRepresentativeFaxNumber })
        };
        updateMerchant({ ...agxMerchantParams, ...updatedValues } as AgxMerchantParams);

        setShow(false);
    }

    const handleBack = async () => {
        // Lưu dữ liệu form hiện tại vào agxMerchantParams trước khi back
        const formValues = getValues();
        const lastName = formValues.agxRepresentativeLastName?.trim() || "";
        const firstName = formValues.agxRepresentativeFirstName?.trim() || "";
        const phoneticLastName = formValues.agxRepresentativePhoneticLastName?.trim() || "";
        const phoneticFirstName = formValues.agxRepresentativePhoneticFirstName?.trim() || "";

        // Tạo chuỗi số điện thoại đầy đủ từ các phần riêng lẻ
        const phoneNumber1 = formValues.agxRepresentativePhoneNumber1?.trim() || "";
        const phoneNumber2 = formValues.agxRepresentativePhoneNumber2?.trim() || "";
        const phoneNumber3 = formValues.agxRepresentativePhoneNumber3?.trim() || "";
        const agxRepresentativePhoneNumber = (phoneNumber1 || phoneNumber2 || phoneNumber3) 
            ? `${phoneNumber1}-${phoneNumber2}-${phoneNumber3}` 
            : "";

        // Tạo chuỗi số fax đầy đủ từ các phần riêng lẻ
        const faxNumber1 = formValues.agxRepresentativeFaxNumber1?.trim() || "";
        const faxNumber2 = formValues.agxRepresentativeFaxNumber2?.trim() || "";
        const faxNumber3 = formValues.agxRepresentativeFaxNumber3?.trim() || "";
        const agxRepresentativeFaxNumber = (faxNumber1 || faxNumber2 || faxNumber3) 
            ? `${faxNumber1}-${faxNumber2}-${faxNumber3}` 
            : "";

        const updatedValues = {
            ...formValues,
            ...(lastName && firstName && { agxRepresentativeName: `${lastName} ${firstName}` }),
            ...(phoneticLastName && phoneticFirstName && { agxRepresentativePhoneticName: `${phoneticLastName} ${phoneticFirstName}` }),
            ...(formValues.representativeBirthdayYear && formValues.representativeBirthdayMonth && formValues.representativeBirthdayDay && { agxRepresentativeBirthday: `${formValues.representativeBirthdayYear}-${formValues.representativeBirthdayMonth}-${formValues.representativeBirthdayDay}` }),
            ...(agxRepresentativePhoneNumber && { agxRepresentativePhoneNumber }),
            ...(agxRepresentativeFaxNumber && { agxRepresentativeFaxNumber })
        };

        setAgxMerchantParams({
            ...agxMerchantParams,
            ...updatedValues,
        } as AgxMerchantParams);

        setStep(STEP.HOUJIN);
    };

    // Sử dụng useAddress hook
    const {
        // States
        agxRepresentativePostalCode,
        stateErrorPostalCode,
        agxRepresentativePhoneNumber1,
        agxRepresentativePhoneNumber2,
        agxRepresentativePhoneNumber3,
        stateErrorPhoneNumber,
        agxRepresentativeFaxNumber1,
        agxRepresentativeFaxNumber2,
        agxRepresentativeFaxNumber3,
        stateErrorFaxNumber,
        isLoadingAddress,
        handleFindAddress,

        // Handlers
        handleChangeAgxRepresentativePostalCode,
        handleSetDataAgxRepresentativePostalCode,
        handleChangeAgxRepresentativePhoneNumber1,
        handleChangeAgxRepresentativePhoneNumber2,
        handleChangeAgxRepresentativePhoneNumber3,
        handleSetDataAgxRepresentativePhoneNumber,
        handleChangeAgxRepresentativeFaxNumber1,
        handleChangeAgxRepresentativeFaxNumber2,
        handleChangeAgxRepresentativeFaxNumber3,
        handleSetDataAgxRepresentativeFaxNumber,

        // Setters for external access
        setAgxRepresentativePostalCode,
        setAgxRepresentativePhoneNumber1,
        setAgxRepresentativePhoneNumber2,
        setAgxRepresentativePhoneNumber3,
        setAgxRepresentativeFaxNumber1,
        setAgxRepresentativeFaxNumber2,
        setAgxRepresentativeFaxNumber3,
    } = useAddressDaiyousha(agxMerchantParams);

    useEffect(() => {
        if (agxMerchantParams) {
            setValue("agxRepresentativeLastName", agxMerchantParams?.agxRepresentativeName?.split(" ")[0] || "");
            setValue("agxRepresentativeFirstName", agxMerchantParams?.agxRepresentativeName?.split(" ")[1] || "");
            setValue("agxRepresentativePhoneticLastName", agxMerchantParams?.agxRepresentativePhoneticName?.split(" ")[0] || "");
            setValue("agxRepresentativePhoneticFirstName", agxMerchantParams?.agxRepresentativePhoneticName?.split(" ")[1] || "");
            setValue("agxRepresentativeGender", agxMerchantParams?.agxRepresentativeGender ? Number(agxMerchantParams?.agxRepresentativeGender) : 283260000);
            setValue("agxRepresentativeBirthday", agxMerchantParams?.agxRepresentativeBirthday ? agxMerchantParams?.agxRepresentativeBirthday : (getValues("representativeBirthdayYear") + '-' + getValues("representativeBirthdayMonth") + '-' + getValues("representativeBirthdayDay")));
            setValue("representativeBirthdayYear", agxMerchantParams?.agxRepresentativeBirthday?.split("-")[0] || "1973");
            setValue("representativeBirthdayMonth", agxMerchantParams?.agxRepresentativeBirthday?.split("-")[1]?.padStart(2, '0') || "01");
            setValue("representativeBirthdayDay", agxMerchantParams?.agxRepresentativeBirthday?.split("-")[2]?.padStart(2, '0') || "01");
            setValue("agxRepresentativePostalCode", agxMerchantParams?.agxRepresentativePostalCode || "");
            
            // Set form values và đồng bộ với hook state cho số điện thoại
            const phoneNumber1 = agxMerchantParams?.agxRepresentativePhoneNumber?.split("-")[0] || "";
            const phoneNumber2 = agxMerchantParams?.agxRepresentativePhoneNumber?.split("-")[1] || "";
            const phoneNumber3 = agxMerchantParams?.agxRepresentativePhoneNumber?.split("-")[2] || "";
            setValue("agxRepresentativePhoneNumber1", phoneNumber1);
            setValue("agxRepresentativePhoneNumber2", phoneNumber2);
            setValue("agxRepresentativePhoneNumber3", phoneNumber3);
            // Đồng bộ với hook state
            setAgxRepresentativePhoneNumber1(phoneNumber1);
            setAgxRepresentativePhoneNumber2(phoneNumber2);
            setAgxRepresentativePhoneNumber3(phoneNumber3);
            
            // Set form values và đồng bộ với hook state cho số fax
            const faxNumber1 = agxMerchantParams?.agxRepresentativeFaxNumber?.split("-")[0] || "";
            const faxNumber2 = agxMerchantParams?.agxRepresentativeFaxNumber?.split("-")[1] || "";
            const faxNumber3 = agxMerchantParams?.agxRepresentativeFaxNumber?.split("-")[2] || "";
            setValue("agxRepresentativeFaxNumber1", faxNumber1);
            setValue("agxRepresentativeFaxNumber2", faxNumber2);
            setValue("agxRepresentativeFaxNumber3", faxNumber3);
            // Đồng bộ với hook state
            setAgxRepresentativeFaxNumber1(faxNumber1);
            setAgxRepresentativeFaxNumber2(faxNumber2);
            setAgxRepresentativeFaxNumber3(faxNumber3);
            
            setValue("agxRepresentativePrefecture", agxMerchantParams?.agxRepresentativePrefecture || "北海道");
            setValue("agxRepresentativeAddress1", agxMerchantParams?.agxRepresentativeAddress1 || "");
            setValue("agxRepresentativeAddress2", agxMerchantParams?.agxRepresentativeAddress2 || "");
            setValue("agxRepresentativePhoneticAddress1", agxMerchantParams?.agxRepresentativePhoneticAddress1 || "");
            setValue("agxRepresentativePhoneticAddress2", agxMerchantParams?.agxRepresentativePhoneticAddress2 || "");
            setValue("agxRepresentativeAddressCopyFlag", agxMerchantParams?.agxRepresentativeAddressCopyFlag || false);
            setCopyAddressFromCorporate(agxMerchantParams?.agxRepresentativeAddressCopyFlag || false);
        }
    }, [agxMerchantParams, setValue, setAgxRepresentativePhoneNumber1, setAgxRepresentativePhoneNumber2, setAgxRepresentativePhoneNumber3, setAgxRepresentativeFaxNumber1, setAgxRepresentativeFaxNumber2, setAgxRepresentativeFaxNumber3]);

    // Wrapper handlers để đồng bộ react-hook-form với hook state
    const handlePhoneNumber1Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxRepresentativePhoneNumber1", value);
        handleChangeAgxRepresentativePhoneNumber1(e);
    };

    const handlePhoneNumber2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxRepresentativePhoneNumber2", value);
        handleChangeAgxRepresentativePhoneNumber2(e);
    };

    const handlePhoneNumber3Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxRepresentativePhoneNumber3", value);
        handleChangeAgxRepresentativePhoneNumber3(e);
    };

    const handleFaxNumber1Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxRepresentativeFaxNumber1", value);
        handleChangeAgxRepresentativeFaxNumber1(e);
    };

    const handleFaxNumber2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxRepresentativeFaxNumber2", value);
        handleChangeAgxRepresentativeFaxNumber2(e);
    };

    const handleFaxNumber3Change = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setValue("agxRepresentativeFaxNumber3", value);
        handleChangeAgxRepresentativeFaxNumber3(e);
    };

    // Hàm xử lý khi checkbox thay đổi
    const handleChangeAgxRepresentativeAddressCopyFlag = (checked: boolean) => {
        setCopyAddressFromCorporate(checked);

        if (checked) {
            // Copy từ thông tin pháp nhân sang đại diện
            const corporateData = agxMerchantParams;

            // Copy postal code
            setValue("agxRepresentativePostalCode", corporateData.agxCorporatePostalCode || "");

            // Copy prefecture và address
            setValue("agxRepresentativePrefecture", corporateData.agxCorporatePrefecture || "北海道");
            setValue("agxRepresentativeAddress1", corporateData.agxCorporateAddress1 || "");
            setValue("agxRepresentativePhoneticAddress1", corporateData.agxCorporatePhoneticAddress1 || "");
            setValue("agxRepresentativeAddress2", corporateData.agxCorporateAddress2 || "");
            setValue("agxRepresentativePhoneticAddress2", corporateData.agxCorporatePhoneticAddress2 || "");

            // Copy phone number
            if (corporateData.agxCorporatePhoneNumber) {
                const phoneParts = corporateData.agxCorporatePhoneNumber.split("-");
                setValue("agxRepresentativePhoneNumber1", phoneParts[0] || "");
                setValue("agxRepresentativePhoneNumber2", phoneParts[1] || "");
                setValue("agxRepresentativePhoneNumber3", phoneParts[2] || "");
            }

            // Copy fax number
            if (corporateData.agxCorporateFaxNumber) {
                const faxParts = corporateData.agxCorporateFaxNumber.split("-");
                setValue("agxRepresentativeFaxNumber1", faxParts[0] || "");
                setValue("agxRepresentativeFaxNumber2", faxParts[1] || "");
                setValue("agxRepresentativeFaxNumber3", faxParts[2] || "");
            }

            // Cập nhật state trong hook useAddressDaiyousha
            if (corporateData.agxCorporatePostalCode) {
                setAgxRepresentativePostalCode(corporateData.agxCorporatePostalCode);
            }
            if (corporateData.agxCorporatePhoneNumber) {
                const phoneParts = corporateData.agxCorporatePhoneNumber.split("-");
                setAgxRepresentativePhoneNumber1(phoneParts[0] || "");
                setAgxRepresentativePhoneNumber2(phoneParts[1] || "");
                setAgxRepresentativePhoneNumber3(phoneParts[2] || "");
            }
            if (corporateData.agxCorporateFaxNumber) {
                const faxParts = corporateData.agxCorporateFaxNumber.split("-");
                setAgxRepresentativeFaxNumber1(faxParts[0] || "");
                setAgxRepresentativeFaxNumber2(faxParts[1] || "");
                setAgxRepresentativeFaxNumber3(faxParts[2] || "");
            }
        }
        trigger([
            "agxRepresentativePostalCode", 
            "agxRepresentativePrefecture", 
            "agxRepresentativeAddress1", 
            "agxRepresentativePhoneticAddress1", 
            "agxRepresentativeAddress2", 
            "agxRepresentativePhoneticAddress2",
            "agxRepresentativePhoneNumber1",
            "agxRepresentativePhoneNumber2",
            "agxRepresentativePhoneNumber3",
            "agxRepresentativeFaxNumber1",
            "agxRepresentativeFaxNumber2",
            "agxRepresentativeFaxNumber3"
        ])
        setValue("agxRepresentativeAddressCopyFlag", checked);
    };

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box className="mx-auto mb-32 md:mb-32 lg:mb-32 mt-4 md:mt-6 lg:mt-[41px] space-y-4 md:space-y-6 lg:space-y-[41px]">
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                        お名前<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full md:max-w-[300px] md:flex-1 flex flex-col">
                            <input
                                {...register("agxRepresentativeLastName")}
                                onBlur={() => trigger(["agxRepresentativeLastName", "agxRepresentativeFirstName"])}
                                maxLength={100}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="姓（全角）"
                            />
                            <div className="min-h-[16px] md:min-h-[20px] mt-1">
                                {errors.agxRepresentativeLastName && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                                        {errors.agxRepresentativeLastName.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full md:max-w-[300px] md:flex-1 flex flex-col">
                            <input
                                {...register("agxRepresentativeFirstName")}
                                onBlur={() => trigger(["agxRepresentativeLastName", "agxRepresentativeFirstName"])}
                                maxLength={100}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="名（全角）"
                            />
                        </Box>
                    </Box>

                    {/* Corporate name field (Kana) */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            お名前（カナ)<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full md:max-w-[300px] md:flex-1 flex flex-col">
                            <input
                                {...register("agxRepresentativePhoneticLastName")}
                                onBlur={() => trigger(["agxRepresentativePhoneticLastName", "agxRepresentativePhoneticFirstName"])}
                                maxLength={100}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="姓カナ（全角カナ）"
                            />
                            <div className="min-h-[16px] md:min-h-[20px] mt-1">
                                {errors.agxRepresentativePhoneticLastName && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                                        {errors.agxRepresentativePhoneticLastName.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full md:max-w-[300px] md:flex-1 flex flex-col">
                            <input
                                {...register("agxRepresentativePhoneticFirstName")}
                                onBlur={() => trigger(["agxRepresentativePhoneticLastName", "agxRepresentativePhoneticFirstName"])}
                                maxLength={100}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-3 md:px-4 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="名カナ（全角カナ）"
                            />
                        </Box>
                    </Box>

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            性別<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full md:max-w-[250px] lg:max-w-[388px] md:flex-1 relative flex flex-col">
                            <select
                                {...register("agxRepresentativeGender")}
                                className="
                                w-full h-[40px] md:h-[50px] lg:h-[66px]
                                rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                border border-solid border-[#707070]
                                px-3 md:px-4 lg:px-[17px]
                                py-0 md:py-0 lg:py-0
                                pr-10 md:pr-12 lg:pr-14
                                text-sm md:text-base lg:text-[1.75rem] 
                                text-[#707070] bg-white
                                leading-[40px] md:leading-[50px] lg:leading-[66px]
                                focus:border-[#1D9987] focus:outline-none transition-colors 
                                appearance-none
                                disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                            >
                                <option value="283260000">{gender(283260000)}</option>
                                <option value="283260001">{gender(283260001)}</option>
                                <option value="283260002">{gender(283260002)}</option>
                            </select>
                            {/* Custom dropdown arrow */}
                            <div className="absolute right-0 top-[50%] -translate-y-1/2 flex items-center pr-2 md:pr-3 lg:pr-4 pointer-events-none">
                                <svg className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                            </div>
                        </Box>
                        <Box className="hidden md:block md:flex-1">
                            <p />
                        </Box>
                    </Box>

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            生年月日<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="flex flex-row gap-2 md:gap-4 lg:gap-6 w-full md:w-auto">
                            <Box className="w-[100px] md:w-[120px] lg:w-[152px] relative flex flex-col">
                                <select
                                    {...register("representativeBirthdayYear")}
                                    className="
                                    w-full h-[40px] md:h-[50px] lg:h-[66px]
                                    rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                    border border-solid border-[#707070]
                                    px-3 md:px-4 lg:px-[17px]
                                    py-0 md:py-0 lg:py-0
                                    pr-10 md:pr-12 lg:pr-14
                                    text-sm md:text-base lg:text-[1.75rem] 
                                    text-[#707070] bg-white
                                    leading-[40px] md:leading-[50px] lg:leading-[66px]
                                    focus:border-[#1D9987] focus:outline-none transition-colors 
                                    appearance-none
                                    disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                                >
                                    {Array.from({ length: endYear - startYear + 1 }, (_, i) => (
                                        <option key={i} value={startYear + i}>{startYear + i}</option>
                                    ))}
                                </select>
                                {/* Custom dropdown arrow */}
                                <div className="absolute inset-y-0 right-0 flex items-center pr-1 md:pr-2 lg:pr-4 pointer-events-none">
                                    <svg className="w-3 h-3 md:w-4 md:h-4 lg:w-5 lg:h-5 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
    
                            </Box>
                            <Box className="w-[80px] md:w-[100px] lg:w-[152px] relative flex flex-col">
                                <select
                                    {...register("representativeBirthdayMonth")}
                                    onChange={(e) => {
                                        setValue("agxRepresentativeBirthday", `${getValues("representativeBirthdayYear")}-${e.target.value}-${getValues("representativeBirthdayDay")}`);
                                    }}
                                    className="
                                        w-full h-[40px] md:h-[50px] lg:h-[66px]
                                        rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                        border border-solid border-[#707070]
                                        px-3 md:px-4 lg:px-[17px]
                                        py-0 md:py-0 lg:py-0
                                        pr-10 md:pr-12 lg:pr-14
                                        text-sm md:text-base lg:text-[1.75rem] 
                                        text-[#707070] bg-white
                                        leading-[40px] md:leading-[50px] lg:leading-[66px]
                                        focus:border-[#1D9987] focus:outline-none transition-colors 
                                        appearance-none
                                        disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                                >
                                    {Array.from({ length: 12 }, (_, i) => {
                                        const monthValue = (i + 1).toString().padStart(2, '0');
                                        return <option key={i} value={monthValue}>{i + 1}月</option>
                                    })}
                                </select>
                                {/* Custom dropdown arrow */}
                                <div className="absolute inset-y-0 right-0 flex items-center pr-1 md:pr-2 lg:pr-4 pointer-events-none">
                                    <svg className="w-3 h-3 md:w-4 md:h-4 lg:w-5 lg:h-5 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </Box>
                            <Box className="w-[80px] md:w-[100px] lg:w-[152px] relative flex flex-col">
                                <select
                                    {...register("representativeBirthdayDay")}
                                    onChange={(e) => {
                                        setValue("agxRepresentativeBirthday", `${getValues("representativeBirthdayYear")}-${getValues("representativeBirthdayMonth")}-${e.target.value}`);
                                    }}
                                    className="
                                    w-full h-[40px] md:h-[50px] lg:h-[66px]
                                    rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                                    border border-solid border-[#707070]
                                    px-3 md:px-4 lg:px-[17px]
                                    py-0 md:py-0 lg:py-0
                                    pr-10 md:pr-12 lg:pr-14
                                    text-sm md:text-base lg:text-[1.75rem] 
                                    text-[#707070] bg-white
                                    leading-[40px] md:leading-[50px] lg:leading-[66px]
                                    focus:border-[#1D9987] focus:outline-none transition-colors 
                                    appearance-none
                                    disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                                >
                                    {Array.from({ length: 31 }, (_, i) => {
                                        const dayValue = (i + 1).toString().padStart(2, '0');
                                        return <option key={i} value={dayValue}>{i + 1}日</option>
                                    })}
                                </select>
                                {/* Custom dropdown arrow */}
                                <div className="absolute inset-y-0 right-0 flex items-center pr-1 md:pr-2 lg:pr-4 pointer-events-none">
                                    <svg className="w-3 h-3 md:w-4 md:h-4 lg:w-5 lg:h-5 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </Box>
                        </Box>
                    </Box>

                    {
                        agxMerchantParams?.agxBusinessForm === 283260000 && (
                            <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 items-start relative">
                            <Box className="w-full md:w-[22.5%] pt-0 flex justify-start md:justify-end">
                                {/* Mobile layout: checkbox + text inline */}
                                <Box className="flex items-center gap-3 md:hidden">
                                    <Checkbox
                                        className={`w-[18px] h-[18px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0`}
                                        checked={copyAddressFromCorporate}
                                        onCheckedChange={handleChangeAgxRepresentativeAddressCopyFlag}
                                        disabled={false}
                                    />
                                    <Label className="text-[rgba(112,112,112,1)] cursor-pointer text-sm" onClick={() => handleChangeAgxRepresentativeAddressCopyFlag(!copyAddressFromCorporate)}>
                                        法人所在地の入力内容をコピーする
                                    </Label>
                                </Box>
                                {/* Desktop layout: only checkbox */}
                                <Checkbox
                                    className={`w-[22px] h-[22px] lg:w-[27px] lg:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 hidden md:block`}
                                    checked={copyAddressFromCorporate}
                                    onCheckedChange={handleChangeAgxRepresentativeAddressCopyFlag}
                                    disabled={false}
                                />
                            </Box>
                            <Box className="w-full md:flex-1 hidden md:block">
                                <Label className="text-[rgba(112,112,112,1)] cursor-pointer text-base lg:text-xl xl:text-[1.75rem] pt-0 md:pt-2" onClick={() => handleChangeAgxRepresentativeAddressCopyFlag(!copyAddressFromCorporate)}>
                                    法人所在地の入力内容をコピーする
                                </Label>
                            </Box>
                        </Box>
                        )
                    }

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            郵便番号<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full md:w-[250px] lg:w-[388px] flex flex-col">
                            <input
                                data-pattern="^(\d{7}|\d{3}-\d{4})$"
                                {...register("agxRepresentativePostalCode")}
                                onChange={handleChangeAgxRepresentativePostalCode}
                                onBlur={() => handleSetDataAgxRepresentativePostalCode(setValue, trigger)}
                                maxLength={8}
                                value={agxRepresentativePostalCode}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-3 md:px-4 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors disabled:bg-[#FAFAFA] disabled:cursor-not-allowed"
                                placeholder="000-0000"
                                disabled={copyAddressFromCorporate}
                            />
                            <div className="min-h-[16px] md:min-h-[20px] mt-1">
                                {errors.agxRepresentativePostalCode && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                                        {errors.agxRepresentativePostalCode.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full md:flex-1">
                            <Button
                                className="bg-[#A5A4A6] h-[40px] md:h-[50px] lg:h-[66px] w-full md:w-[200px] lg:w-[323px] text-sm md:text-base lg:text-[1.75rem] hover:bg-[#1D9987] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 rounded-lg transition-colors shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => handleFindAddress(setValue, trigger)}
                                disabled={isLoadingAddress || !agxRepresentativePostalCode || copyAddressFromCorporate}
                                type="button"
                            >
                                {isLoadingAddress ? "検索中..." : "住所情報を入力"}
                            </Button>
                        </Box>
                    </Box>

                    {/* Select prefecture */}
                    <PrefectureSelect
                        label="都道府県"
                        required
                        disabled={copyAddressFromCorporate}
                        value={watch("agxRepresentativePrefecture")}
                        onChange={(e) => setValue("agxRepresentativePrefecture", e.target.value)}
                        error={errors.agxRepresentativePrefecture?.message}
                    />

                    <FormInput
                        label="市町村"
                        name="agxRepresentativeAddress1"
                        placeholder="〇〇市〇〇区〇〇（全角）"
                        register={register}
                        errors={errors}
                        required
                        disabled={copyAddressFromCorporate}
                    />

                    <FormInput
                        label="市町村（カナ)"
                        name="agxRepresentativePhoneticAddress1"
                        placeholder="マルマルシマルマルクマルマル（全角カナ）"
                        register={register}
                        errors={errors}
                        required
                        disabled={copyAddressFromCorporate}
                    />

                    <FormInput
                        label="丁目番地建物名"
                        name="agxRepresentativeAddress2"
                        placeholder="１丁目２ー３（全角）"
                        register={register}
                        errors={errors}
                        required
                        disabled={copyAddressFromCorporate}
                    />

                    <FormInput
                        label="丁目番地建物名（カナ)"
                        name="agxRepresentativePhoneticAddress2"
                        placeholder="１チョウメ２ー３（全角カナ）"
                        register={register}
                        errors={errors}
                        required
                        disabled={copyAddressFromCorporate}
                    />

                    {/* Phone number */}
                    <ThreePartNumberInput
                        label="電話番号"
                        required
                        value1={agxRepresentativePhoneNumber1}
                        value2={agxRepresentativePhoneNumber2}
                        value3={agxRepresentativePhoneNumber3}
                        onChange1={handlePhoneNumber1Change}
                        onChange2={handlePhoneNumber2Change}
                        onChange3={handlePhoneNumber3Change}
                        onBlur={() => handleSetDataAgxRepresentativePhoneNumber(
                            setValue,
                            trigger,
                        )}
                        error={errors.agxRepresentativePhoneNumber1?.message || errors.agxRepresentativePhoneNumber2?.message || errors.agxRepresentativePhoneNumber3?.message}
                        showError={stateErrorPhoneNumber || !!(errors.agxRepresentativePhoneNumber1 || errors.agxRepresentativePhoneNumber2 || errors.agxRepresentativePhoneNumber3)}
                        disabled={copyAddressFromCorporate}
                    />

                    {/* FAX */}
                    <ThreePartNumberInput
                        label="FAX"
                        required={false}
                        value1={agxRepresentativeFaxNumber1}
                        value2={agxRepresentativeFaxNumber2}
                        value3={agxRepresentativeFaxNumber3}
                        onChange1={handleFaxNumber1Change}
                        onChange2={handleFaxNumber2Change}
                        onChange3={handleFaxNumber3Change}
                        onBlur={() => handleSetDataAgxRepresentativeFaxNumber(
                            setValue,
                            trigger,
                        )}
                        error={errors.agxRepresentativeFaxNumber1?.message || errors.agxRepresentativeFaxNumber2?.message || errors.agxRepresentativeFaxNumber3?.message}
                        showError={stateErrorFaxNumber || !!(errors.agxRepresentativeFaxNumber1 || errors.agxRepresentativeFaxNumber2 || errors.agxRepresentativeFaxNumber3)}
                        disabled={copyAddressFromCorporate}
                    />

                    {/* Submit button */}
                    <FormButtons
                        onSave={showConfirmDialog}
                        onNext={onSubmit}
                        onBack={handleBack}
                        isSubmitting={isUpdating}
                        showBackButton={agxMerchantParams?.agxBusinessForm === 283260000}
                    />
                </Box>
            </form>
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                title="入力内容を一時保存します。"
                cancelLabel="戻る"
                confirmLabel="一時保存"
                confirmVariant="danger"
                onCancel={() => setShow(false)}
                onConfirm={onSave}
            />
        </>
    )
}

export default Daiyousha;
