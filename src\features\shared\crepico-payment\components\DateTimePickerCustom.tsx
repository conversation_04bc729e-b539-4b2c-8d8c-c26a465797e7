import React from 'react';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ja } from 'date-fns/locale';
import { DateUtils } from '@/utils/dateUtils';

interface DateTimePickerCustomProps {
    value: string;
    onChange: (date: Date | null) => void;
    placeholder?: string;
    className?: string;
}

export const DateTimePickerCustom: React.FC<DateTimePickerCustomProps> = ({
    value,
    onChange,
    placeholder = "YYYY/MM/DD HH:mm",
    className = "w-[260px] px-2 rounded text-2xl h-[40px]"
}) => {

    return (
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ja}>
            <DateTimePicker
                value={DateUtils.parse(value, 'YYYY/MM/DD HH:mm')}
                onChange={onChange}
                format="yyyy/MM/dd HH:mm"
                timeSteps={{ minutes: 1 }}
                ampm={false}
                onAccept={(value) => onChange(value)}
                onClose={() => {}}
                slotProps={{
                    textField: {
                        placeholder,
                        className,
                        sx: {
                            '& .MuiOutlinedInput-root': {
                                border: 'none',
                            },
                            '& .MuiPickersInputBase-root': {
                                color: '#6F6F6E',
                                fontSize: value ? '24px' : '20px',
                                height: '40px',
                                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                            },
                            '& .MuiPickersOutlinedInput-notchedOutline': {
                                borderColor: '#6F6F6E !important',
                                borderWidth: '1px !important',
                            },
                        }
                    },
                    actionBar: {
                        actions: ['clear', 'accept']
                    }
                }}
            />
        </LocalizationProvider>
    );
};
