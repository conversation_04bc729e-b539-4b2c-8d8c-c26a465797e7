import AgreementCard from '@/components/AgreementCard';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useGetAgreement } from '@/features/agreement';
import { useAuthStore } from '@/features/auth';

const AgreementPaygate = () => {
    const { user } = useAuthStore();
    const { data, isLoading } = useGetAgreement(user?.agxMerchantNo);

    const agreementData = [
        { title: "チョキペイ加盟店規約", url: "https://choqi.co.jp/choqipay/rule.pdf", isDisplay: data?.settlementCard },
        { title: "MUFGカード加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/mufgcard/shop_01.pdf", isDisplay: data?.settlementCard },
        { title: "JCB加盟店規約", url: "https://www.jcb.co.jp/kiyaku/pdf/kameiten0705_05.pdf", isDisplay: !data?.jcbExistsMembershipFlag && data?.settlementCard },
        { title: "JCB PREMO取扱加盟店特約", url: "https://www.jcb.co.jp/kiyaku/pdf/premo_kameiten.pdf", isDisplay: !data?.jcbExistsMembershipFlag && data?.settlementCard },
        { title: "店子加盟店特約（店頭通販共通）", url: "https://www.jcb.co.jp/kiyaku/pdf/tanako_kameiten.pdf", isDisplay: !data?.jcbExistsMembershipFlag && data?.settlementCard },
        { title: "銀聯カード加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/ginrei/shop.pdf", isDisplay: data?.settlementCard },
        { title: "三菱UFJニコス－交通系電子マネー加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/index_01.pdf", isDisplay: data?.settlementTraffic },
        { title: "三菱UFJニコス－nanaco電子マネー加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/nanaco.pdf", isDisplay: data?.settlementNanaco },
        { title: "三菱UFJニコス－WAON加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/waon.pdf", isDisplay: data?.settlementWaon },
        { title: "三菱UFJニコス－Edy間接加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/edy.pdf", isDisplay: data?.settlementEdy },
        { title: "三菱UFJニコス－ｉＤ加盟店規約", url: "https://www.cr.mufg.jp/merchant/rule/e_money/id.pdf", isDisplay: data?.settlementAid },
        { title: "QRコード決済規約", url: "https://www.daiwahousefinancial.co.jp/docs/terms/omatome-terms/", isDisplay: data?.settlementQrCode },
    ];

    if (isLoading) return <LoadingSpinner />;

    return <AgreementCard data={agreementData} isDataEmpty={!data} />
}

export default AgreementPaygate;