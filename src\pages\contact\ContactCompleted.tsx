import icoPagetop01 from "/images/ico_pagetop01.png";

import { Link } from "react-router-dom";

export default function ContactCompleted() {
  const step = "completed";
  return (
    <div className="relative mb-[83px]">
      <div className="max-w-[1000px] mx-auto py-6 px-4">
        <div className="text-center">
          <h2 className="text-[37px] font-bold mb-9 text-[#727272]">
            お問い合わせフォーム
          </h2>
          <div className="flex justify-center">
            <ul className="flex justify-between gap-2 w-[370px] my-[80px]">
              <li className="flex items-center flex-col text-[15px] text-[#14AEA9] font-[700] whitespace-nowrap gap-2">
                <span
                  className={`w-[10px] h-[10px] rounded-full bg-[#a0a0a0] mt-[5px]
                  `}
                ></span>
                <span
                  className={`w-[10px] h-[10px] rounded-full text-[#a0a0a0]
                  `}
                >
                  入力
                </span>
              </li>
              <span className="w-full h-0.5 bg-[#a0a0a0] mt-2"></span>
              <li className="flex items-center flex-col text-[15px] text-[#14AEA9] font-[700] whitespace-nowrap gap-2">
                <span
                  className={`rounded-full bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px]`}
                ></span>
                <span
                  className={`w-5 h-5 rounded-full text-[#a0a0a0]
                  `}
                >
                  確認
                </span>
              </li>
              <span className="w-full h-0.5 bg-[#a0a0a0] mt-2"></span>
              <li className="flex items-center flex-col text-[15px] text-[#14AEA9]  font-[700] whitespace-nowrap gap-2 ">
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${
                    step === "completed" ? "bg-teal-600" : "bg-gray-300"
                  }`}
                ></span>
                完了
              </li>
            </ul>
          </div>
          <div className="text-sm text-center text-black mb-10 font-[500] space-y-4 leading-relaxed">
            <p>確認のため、自動返信メールをお送りさせていただきます。</p>
            <div>
              <p className="underline">お問い合わせ内容につきまして</p>
              <p>担当者が問い合わせ内容を確認後、ご連絡させていただきます。</p>
            </div>
          </div>
          <div className="mx-auto greenBtn01 btn01 disabled:cursor-not-allowed">
            <Link to="https://choqi.co.jp/choqipay/">
              <span>トップへ戻る</span>
              <span className="text-lg">&gt;</span>
            </Link>
          </div>
        </div>
      </div>
      <div>
        <div className="flex justify-end mt-[100px] mr-[20px] mb-5">
          <p
            className=" bg-white rounded-full shadow-lg cursor-pointer"
            onClick={() => {
              window.scrollTo({ top: 0 });
            }}
          >
            <img src={icoPagetop01} alt="pagetop" />
          </p>
        </div>
        <div className="w-full flex justify-center bg-[#EEEEEE] py-4 ">
          <ul className="contactContent01 flex space-x-4 list-none">
            <li className="mailBtn01 text-blue-700">
              <Link to="/contact">メールで相談</Link>
            </li>
            <li className="greenBtn01 btn01 downloadBtn01">
              <Link
                to="/document"
                className="!no-underline hover:text-blue-700 font-semibold flex items-center"
              >
                <p className="z-[999]">資料ダウンロード</p>
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
