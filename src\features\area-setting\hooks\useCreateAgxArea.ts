import { useToast } from '@/hooks/use-toast';
import { useMutation } from '@tanstack/react-query';
import { areaSettingService } from '../services/areaSettingService';
import { AgxAreaParam } from '../type';

export const useCreateAgxArea = () => {
    const { toast } = useToast();
    const createArea =  useMutation({
        mutationFn: async (agxArea: AgxAreaParam) => {
            await areaSettingService.createArea(agxArea);
        },
        onSuccess: () => {
            toast({
                variant: "default",
                title: '保存が完了しました。',
                description: "",
            });
        },
        onError: () => {
            toast({
                variant: "destructive",
                title: '保存が完了しました。',
                description: "",
            });
        }
    });
    return {
        createAgxArea: createArea.mutate,
        createAgxAreaAsync: createArea.mutateAsync,
        isLoading: createArea.isPending,
        isError: createArea.isError,
        error: createArea.error,
        reset: createArea.reset,
    };
};
