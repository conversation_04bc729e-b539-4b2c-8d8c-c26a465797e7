import React from 'react';
import { AdminDepositDetailPage } from '@/features/adminStore/adminDeposit/components/AdminDepositDetailPage';
import { useAuthStore } from '@/features/auth/slices/authStore';

const AdminDepositDetail: React.FC = () => {
  const { user } = useAuthStore();

  // Use actual merchant number from auth store
  const agxMerchantNo = user?.agxMerchantNo;

  if (!agxMerchantNo) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">認証情報が見つかりません。</h2>
      </div>
    );
  }

  return <AdminDepositDetailPage agxMerchantNo={agxMerchantNo} />;
};

export default AdminDepositDetail;
