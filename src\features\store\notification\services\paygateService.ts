import apiService from '@/services/api';
import { PaygateResponse } from '@/features/store/notification/types/paygateType';
import { API_ENDPOINTS } from '@/config/api-endpoints';

class PaygateService {
  /**
   * Get paygate data for a merchant
   * @param merchantNo - Merchant number
   * @returns Paygate data
   */
  async getData(merchantNo: string): Promise<PaygateResponse> {
    const response = await apiService.get<PaygateResponse>(API_ENDPOINTS.PAYGATE.GET_DATA(merchantNo));
    return response;
  }
}

export const paygateService = new PaygateService();
export default paygateService;
