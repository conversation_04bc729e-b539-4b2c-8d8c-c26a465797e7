
import React, { useState } from 'react';
import { IBusinessType, IMerchantCoreType } from '../../types';
import { BUSINESS_TYPES } from '../../constant';
import { Button } from '@/components/ui/button';
import { <PERSON>alog, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { DialogTitle } from '@radix-ui/react-dialog';

interface Props {
    data: IMerchantCoreType;
    onNext: () => void;
    onUpdate: (data: Partial<IMerchantCoreType>) => void;
}

const BusinessType = ({ data, onNext, onUpdate }: Props) => {
    const [selectedType, setSelectedType] = useState<number | null>(data.agxBusinessType ? Number(data.agxBusinessType) : null);
    const [showModal, setShowModal] = useState(false);


    const handleSelection = (type: number) => {
        setSelectedType(type);
        onUpdate({ agxBusinessType: type });

        // Show modal for specific business type
        if (type === 283260008) {
            setShowModal(true);
        }
    };

    const handleNext = () => {
        if (selectedType) {
            onNext();
        }
    };

    return (
        <>
            <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-full md:w-[90%] lg:w-[90%] xl:w-[30%] max-w-full flex-col items-center text-[rgba(112,112,112,1)] mt-[33px] py-10 px-8 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
                <h2 className="text-[28px] text-[rgba(112,112,112,1)] font-normal mb-10">業種を教えてください</h2>

                <div className="flex flex-wrap justify-center gap-4 w-full mb-10">
                    {BUSINESS_TYPES.map((type) => (
                        <Button
                            key={type.id}
                            onClick={() => handleSelection(type.id)}
                            variant={selectedType === type.id ? "default" : "outline"}
                            size="lg"
                            className={`h-[50px] rounded-[8px] text-[16px] font-normal mb-2 transition-colors
                                ${selectedType === type.id
                                    ? "bg-[#19A492] text-white border-[#19A492] hover:bg-[#15b19d] hover:border-[#15b19d]"
                                    : "bg-white text-[rgba(112,112,112,1)] border-[rgba(112,112,112,0.3)] hover:bg-[#89a9a7] hover:text-white hover:border-[#89a9a7]"
                                }`}
                        >
                            {type.label}
                        </Button>
                    ))}
                </div>

                <Button
                    onClick={handleNext}
                    disabled={!selectedType}
                    className="text-[20px] w-[20%] max-md:w-full h-[45px] bg-[#19A492] text-white rounded-[8px] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#15b19d] transition-colors"
                >
                    次へ
                </Button>
            </section>

            <Dialog open={showModal} onOpenChange={setShowModal}>
                <DialogTitle></DialogTitle>
                <DialogContent className="max-w-2xl px-0">
                    <div className="px-8 py-4 mt-6 border-y border-gray-300 text-[#6F6F6E] overflow-auto max-h-[500px]">
                        <p>以下の項目に当てはまらないものがあれば中小企業料率での提供はできかねますので、ご注意ください。</p>
                        <br />
                        <p>・従業員数が300人以下、資本金が3億円以下であること</p>
                        <p>・上場企業に属していないこと</p>
                        <p>・現在に至るまで弊社及び他のカード会社含めカード決済のご契約歴がないこと</p>
                        <p>・本プログラムへの申し込み完了後に、会社業況が以下へ該当した際には</p>
                        <p>事前合意の加盟店手数料率へ変更となること</p>
                        <p>①本プログラムへ申し込みを行った加盟店のクレジットカード年間取扱高が、</p>
                        <p>{' '.repeat(2)}国際ブランドの取り決める以下金額のいずれかを超過した場合</p>
                        <p>{' '.repeat(2)}(集計のタイミングは国際ブランドの判断によるものとする※)</p>
                        <p>{' '.repeat(6)}Visa：2千万円</p>
                        <p>{' '.repeat(6)}MasterCard：1千万円</p>
                        <p>{' '.repeat(6)}※店舗単位ではなく、事業者単位（法人単位）での取扱高の集計となります。</p>
                        <p>②国際ブランドまたは、弊社が本プログラムを終了した場合</p>
                        <p>  ※なお、本プログラムは事前の告知なく終了する場合がございます。</p>
                        <br />
                        <p>また、申請時の「表明・確約事項」「同意事項」を同意いただいた中小企業の薬局様のみ適用となりますので、ご了承ください。</p>
                    </div>
                    <DialogFooter>
                        <Button
                            onClick={() => setShowModal(false)}
                            className="bg-[#c94e4e] hover:bg-[#c93838] text-white sm:mr-4"
                        >
                            閉じる
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default BusinessType;
