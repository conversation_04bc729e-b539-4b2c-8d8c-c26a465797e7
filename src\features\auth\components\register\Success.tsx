import React from 'react';
import logo from '@/assets/images/グループ 3.svg';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { AccountTypes } from '@/types/globalType';
import { useAuthStore } from '@/store';

const Success = () => {
    const navigate = useNavigate();
    const { setAccountType } = useAuthStore();

    const handleClick = async () => {
        setAccountType(AccountTypes.APPLICATION_STEPS);
        navigate('/application-steps');
    }

    return (
        <div className="flex flex-col items-center justify-center text-center space-y-8 w-full max-w-[1080px] mx-auto h-[60vh]">
            <div className="flex items-center justify-center space-x-2 mb-1">
                <img src={logo} alt="ChoQi Logo" className="h-[85px] object-contain" />
            </div>

            <p className="text-[20px] text-[rgba(112,112,112,1)] font-normal mb-6">
                続けて、加盟店申し込みをお願いいたします。
            </p>

            <Link to="/application-steps" className="w-full max-w-[323px]">
                <Button
                    className="w-full h-[50px] bg-[#19A492] text-white rounded-md text-[20px] font-medium shadow-md hover:bg-[#15b19d] transition-colors flex items-center justify-center"
                    onClick={handleClick}
                >
                    加盟店申し込みへ
                </Button>
            </Link>
        </div>
    );
};

export default Success;
