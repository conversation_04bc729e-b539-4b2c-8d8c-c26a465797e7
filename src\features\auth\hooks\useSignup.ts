import { useMutation } from "@tanstack/react-query";
import { authService } from "../services/authService";
import { AuthResponse, SignupParams } from "../types";
import { useToast } from "@/hooks/use-toast";
import { contactStore } from "../slices/contactStore";

export const useSignup = () => {
    const { toast } = useToast();
    const { setId } = contactStore();
    const signupMutation = useMutation({
        mutationFn: async (data: SignupParams) => {
            const response = await authService.signup(data);
            setId(response?.data?.id);
            return response.data;
        },
        onSuccess: (response: AuthResponse) => {
            toast({
                title: "ログイン成功",
                description: "ダッシュボードにリダイレクトしています...",
                duration: 2000,
            });
        },
        onError: (error: any) => {
            toast({
                title: "ログイン失敗",
                description: error.message,
                duration: 2000,
            });
        }
    })
    return {
        signup: signupMutation.mutate,
        signupAsync: signupMutation.mutateAsync,
        isLoading: signupMutation.isPending,
        isError: signupMutation.isError,
        error: signupMutation.error,
        reset: signupMutation.reset,
    };
}