export interface PaygateData {
  id: string;
  password: string;
  appSerialNumber: string;
}

export interface PaygateResponse {
  data: PaygateData;
  statusCode: number;
  message: string;
}

export interface PaygateState {
  id: string;
  password: string;
  appSerialNumber: string;
  loading: boolean;
  error: string | null;
}

// Deposit Paygate types
export interface PaymentBreakdown {
  agxMerchantPaymentId: string;
  agxTransactionType: number;
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFeeRate: number;
  agxTotalFee: number;
  tax: number;
  agxPaymentAmount: number;
  agxPaymentBreakdownId: string;
  groupCodeName: string;
}

export interface MerchantPayment {
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFee: number;
  agxSettlementCompanyTax: number;
  agxInHouseTax: number;
  agxPaymentAmount: number;
  agxInvoiceFlg: number;
  agxBankName: string;
  agxBranchName: string;
  agxAcccountType: string;
  agxAccountNo: string;
  agxAccountHolder: string;
}
