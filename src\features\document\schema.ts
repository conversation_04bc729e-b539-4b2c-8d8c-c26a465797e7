export const validateWideString = (value: string, allowSpaces = true): boolean => {
  // Kiểm tra toàn bộ ký tự là full-width (zenkaku)
  const pattern = allowSpaces ? /^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\u3400-\u4DBF\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\u3000\s]*$/ : /^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\u3400-\u4DBF\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A]*$/;
  return pattern.test(value);
};