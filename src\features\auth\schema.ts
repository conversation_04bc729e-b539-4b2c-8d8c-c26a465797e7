import { z } from 'zod';

export const medicalCodeSchema = z.object({
  agxMedicalInstitutionCode: z
    .string()
    .nonempty('※必須項目です、値を入力してください。')
    .length(10, '※10文字で入力してください。')
    .regex(/^\d+$/, '※半角数字で入力してください。'),
});

export type MedicalCodeSchema = z.infer<typeof medicalCodeSchema>;

export const referralCodeSchema = z.object({
  agxEmsEmployeeNo: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[a-zA-Z0-9]*$/.test(val),
      { message: '※半角英数字で入力してください。' }
    ),
});

export type ReferralCodeSchema = z.infer<typeof referralCodeSchema>;

export const registrationSchema = z.object({
  password: z
    .string()
    .min(8, '※パスワードは 8 文字以上で指定する必要があります。')
    .regex(
      /^(((?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[A-Z])(?=.*[$*!@#&]))|((?=.*[a-z])(?=.*[0-9])(?=.*[$*!@#&]))|((?=.*[A-Z])(?=.*[0-9])(?=.*[$*!@#&])))[a-zA-Z0-9$*!@#&]{8,32}$/,
      '※パスワードには、次の 4 種類のうち少なくとも 3 種類の文字が含まれている必要があります: アルファベットの大文字、小文字、数字、および記号（$*!@#&）'
    ),
  confirmPassword: z
    .string()
    .min(1, '※確認パスワードは必須です')
}).superRefine((values, ctx) => {
  if (values.password !== values.confirmPassword) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['confirmPassword'],
      message: '※確認パスワードが正しくないので、再入力してください。',
    });
  }
});

export type RegistrationSchema = z.infer<typeof registrationSchema>;

export const loginSchema = z.object({
  email: z
    .string()
    .trim()
    .min(1, 'メールアドレスを入力してください')
    .max(254, 'メールアドレスが長すぎます（254文字以内）')
    .email('有効なメールアドレスの形式で入力してください')
    .regex(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      '正しいメールアドレスの形式で入力してください'
    )
    .refine(
      (email: string) => !email.startsWith('.') && !email.endsWith('.'),
      'メールアドレスは「.」で始まったり終わったりできません'
    )
    .refine(
      (email: string) => !email.includes('..'),
      'メールアドレスに連続する「.」は使用できません'
    ),
  password: z.string().trim().min(8, 'パスワードは8文字以上で入力してください'),
});

export type LoginFormData = z.infer<typeof loginSchema>;
