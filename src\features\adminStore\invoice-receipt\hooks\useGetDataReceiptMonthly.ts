import { useQuery } from '@tanstack/react-query';
import { invoiceService } from '../services/invoiceReceiptService';
import { ReceiptMonthlyType } from '../types';

export const useGetDataReceiptMonthly = (merchantNo: string, yearMonth: string) => {
  return useQuery({
    queryKey: ['admin-receipt-monthly', merchantNo, yearMonth],
    queryFn: async (): Promise<ReceiptMonthlyType> => {
      if (!merchantNo || !yearMonth) {
        throw new Error('Merchant number and year month are required');
      }
      const response = await invoiceService.getDataReceiptMonthly(merchantNo, yearMonth);
      return response.data;
    },
    enabled: !!merchantNo && !!yearMonth,
  });
};
