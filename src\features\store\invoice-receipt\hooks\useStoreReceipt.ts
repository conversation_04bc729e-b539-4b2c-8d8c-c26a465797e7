import { useQuery } from '@tanstack/react-query';
import { receiptService } from '@/features/store/invoice-receipt/services/receiptService';
import { ReceiptData } from '@/features/store/invoice-receipt/types';

interface UseStoreReceiptReturn {
    data: ReceiptData | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
}

export const useStoreReceipt = (invoiceNo: string | undefined): UseStoreReceiptReturn => {
    const {
        data = null,
        isLoading: loading,
        error,
        refetch
    } = useQuery({
        queryKey: ['store-receipt', invoiceNo],
        queryFn: async () => {
            if (!invoiceNo) {
                throw new Error('請求書番号が見つかりません。');
            }
            
            const result = await receiptService.getData(invoiceNo);
            return result;
        },
        enabled: !!invoiceNo,
        staleTime: 0,
        gcTime: 0,
    });

    return { 
        data, 
        loading, 
        error: error?.message || null, 
        refetch 
    };
}; 