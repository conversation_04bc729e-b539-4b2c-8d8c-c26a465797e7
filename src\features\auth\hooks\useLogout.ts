import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { contactStore } from '@/features/auth/slices/contactStore';
import authService from '@/features/auth/services/authService';

export const useLogout = () => {
  const navigate = useNavigate();
  const { clearUser, clearTypeStore } = useAuthStore();
  const { clearId } = contactStore();

  const handleLogout = async () => {
    try {
      // Call logout API (this will clear tokens and user store)
      await authService.logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Always clear all remaining stores and persisted data
      clearUser();
      clearTypeStore();
      clearId();
      // Navigate to login page
      navigate('/login');
    }
  };

  return {
    handleLogout
  };
};
