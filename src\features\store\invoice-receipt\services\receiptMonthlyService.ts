import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { ReceiptData, ReceiptResponse } from "@/features/store/invoice-receipt/types";



class ReceiptMonthlyService {
    async getData(merchantNo: string, invoiceNo: string): Promise<ReceiptData> {
        const response =  await apiService.get<ReceiptResponse>(
            API_ENDPOINTS.INVOICE_RECEIPT.GET_DATA_RECEIPT_MONTHLY(merchantNo, invoiceNo)
        );

        return response.data;
    }
}

export const receiptMonthlyService = new ReceiptMonthlyService();