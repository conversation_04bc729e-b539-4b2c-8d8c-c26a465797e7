import { useQuery } from '@tanstack/react-query';
import { summaryService } from '@/features/store/summary/services/summaryService';
import { MonthlySummaryState } from '@/features/store/summary/types/summaryType';

export const useMonthlySummary = (merchantNo: string) => {
  const {
    data: response,
    error,
    refetch
  } = useQuery({
    queryKey: ['monthly-summary', merchantNo],
    queryFn: () => summaryService.getMonthlySummaryData(merchantNo),
    enabled: !!merchantNo,
  });
  
  // Transform the response data to match the expected format
  const summaryMonthlyData: MonthlySummaryState = {
    chartLabel: response?.data?.chartLabel || [],
    creditData: response?.data?.creditData || [],
    electronicData: response?.data?.electronicData || [],
    qrData: response?.data?.qrData || [],
    lineData: response?.data?.lineData || [],
    barData: response?.data?.barData || [],
    barLastYearData: response?.data?.barLastYearData || [],
    error: !!error
  };

  return {
    summaryMonthlyData,
    refetch
  };
};
