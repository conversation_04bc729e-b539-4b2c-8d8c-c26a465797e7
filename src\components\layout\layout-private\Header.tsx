import { useLocation } from 'react-router-dom';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { getMenuNameFromPath } from '@/features/header/utils';

export const Header = () => {
  const location = useLocation();
  const currentMenuName = getMenuNameFromPath(location.pathname);

  return (
    // <header className="fixed top-0 left-0 lg:left-[14%] right-0 bg-white px-3 lg:px-10 pt-8 pb-2 z-50">
    <header className="bg-white lg:px-5 xl:px-10 px-4 pt-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="xl:hidden" />
          <nav className="flex items-center space-x-2 text-[24px] text-gray-500">
            <span>チョキペイ</span>
            <span className="text-gray-400 h-10">|</span>
            <span className="text-gray-500">{currentMenuName}</span>
          </nav>
        </div>
      </div>
    </header>
  );
};