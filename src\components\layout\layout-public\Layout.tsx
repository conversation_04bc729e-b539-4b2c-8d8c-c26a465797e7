import React from 'react';
import { Header } from '@/components/layout/layout-public/Header';
import { Footer } from '@/components/layout/Footer';

interface LayoutProps {
  children: React.ReactNode;
  showManualLink?: boolean;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="bg-white min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 pt-[65px] md:pt-[100px] lg:pt-[130px]">
        {children}
      </main>
      
      <Footer />
    </div>
  );
}; 