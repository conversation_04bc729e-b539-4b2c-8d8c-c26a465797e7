import { useState, useEffect } from 'react';
import { BUSINESS_TYPE_LABELS, ENTITY_TYPE_LABELS, ERROR_MESSAGES } from '@/features/auth/constant';
import { Button } from '@/components/ui/button';
import { IMerchantCoreType } from '@/features/auth/types';
import { contactStore } from '@/features/auth/slices/contactStore';
import { useCreateMerchant } from '@/features/auth/hooks/useCreateMerchant';

interface Props {
    data: IMerchantCoreType;
    onNext: () => void;
    onPrev: () => void;
    isSubmitting?: boolean;
    onUpdate: (data: Partial<IMerchantCoreType>) => void;
}

const Confirmation = ({ data, onNext, onPrev, isSubmitting = false, onUpdate }: Props) => {
    const [submitting, setSubmitting] = useState(isSubmitting);
    const { id } = contactStore();
    const [error, setError] = useState<number | null>(null);
    const { createMerchantAsync } = useCreateMerchant();

    useEffect(() => {
        onUpdate({ agxContactId: id });
    }, []);

    const handleSubmit = async () => {
        setSubmitting(true);
        setError(null);
        try {
            const response = await createMerchantAsync(data);
            onNext();
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-full md:w-[90%] lg:w-[90%] xl:w-[30%] max-w-full flex-col items-center text-[rgba(112,112,112,1)] mt-[33px] py-10 px-8 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
            <h2 className="text-[28px] text-gray-600 font-normal mb-8">よろしいですか？</h2>

            <div className="w-full max-w-lg mb-8">
                <div className="space-y-4">
                    <div className="flex items-center">
                        <div className="w-1/3 text-right text-gray-600 pr-4 text-[18px]">業種</div>
                        <div className="w-2/3 h-14 bg-white rounded-md py-3 px-4 text-[#19A492] border border-gray-200 shadow-sm">
                            {BUSINESS_TYPE_LABELS[Number(data.agxBusinessType)] ?? String(data.agxBusinessType)}
                        </div>
                    </div>

                    <div className="flex items-center">
                        <div className="w-1/3 text-right text-gray-600 pr-4 text-[18px]">法人/個人</div>
                        <div className="w-2/3 h-14 bg-white rounded-md py-3 px-4 text-[#19A492] border border-gray-200 shadow-sm">
                            {ENTITY_TYPE_LABELS[Number(data.agxBusinessForm)] ?? String(data.agxBusinessForm)}
                        </div>
                    </div>

                    <div className="flex items-center">
                        <div className="w-1/3 text-right text-gray-600 pr-4 text-[18px]">医療機関コード</div>
                        <div className="w-2/3 h-14 bg-white rounded-md py-3 px-4 text-[#19A492] border border-gray-200 shadow-sm">
                            {data.agxMedicalInstitutionCode}
                        </div>
                    </div>

                    {data.agxEmsEmployeeNo && (
                        <div className="flex items-center">
                            <div className="w-1/3 text-right text-gray-600 pr-4 text-[18px]">紹介コード</div>
                            <div className="w-2/3 h-14 bg-white rounded-md py-3 px-4 text-[#19A492] border border-gray-200 shadow-sm">
                                {data.agxEmsEmployeeNo}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {error && (
                <div className="w-full max-w-lg mb-4">
                    <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                        {ERROR_MESSAGES[error] ?? ERROR_MESSAGES[500]}
                    </div>
                </div>
            )}

            <div className="flex justify-center gap-4 w-full max-w-xs">
                <Button
                    onClick={onPrev}
                    disabled={submitting}
                    className="text-[18px] w-full h-[45px] bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    戻る
                </Button>

                <Button
                    onClick={handleSubmit}
                    disabled={submitting}
                    className="text-[18px] w-full h-[45px] bg-red-600 text-white rounded-md hover:bg-[#ec2020] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {submitting ? '送信中...' : '完了'}
                </Button>
            </div>
        </section>
    );
};

export default Confirmation;
