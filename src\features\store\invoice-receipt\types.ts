export interface Invoice {
    deadlineDate?: string | null;
    invoiceStatus?: number | null;
    invoiceUrl?: string | null;
    receiptUrl?: string | null;
    total?: number | null;
}

export interface InvoiceApiResponse {
    data: Invoice;
    totalPage: number;
    totalElement: number;
    hasNext: boolean;
  }

// Interface cho hóa đơn hàng tháng
export interface AgxInvoice {
  year?: number;
  month?: number;
  total?: number;
  invoiceUrl?: string;
  receiptUrl?: string;
  deadlineDate?: string;
}

// Interface cho chi tiết hóa đơn thiết bị terminal
export interface AgxInvoiceDetail {
  deadlineDate: string;
  invoiceStatus: number;
  invoiceUrl: string;
  receiptUrl: string;
  total: number;
  agxStatus?: number;
}

// Interface cho thông tin GMO
export interface GmoData {
  memberId: string | null;
  cardNo: string | null;
}

// Response types
export interface AgxInvoiceResponse {
  data: AgxInvoice[];
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

export interface AgxInvoiceDetailResponse {
  data: AgxInvoiceDetail;
}

export interface GmoInfoResponse {
  data: GmoData;
}

export interface GmoPaymentResponse {
  data: {
    LinkUrl: string;
  };
}

// Enum cho invoice status (dựa trên logic cũ)
export enum InvoiceStatus {
  PENDING = *********,  // Chờ thanh toán
  PAID = *********,     // Đã thanh toán
}

// Constants cho UI display
export const INVOICE_STATUS_LABELS = {
  [InvoiceStatus.PENDING]: '未払い',
  [InvoiceStatus.PAID]: '支払済み',
} as const;

export interface ReceiptData {
  paymentDate: string;
  storeName: string;
  subTotal: number;
  tax: number;
  total: number;
  invoiceCreatedDate: string;
}

export interface ReceiptResponse {
  data: ReceiptData;
}