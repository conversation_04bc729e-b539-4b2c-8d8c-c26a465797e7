import React from 'react';
import { Download } from "lucide-react";
import { useParams } from 'react-router-dom';
import ChoqiHealthHacking from '@/assets/images/choqi-health-hacking.png';
import { formatDateJapan, formatMoney, formatNumber } from '@/utils/dateUtils';
import Secretkey from '@/assets/images/secretkey.png';
import { InvoiceDetailResponse } from '@/features/store/invoice-receipt/services/invoiceService';
import { axiosPDF } from '@/features/store/invoice-receipt/utils';
import PDFService, { blobToBase64, PDFConfig } from '@/services/pdfService';
import { PDF_IMG_LOGO, PDF_IMG_STAMP } from '@/features/store/invoice-receipt/utils/image-base64';

interface InvoiceMonthlyLayoutProps {
  data: InvoiceDetailResponse['data'];
  isMonthly?: boolean;
  children: React.ReactNode;
}

export const InvoiceMonthlyLayout = ({
  data,
  isMonthly = true,
  children,
}: InvoiceMonthlyLayoutProps) => {
  const { invoiceNo } = useParams<{ invoiceNo: string }>();
  const switchLayoutDate = "2023-11-05";

  // Get PDF table data
  const getInvoiceTableData = () => {
    const tableData: any[][] = [];

    // Add item rows if data exists
    if (data?.invoiceDetail) {
        data.invoiceDetail.forEach(item => {
            tableData.push([
                { text: item.name, alignment: 'left' },
                { text: formatNumber(item.quantity), alignment: 'right' },
                { text: formatMoney(item.unitPrice), alignment: 'right' },
                { text: formatNumber(item.amount), alignment: 'right' },
            ]);
        });
    }

    return tableData;
  };

  const getRecordDetailPDF = () => {
      const result = [[{
          text: '品目',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '数量',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '単価',
          style: 'tableHeader',
          alignment: 'center'
      },
      {
          text: '金額',
          style: 'tableHeader',
          alignment: 'center'
      }]];
      for (const index in data?.invoiceDetail) {
          const cols = [];
          cols.push({ text: data?.invoiceDetail[index].name, alignment: 'left' },
              { text: formatNumber(data?.invoiceDetail[index].quantity), alignment: 'right' },
              { text: formatNumber(data?.invoiceDetail[index].unitPrice), alignment: 'right' },
              { text: formatNumber(data?.invoiceDetail[index].amount), alignment: 'right' },
          );
          result.push(cols);
      }
      //Table detail footer
      result.push([
          {
              text: '\n',
              border: [false, false, false, false],
          } as any,
          {
              text: '小計',
              border: [true, true, false, false],
          } as any,
          {
              text: '\n',
              border: [false, true, false, false],
          } as any,
          {
              text: formatNumber(data?.subTotal),
              alignment: 'right'
          } as any,
      ]);
      result.push([
          {
              text: '\n',
              border: [false, false, false, false],
          } as any,
          {
              text: '消費税(10%) ',
              border: [true, true, false, false],
          } as any,
          {
              text: '\n',
              border: [false, true, false, false],
          } as any,
          {
              text: formatNumber(data?.tax),
              alignment: 'right'
          } as any,
      ]);
      result.push([
          {
              text: '\n',
              border: [false, false, false, false],
          } as any,
          {
              text: '合計',
              bold: true,
              border: [true, true, false, true],
          } as any,
          {
              text: '\n',
              border: [false, true, false, true],
          } as any,
          {
              text: formatNumber(data?.total),
              bold: true,
              alignment: 'right'
          } as any,
      ]);
      return result;
  }

  // Handle PDF export
  const handleExportPDF = async () => {
  try {
      // Setup fonts first and wait for them to be ready
      const responseYugothib = await axiosPDF.get('yugothib.ttf');
      if (responseYugothib.status !== 200) {
          alert('フォントの読み込みに失敗しました。');
          return;
      }
      const fontYugothib = await blobToBase64(responseYugothib.data);

      const responseArial = await axiosPDF.get('arial.ttf');
      if (responseArial.status !== 200) {
          alert('フォントの読み込みに失敗しました。');
          return;
      }
      const fontArial = await blobToBase64(responseArial.data);

      // Setup fonts and wait for completion
      await PDFService.setupMultipleFonts(fontYugothib, fontArial);
      
      const currentDate = data?.invoiceCreatedDate ? formatDateJapan(data?.invoiceCreatedDate) : '';
      const invoiceNumber = invoiceNo ? atob(invoiceNo).split("-")[0] : '';

      // Create main table with items
      const tableData = getInvoiceTableData();

      // Add company info to the right column
      const companyInfo = [
          { text: '〒532-0003', margin: [70, 0, 0, 5], fontSize: 12 },
          { text: '大阪府大阪市淀川区宮原1-6-1', margin: [70, 0, 0, 5], fontSize: 12 },
          { text: '新大阪ブリックビル', margin: [70, 0, 0, 10], fontSize: 12 },
          { text: 'TEL:06-6397-5210', margin: [70, 0, 0, 5], fontSize: 12, font: 'arial' },
          { text: 'FAX:06-6397-5211', margin: [70, 0, 0, 5], fontSize: 12, font: 'arial' },
          { text: '<EMAIL>', margin: [70, 0, 0, 5], fontSize: 12, font: 'arial' }
      ];

      // Add qualified business registration number for newer invoices
      if (data?.invoiceCreatedDate && data?.invoiceCreatedDate > switchLayoutDate) {
          companyInfo.unshift(
              { text: '適格事業者登録番号', margin: [70, 0, 0, 5], fontSize: 12 },
              { text: '(T6120001228218)', margin: [70, 0, 0, 5], fontSize: 12 }
          );
      }

      const details = getRecordDetailPDF();

      const config: PDFConfig = {
          pageSize: { width: 640, height: 900 } as any,
          pageOrientation: 'portrait',
          defaultStyle: {
              font: 'yugothib'
          },
          background: function (page) {
            if (page === 1) {
                return [
                    {
                        image: PDF_IMG_STAMP,
                        width: 86,
                        margin: [480, 245, 0, 0],
                    }
                ];
            }
          },
          content: [
              { text: data?.invoiceCreatedDate ? formatDateJapan(data?.invoiceCreatedDate) : '', alignment: 'right', margin: [0, 20, 20, 5] },
              { text: `請求書番号: ${atob(invoiceNo) || ''}`, alignment: 'right', margin: [0, 0, 20, 5] },
              { text: '請求書', fontSize: 24, alignment: 'center', style: 'header', margin: [0, 0, 0, 20] },
              { text: `${data?.storeName || ''} 御中`, alignment: 'left', margin: [20, 0, 0, 5] },
              {
                  margin: [20, 20, 0, 0],
                  columns: [
                      {
                          width: '*',
                          stack: [
                              { text: '下記のとおりご請求申し上げます。', margin: [0, 0, 0, 20], fontSize: 10 },
                              {
                                  table: {
                                      widths: [100, 100],
                                      heights: [15],
                                      body: [
                                          [
                                              {
                                                  border: [false, false, false, false],
                                                  fillColor: '#f3f3f3',
                                                  text: 'ご請求金額',
                                                  alignment: 'left',
                                                  margin: [0, 2, 0, 0]
                                              },
                                              {
                                                  border: [false, false, false, false],
                                                  fillColor: '#f3f3f3',
                                                  text: `${formatMoney(data?.total || 0)}`,
                                                  margin: [40, 2, 0, 0]
                                              }
                                          ],
                                      ]
                                  },
                                  layout: {
                                      fillColor: function (rowIndex, node, columnIndex) {
                                          return (rowIndex % 2 === 1) ? '#f3f3f3' : null;
                                      }
                                  }
                              },
                              { text: `お支払期限：${data?.invoiceDeadlineDate ? formatDateJapan(data?.invoiceDeadlineDate) : ''}`, margin: [0, 10, 0, 5], fontSize: 10 },
                          ]
                      },
                      {
                          width: '*',
                          stack: [
                              {
                                  image: PDF_IMG_LOGO,
                                  width: 160,
                                  margin: [70, 0, 0, 30],
                              },
                              { text: 'チョキ株式会社', margin: [70, 10, 0, 5], fontSize: 12 },
                              ...companyInfo
                          ]
                      }
                  ]
              },
              {
                  style: 'tableMargin',
                  table: {
                      headerRows: 1,
                      widths: [220, 70, 95, '*'],
                      body: details
                  },
                  layout: {
                      fillColor: function (rowIndex, node, columnIndex) {
                          return (rowIndex === 0) ? '#757575' : null;
                      }
                  }
              },
              {
                  text: '振込先',
                  margin: [20, 0, 0, 5]
              },
              {
                  margin: [20, 0, 20, 20],
                  table: {
                      widths: ['*'],
                      heights: [60],
                      body: [
                          [
                              {
                                  border: [false, false, false, false],
                                  text: '\n三菱UFJ銀行 新大阪支店 普通預金 0317169\n口座名義：チヨキ（カ',
                                  fillColor: '#eeeeee',
                              },
                          ],
                      ]
                  }
              },
              {
                  text: '備考',
                  margin: [20, 0, 0, 5]
              },
              {
                  margin: [20, 0, 20, 0],
                  table: {
                      widths: ['*'],
                      heights: [60],
                      body: [
                          [
                              {
                                  border: [false, false, false, false],
                                  text: isMonthly ? 
                                  `\n※恐れ入りますが、振込手数料はご負担ください。\n※チョキペイにご登録いただいた口座と異なる名義の口座からご入金手続きをされる場合は、備考欄に、本請求書右上に記載の「請求書番号」をご入力のうえ、お振り込みください。\n※既にクレジットカードを登録されている場合、事務手数料はかかりません。\n※クレジットカード未登録の場合は、当月15日までに登録いただきますと、事務手数料はかかりません。\n※同一法人で複数店舗でチョキペイをご利用の場合は、まとめてお振込いただくことが可能です。まとめてお振込いただく場合、店舗数に関わらず事務手数料は550円（税込）のみとなります\n`
                                  : `\n※恐れ入りますが、振込手数料はご負担ください。\n※チョキペイにご登録いただいた口座と異なる名義の口座からご入金手続きをされる場合は、備考欄に、本請求書右上に記載の「請求書番号」をご入力のうえ、お振り込みください。\n`,
                                  fillColor: '#eeeeee',
                              },
                          ],
                      ]
                  }
              }
          ],
          styles: {
              header: {
                  fontSize: 18,
                  bold: true,
                  margin: [0, 0, 0, 10]
              },
              tableMargin: {
                  margin: [20, 10, 20, 15]
              },
              tableHeader: {
                  bold: true,
                  fontSize: 13,
                  color: 'white'
              }
          },
          filename: `invoice-monthly.pdf`
      };

      // Create PDF after fonts are properly loaded
      await PDFService.createPDFWithSetupFonts(config);
      
  } catch (error) {
      console.error('PDF作成エラー:', error);
      alert('PDF作成に失敗しました。');
  }
  };

  return (
    <div className="p-4 lg:py-6 lg:px-1 xl:p-6">
      <div className="page-heading">
        <div className="flex">
          <h2 className="text-[20px] lg:text-[22px] xl:text-[24px] text-[#6F6F6E]">請求書</h2>
          <button id="download-pdf" className="p-2 pl-4 text-[#1D9987] hover:text-[#1D9987]/80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" onClick={handleExportPDF}>
            <Download className="h-6 w-6" />
          </button>
        </div>
        <hr className='mt-3 border-1 border-gray-500' />
        <div className="py-4">
            <div className="space-y-2">
                <p id="datetime" className="text-[18px] font-medium text-[#6F6F6E]">{data?.invoiceCreatedDate ? formatDateJapan(data.invoiceCreatedDate) : ''}</p>
                <p id="merchant-no" className="text-[18px] font-medium text-[#6F6F6E]">請求書番号: {atob(invoiceNo)}</p>
            </div>
        </div>
      </div>
      <div className="pt-2"><p className="text-[24px] text-[#6F6F6E] font-bold">{data?.storeName} 御中</p></div>

      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div className="w-full"><p className="text-[18px] font-medium text-[#6F6F6E]">下記のとおりご請求申し上げます。</p></div>
          <div className="w-full lg:w-1/4 flex justify-center lg:justify-start">
            <img id="image-health" src={ChoqiHealthHacking} className="w-full max-w-[250px] md:max-w-[300px] lg:w-[200px] h-auto" />
          </div>
      </div>
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start py-4 gap-4">
        <div className="w-full lg:flex-1 flex justify-center lg:justify-center order-1 lg:order-2">
          <div className="pt-6 mb-4 font-medium text-[#6F6F6E] text-left bg-contain bg-no-repeat bg-right-top lg:pl-96" style={{ backgroundImage: `url(${Secretkey})`, backgroundSize: '100px 100px' }}>
            <div id="company-name" className="text-[22px] font-bold mb-2">チョキ株式会社</div>
            {data?.invoiceCreatedDate > switchLayoutDate && (
            <div className="mb-2">
              <div className="text-[16px]">適格事業者登録番号</div>
              <div className="text-[16px]">(T6120001228218)</div>
            </div>
            )}
            <div id="postcode" className="text-[16px]">〒532-0003</div>
            <div id="addr1" className="text-[16px]">大阪府大阪市淀川区宮原1-6-1</div>
            <div id="addr2" className="text-[16px] mb-2">新大阪ブリックビル</div>
            <div id="tell" className="text-[16px]">TEL:06-6397-5210</div>
            <div id="fax" className="text-[16px]">FAX:06-6397-5211</div>
            <div id="mail" className="text-[16px]"><EMAIL></div>
          </div>
        </div>
        <div className="w-full lg:flex-1 order-2 lg:order-1">
            <div className="flex w-full lg:w-2/3 justify-between items-center bg-gray-200 p-4 border border-gray-200">
                <p className="text-[26px] font-bold text-[#6F6F6E]">ご請求金額</p>
                <div id="total-fee"><p id="invoice-total" className="text-[26px] font-bold text-[#6F6F6E]">¥ {formatNumber(data?.total || 0)}</p></div>
            </div>
            <div className="pt-4">
                <div id="deadline-date" className="text-[18px] font-medium text-[#6F6F6E]">お支払期限：{formatDateJapan(data?.invoiceDeadlineDate || '')}</div>
            </div>
        </div>
      </div>

      {/* Main Content */}
      {children}

      <div className='py-4'>
        <div className="pt-4">
          <p className="text-[18px] font-medium text-[#6F6F6E]">振込先</p>
        </div>
        <div className="pt-4 bg-gray-200 p-4 border border-gray-200 w-full lg:w-1/2">
            <div className="w-full"><p className="text-[18px] font-medium text-[#6F6F6E]">三菱UFJ銀行 新大阪支店 普通預金 0317169</p></div>
            <div className="w-full"><p className="text-[18px] font-medium text-[#6F6F6E]">口座名義：チヨキ（カ）</p></div>
        </div>
        <div className="pt-4">
          <p className="text-[18px] font-medium text-[#6F6F6E]">備考</p>
        </div>
        <div className="pt-4 bg-gray-200 p-4 border border-gray-200">
          {isMonthly && (
            <div className="w-full">
                <p className="text-[18px] font-medium text-[#6F6F6E]">※恐れ入りますが、振込手数料はご負担ください。</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">※チョキペイにご登録いただいた口座と異なる名義の口座からご入金手続きをされる場合は、備考欄に、</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">{' '.repeat(4)}本請求書右上に記載の「請求書番号」をご入力のうえ、お振り込みください。</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">※既にクレジットカードを登録されている場合、事務手数料はかかりません。</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">※クレジットカード未登録の場合は、当月15日までに登録いただきますと、事務手数料はかかりません。</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">※同一法人で複数店舗でチョキペイをご利用の場合は、まとめてお振込いただくことが可能です。</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">{' '.repeat(4)}まとめてお振込いただく場合、店舗数に関わらず事務手数料は550円（税込）のみとなります</p>
            </div>
          )}
          {!isMonthly && (
            <div className="w-full">
                <p className="text-[18px] font-medium text-[#6F6F6E]">※恐れ入りますが、振込手数料はご負担ください。</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">※チョキペイにご登録いただいた口座と異なる名義の口座からご入金手続きをされる場合は、備考欄に、</p>
                <p className="text-[18px] font-medium text-[#6F6F6E]">{' '.repeat(4)}本請求書右上に記載の「請求書番号」をご入力のうえ、お振り込みください。</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 