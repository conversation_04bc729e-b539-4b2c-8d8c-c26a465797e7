import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { useMonthlySummary } from '@/features/store/summary/hooks/useMonthlySummary';
import SummaryHeader from './shared/SummaryHeader';
import SummaryMonthlyChart from './shared/SummaryMonthlyChart';
import SummaryTable from './shared/SummaryTable';

const SummaryMonthlyPage: React.FC<{agxMerchantNo: string; type: string  }> = ({agxMerchantNo, type }) => {
  const { user } = useAuthStore();
  const storeName = user?.agxStoreName || '';

  // Use actual API hook for monthly data
  const { summaryMonthlyData } = useMonthlySummary(agxMerchantNo);

  // Show error if no merchant number
  if (!agxMerchantNo) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-[#6F6F6E]">認証情報が見つかりません。</h2>
      </div>
    );
  }

  return (
    <div className="px-2 sm:px-4 py-4 sm:py-6">
      <SummaryHeader
        title="売上金額・件数推移（振込月別）"
        currentPath={`/store/summary-monthly/${type}`}
        type={type}
        storeName={storeName}
        agxMerchantNo={agxMerchantNo}
      />

      <SummaryMonthlyChart
        chartLabel={summaryMonthlyData.chartLabel}
        lineData={summaryMonthlyData.lineData}
        barData={summaryMonthlyData.barData}
        barLastYearData={summaryMonthlyData.barLastYearData}
      />

      <SummaryTable
        chartLabel={summaryMonthlyData.chartLabel}
        lineData={summaryMonthlyData.lineData}
        creditData={summaryMonthlyData.creditData}
        electronicData={summaryMonthlyData.electronicData}
        qrData={summaryMonthlyData.qrData}
        barData={summaryMonthlyData.barData}
        isMonthly={true}
      />
    </div>
  );
};

export default SummaryMonthlyPage;
