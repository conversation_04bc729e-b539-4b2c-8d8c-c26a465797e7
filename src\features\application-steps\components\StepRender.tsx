import { useEffect, useState } from "react";
import Houjin from "./Houjin";
import { STEP } from "@/constants/common.constant";
import { AgxMerchantParams } from "../types";
import { useAgxMerchants } from "../hooks/useAgxMerchants";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import Daiyoush<PERSON> from "./Daiyousha";
import ShopInfo from "./ShopInfo";
import Bank from "./Bank";
import Additional from "./Additional";
import { Additional1 } from "./Additional1";
import { ChoqipayPaygate } from "./ChoqipayPaygate";
import { ConfirmCrepico } from "./ConfirmCrepico";
import { CompleteComponent } from "./Complete";
import { KiyakuPaygate } from "./KiyakuPaygate";
import { PageHeader } from "@/components/PageHeader";
import Box from "@/components/ui/box";
import { progress, title } from "../utils";
import { ConfirmPaygate } from "./ConfirmPaygate";
import { ChoqiPayCrepico } from "./ChoqiPayCrepico";
import { KiyakuCrepico } from "./KiyakuCrepico";
import { AccountTypes } from "@/types/globalType";

const StepRender = () => {
    const [agxMerchantParams, setAgxMerchantParams] = useState<AgxMerchantParams | null>(null);

    const [step, setStep] = useState<number>(STEP.HOUJIN);

    const { user, merchant, isLoading, updateMerchant, isUpdating } = useAgxMerchants();

    useEffect(() => {   
        if (merchant) {
            setAgxMerchantParams(merchant);
            setStep(merchant.agxBusinessForm === ********* ? STEP.HOUJIN : STEP.DAIHYOUSHA);
        }
    }, [merchant, setAgxMerchantParams]);

    useEffect(() => {
        if (user.statusAccount === AccountTypes.APPLICATION_COMPLETE) {
            window.location.href = '/overview';
        }
    }, []);

    if (isLoading) return <LoadingSpinner />

    const renderStep = (step: number) => {
        switch (step) {
            case STEP.HOUJIN:
                return <Houjin
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.DAIHYOUSHA:
                return <Daiyousha
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.SHOPINFO:
                return <ShopInfo
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.BANK:
                return <Bank
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.ADDITIONAL:
                return <Additional
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.ADDITIONAL1:
                return <Additional1
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.CHOQIPAY:
                if (user.memberType) {
                    return <ChoqiPayCrepico
                        setStep={setStep}
                        agxMerchantParams={agxMerchantParams}
                        setAgxMerchantParams={setAgxMerchantParams}
                        updateMerchant={(data) => updateMerchant(data, false)}
                        isUpdating={isUpdating} />;
                }
                return <ChoqipayPaygate
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.KIYAKU:
                if (user.memberType) {
                    return <KiyakuCrepico
                        setStep={setStep}
                        agxMerchantParams={agxMerchantParams}
                        setAgxMerchantParams={setAgxMerchantParams}
                        updateMerchant={(data) => updateMerchant(data, false)}
                        isUpdating={isUpdating} />;
                }
                return <KiyakuPaygate
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.CONFIRM:
                if (user.memberType) {
                return <ConfirmCrepico
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                        setAgxMerchantParams={setAgxMerchantParams}
                        updateMerchant={(data) => updateMerchant(data, false)}
                        isUpdating={isUpdating} />;
                }
                return <ConfirmPaygate
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, false)}
                    isUpdating={isUpdating} />;
            case STEP.COMPLETE:
                return <CompleteComponent
                    setStep={setStep}
                    agxMerchantParams={agxMerchantParams}
                    setAgxMerchantParams={setAgxMerchantParams}
                    updateMerchant={(data) => updateMerchant(data, true)}
                    isUpdating={isUpdating} />;
            default:
                return <></>
        }
    }

    return (
        <>
            {step === STEP.COMPLETE ? (
                renderStep(step)
            ) : (
                <Box className="max-w-[1450px] mx-auto w-full px-4 md:px-6 py-4 md:py-8 mb-8 md:mb-16 lg:mb-32">
                    <PageHeader title={title(step)} progress={progress(step)} />
                    {renderStep(step)}
                </Box>
            )}
        </>
    )
}

export default StepRender;