import React from "react";
import { UseFormSetValue, UseFormTrigger, FieldErrors } from "react-hook-form";
import Box from "@/components/ui/box";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface PostalCodeInputProps {
    label: string;
    required?: boolean;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: () => void;
    onFindAddress: () => void;
    isLoading: boolean;
    error?: string;
    showError?: boolean;
    placeholder?: string;
    buttonText?: string;
    buttonDisabled?: boolean;
    className?: string;
}

export const PostalCodeInput: React.FC<PostalCodeInputProps> = ({
    label,
    required = false,
    value,
    onChange,
    onBlur,
    onFindAddress,
    isLoading,
    error,
    showError = false,
    placeholder = "000-0000",
    buttonText = "住所情報を入力",
    buttonDisabled = false,
    className = ""
}) => {
    return (
        <Box className={`flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative ${className}`}>
            <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                {label}
                {required && <span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>}
            </Label>
            <Box className="w-full md:w-[250px] lg:w-[388px] flex flex-col">
                <input
                    className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-3 md:px-4 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                    placeholder={placeholder}
                    value={value}
                    onChange={onChange}
                    onBlur={onBlur}
                    maxLength={8}
                />
                <div className="min-h-[16px] md:min-h-[20px] mt-1">
                    {showError && error && (
                        <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                            {error}
                        </span>
                    )}
                </div>
            </Box>
            <Box className="w-full md:flex-1">
                <Button
                    className="bg-[#A5A4A6] h-[40px] md:h-[50px] lg:h-[66px] w-full md:w-[200px] lg:w-[323px] text-sm md:text-base lg:text-[1.75rem] hover:bg-[#1D9987] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 rounded-lg transition-colors shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={onFindAddress}
                    disabled={isLoading || buttonDisabled}
                    type="button"
                >
                    {isLoading ? "検索中..." : buttonText}
                </Button>
            </Box>
        </Box>
    );
};

interface ThreePartNumberInputProps {
    label: string;
    required?: boolean;
    value1?: string;
    value2?: string;
    value3?: string;
    onChange1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onChange2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onChange3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: () => void;
    error?: string;
    showError?: boolean;
    placeholder1?: string;
    placeholder2?: string;
    placeholder3?: string;
    maxLength1?: number;
    maxLength2?: number;
    maxLength3?: number;
    className?: string;
    disabled?: boolean;
}

export const ThreePartNumberInput: React.FC<ThreePartNumberInputProps> = ({
    label,
    required = false,
    value1 = "",
    value2 = "",
    value3 = "",
    onChange1,
    onChange2,
    onChange3,
    onBlur,
    error,
    showError = false,
    placeholder1 = "000",
    placeholder2 = "0000",
    placeholder3 = "0000",
    maxLength1 = 5,
    maxLength2 = 5,
    maxLength3 = 5,
    className = "",
    disabled = false
}) => {
    return (
        <Box className={`flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative ${className}`}>
            <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                {label}
                {required && <span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>}
            </Label>
            <Box className="w-full md:flex-1 flex flex-col">
                <Box className="flex flex-row gap-2 md:gap-4 lg:gap-6 items-center">
                    <Box className="w-[70px] md:w-[80px] lg:w-[96px]">
                        <input
                            className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-2 md:px-3 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                            placeholder={placeholder1}
                            value={value1}
                            onChange={onChange1}
                            onBlur={onBlur}
                            maxLength={maxLength1}
                            disabled={disabled}
                        />
                    </Box>
                    <Box className="w-[10px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] flex items-center justify-center">−</Box>
                    <Box className="w-[70px] md:w-[80px] lg:w-[96px]">
                        <input
                            className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-2 md:px-3 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                            placeholder={placeholder2}
                            value={value2}
                            onChange={onChange2}
                            onBlur={onBlur}
                            maxLength={maxLength2}
                            disabled={disabled}
                        />
                    </Box>
                    <Box className="w-[10px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] flex items-center justify-center">−</Box>
                    <Box className="w-[70px] md:w-[80px] lg:w-[96px]">
                        <input
                            className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-2 md:px-3 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                            placeholder={placeholder3}
                            value={value3}
                            onChange={onChange3}
                            onBlur={onBlur}
                            maxLength={maxLength3}
                            disabled={disabled}
                        />
                    </Box>
                </Box>
                <div className="min-h-[16px] md:min-h-[20px] mt-1">
                    {showError && error && (
                        <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                            {error}
                        </span>
                    )}
                </div>
            </Box>
        </Box>
    );
};

interface PrefectureSelectProps {
    label: string;
    required?: boolean;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    error?: string;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
}

export const PrefectureSelect: React.FC<PrefectureSelectProps> = ({
    label,
    required = false,
    value,
    onChange,
    error,
    placeholder = "都道府県を選択してください",
    className = "",
    disabled = false
}) => {
    const prefectures = [
        "北海道", "青森県", "岩手県", "宮城県", "秋田県", "山形県", "福島県",
        "茨城県", "栃木県", "群馬県", "埼玉県", "千葉県", "東京都", "神奈川県",
        "新潟県", "富山県", "石川県", "福井県", "山梨県", "長野県", "岐阜県",
        "静岡県", "愛知県", "三重県", "滋賀県", "京都府", "大阪府", "兵庫県",
        "奈良県", "和歌山県", "鳥取県", "島根県", "岡山県", "広島県", "山口県",
        "徳島県", "香川県", "愛媛県", "高知県", "福岡県", "佐賀県", "長崎県",
        "熊本県", "大分県", "宮崎県", "鹿児島県", "沖縄県"
    ];

    return (
        <Box className={`flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative ${className}`}>
            <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                {label}
                {required && <span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>}
            </Label>
            <Box className="w-full md:w-[250px] lg:w-[388px] relative flex flex-col">
                <select
                    className="
                        w-full h-[40px] md:h-[50px] lg:h-[66px]
                        rounded-[8px] md:rounded-[10px] lg:rounded-[13px] 
                        border border-solid border-[#707070]
                        px-3 md:px-4 lg:px-[17px]
                        py-0 md:py-0 lg:py-0
                        pr-10 md:pr-12 lg:pr-14
                        text-sm md:text-base lg:text-[1.75rem] 
                        text-[#707070] bg-white
                        leading-[40px] md:leading-[50px] lg:leading-[66px]
                        focus:border-[#1D9987] focus:outline-none transition-colors 
                        appearance-none
                        disabled:bg-[#FAFAFA] disabled:text-[#707070] disabled:opacity-100 disabled:cursor-not-allowed"
                    value={value}
                    onChange={onChange}
                    disabled={disabled}
                >
                    {prefectures.map((pref) => (
                        <option key={pref} value={pref} className="text-[#707070]">
                            {pref}
                        </option>
                    ))}
                </select>

                {/* Custom dropdown arrow */}
                <div className="absolute right-0 top-[40%] -translate-y-1/2 flex items-center pr-2 md:pr-3 lg:pr-4 pointer-events-none">
                    <svg className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-[#6F6F6E]" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                </div>
                <div className="min-h-[16px] md:min-h-[20px] mt-1">
                    {error && (
                        <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                            {error}
                        </span>
                    )}
                </div>
            </Box>
            <Box className="hidden md:block md:flex-1">
                <p />
            </Box>
        </Box>
    );
};

interface TextInputProps {
    label: string;
    required?: boolean;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    placeholder?: string;
    maxLength?: number;
    className?: string;
    width?: "full" | "narrow";
}

export const TextInput: React.FC<TextInputProps> = ({
    label,
    required = false,
    value,
    onChange,
    error,
    placeholder = "",
    maxLength,
    className = "",
    width = "full"
}) => {
    const inputWidth = width === "narrow" ? "w-full md:w-[250px] lg:w-[388px]" : "w-full md:flex-1";

    return (
        <Box className={`flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 items-start relative ${className}`}>
            <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required pt-0 md:pt-2 text-[#6F6F6E]">
                {label}
                {required && <span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>}
            </Label>
            <Box className={`${inputWidth} flex flex-col`}>
                <input
                    className="w-full border-[#707070] border-solid border-[1px] rounded-[8px] md:rounded-[10px] lg:rounded-[13px] px-3 md:px-4 lg:px-[17px] py-2 md:py-3 lg:py-[13px] text-sm md:text-base lg:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                    placeholder={placeholder}
                    value={value}
                    onChange={onChange}
                    maxLength={maxLength}
                />
                <div className="min-h-[16px] md:min-h-[20px] mt-1">
                    {error && (
                        <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                            {error}
                        </span>
                    )}
                </div>
            </Box>
            {width === "narrow" && (
                <Box className="hidden md:block md:flex-1">
                    <p />
                </Box>
            )}
        </Box>
    );
}; 