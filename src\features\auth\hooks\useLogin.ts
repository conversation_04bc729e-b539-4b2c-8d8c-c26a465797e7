import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { authService } from '@/features/auth/services/authService';
import { useToast } from '@/hooks/use-toast';
import { AuthResponse, LoginParams } from '@/features/auth/types';
import { contactStore } from '@/features/auth/slices/contactStore';
import { AccountTypes } from '@/types/globalType';
import { LoginFormData } from '@/features/auth/schema';

// Constants
const TOAST_MESSAGES = {
  LOGIN_SUCCESS: {
    title: "ログイン成功",
    description: "ダッシュボードにリダイレクトしています...",
    duration: 2000,
  },
  LOGIN_ERROR: {
    title: "ログインエラー",
    duration: 3000,
    variant: "destructive" as const,
  },
} as const;

const ERROR_MESSAGES = {
  INVALID_CREDENTIALS: 'ログインに失敗しました。メールアドレスとパスワードを確認してください。',
  GENERAL_ERROR: 'ログインエラーが発生しました。',
} as const;

// Navigation mapping
const ACCOUNT_TYPE_ROUTES: Record<AccountTypes, string> = {
  [AccountTypes.ADMIN_STORE]: '/overview',
  [AccountTypes.STORE]: '/overview',
  [AccountTypes.APPLICATION]: '/register-merchant',
  [AccountTypes.APPLICATION_STEPS]: '/application-steps',
  [AccountTypes.APPLICATION_COMPLETE]: '/overview',
  [AccountTypes.APPLICATION_CANCEL]: '/overview',
  [AccountTypes.APPLICATION_ACCEPTED_CORRECTIONS]: '/store-edit',
  [AccountTypes.APPLICATION_REJECTED]: '/rejected',
} as const;

const DEFAULT_ROUTE = '/overview';
const VERIFICATION_ROUTE = '/verification';

export const useLogin = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { setId } = contactStore();

  // Helper function to handle navigation logic
  const handleNavigation = (response: AuthResponse): void => {
    if (response.requireVerification) {
      navigate(VERIFICATION_ROUTE, { replace: true });
      return;
    }

    const route = response.statusAccount 
      ? ACCOUNT_TYPE_ROUTES[response.statusAccount] || DEFAULT_ROUTE
      : DEFAULT_ROUTE;
      
    navigate(route, { replace: true });
  };

  // Helper function to get error message
  const getErrorMessage = (error: any): string => {
    if (error?.response?.data?.statusCode === 41) {
      return ERROR_MESSAGES.INVALID_CREDENTIALS;
    }
    return error?.response?.data?.message || ERROR_MESSAGES.GENERAL_ERROR;
  };

  const loginMutation = useMutation({
    mutationFn: async (data: LoginParams): Promise<AuthResponse> => {
      return await authService.login({
        username: data.username,
        password: data.password,
      });
    },
    onSuccess: (response: AuthResponse) => {
      toast(TOAST_MESSAGES.LOGIN_SUCCESS);
      setId(response.id);
      handleNavigation(response);
    },
    onError: (error: unknown) => {
      console.error('Login error:', error);
      toast({
        ...TOAST_MESSAGES.LOGIN_ERROR,
        description: getErrorMessage(error),
      });
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    loginMutation.reset();

    loginMutation.mutate({
      username: data.email,
      password: data.password,
    });
  };

  return {
    login: loginMutation.mutate,
    loginAsync: loginMutation.mutateAsync,
    isLoading: loginMutation.isPending,
    isError: loginMutation.isError,
    error: loginMutation.error,
    reset: loginMutation.reset,
    onSubmit,
  } as const;
}; 