import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { GetMerchantStatusResponse } from "../types";
import { convertDate, convertPhoneAndFax } from "@/utils/helper";
import { formatDateYearMonth } from "@/utils/dateUtils";

interface AdditionalInformationProps {
  merchantData: GetMerchantStatusResponse;
}

function AdditionalInformation({ merchantData }: AdditionalInformationProps) {

  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            ご担当者名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxContactName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            ご担当者名（フリガナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div
              id="contact_phonetic_name"
              className="w-full h-10 rounded-md flex items-center px-3"
            >
              {merchantData?.agxContactPhoneticName ??
                ""}
            </div>
          </div>
          <div className="col-span-3">（全角カナ）</div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            ご担当者メールアドレス<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxContactEmail ?? ""}
            </div>
          </div>
          <div className="col-span-3">（半角英数字）</div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            ご担当者電話番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-16 h-10 rounded-md flex items-center px-3">
              {convertPhoneAndFax(merchantData?.agxContactPhoneNumber)[0] || ""}
            </div>
            <span>-</span>
            <div className="w-16 h-10 rounded-md flex items-center px-3">
              {convertPhoneAndFax(merchantData?.agxContactPhoneNumber)[1] || ""}
            </div>
            <span>-</span>
            <div className="w-16 h-10 rounded-md flex items-center px-3">
              {convertPhoneAndFax(merchantData?.agxContactPhoneNumber)[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            資本金<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxCapital ?? ""}
            </div>
            <span className="whitespace-nowrap">万円</span>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            従業員数<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center">
            <div
              id="number_of_employees"
              className="w-full h-10 rounded-md flex items-center px-3"
            >
              {merchantData?.agxNumberOfEmployees ??
                ""}
            </div>
            <span className="ml-2">人</span>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            設立年月<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-1/2 h-10 rounded-md flex items-center px-3">
              {convertDate(merchantData?.agxFoundingDate)[0] || ""}年
            </div>
            <div className="w-1/2 h-10 rounded-md flex items-center px-3">
              {convertDate(merchantData?.agxFoundingDate)[1] || ""}月
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            月商<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center">
            <div
              id="monthly_sales"
              className="w-full h-10 rounded-md flex items-center px-3"
            >
              {merchantData?.agxMonthlySales ?? ""}
            </div>
            <span className="whitespace-nowrap">万円</span>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-4">
          特定商取引に関する確認
        </h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">訪問販売</Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxDoorToDoorSales ? "有" : "無"}
            </div>
          </div>
          <Label className="col-span-3 text-right text-[20px]">
            電話勧誘販売
          </Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxTelemarketingSales ? "有" : "無"}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            連鎖販売取引
          </Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxPyramidScheme ? "有" : "無"}
            </div>
          </div>
          <Label className="col-span-3 text-right text-[20px]">
            業務提供誘引販売取引
          </Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBusinessOpportunityRelatedSales ? "有" : "無"}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            特定継続的役務
          </Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxSpecifiedContinuousServices ? "有" : "無"}
            </div>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-4">
          カード情報保護対策
        </h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-5 text-right text-[20px]">
            【A】 クレジットカード情報の保持状況について
          </Label>
          <div className="col-span-7">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxCardInformationRetentionStatus === 283260000
                ? "保持している"
                : merchantData?.agxCardInformationRetentionStatus === 283260001
                ? "保持していない"
                : merchantData?.agxCardInformationRetentionStatus === 283260002
                ? `非保持化の予定あり ${formatDateYearMonth(
                    typeof merchantData?.agxNoRetainingCardInfoDate === "string"
                      ? merchantData?.agxNoRetainingCardInfoDate
                      : merchantData?.agxNoRetainingCardInfoDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-5 text-right text-[20px]">
            【B】 PCI DSSの準拠状況について
          </Label>
          <div className="col-span-7">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxPcidssStatus === 283260000
                ? "準拠している"
                : merchantData?.agxPcidssStatus === 283260001
                ? "準拠予定なし"
                : merchantData?.agxPcidssStatus === 283260002
                ? `準拠予定あり ${formatDateYearMonth(
                    typeof merchantData?.agxPcidssExpectedComplianceDate === "string"
                      ? merchantData?.agxPcidssExpectedComplianceDate
                      : merchantData?.agxPcidssExpectedComplianceDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-4">不正使用対策</h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-12 text-left font-bold">
            【C】クレジットカードの不正使用対策について
          </Label>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 text-right text-[20px]">
            ① 本人認証サービス(3Dセキュア)
          </Label>
          <div className="col-span-8">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxThreeDSecureStatus === 283260000
                ? "導入済み"
                : merchantData?.agxThreeDSecureStatus === 283260001
                ? "導入予定なし"
                : merchantData?.agxThreeDSecureStatus === 283260002
                ? `導入予定 ${formatDateYearMonth(
                    typeof merchantData?.agxThreeDSecureDate === "string"
                      ? merchantData?.agxThreeDSecureDate
                      : merchantData?.agxThreeDSecureDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 text-right text-[20px]">
            ② セキュリティコードチェック
          </Label>
          <div className="col-span-8">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxSecurityCodeCheckStatus === 283260000
                ? "導入済み"
                : merchantData?.agxSecurityCodeCheckStatus === 283260001
                ? "導入予定なし"
                : merchantData?.agxSecurityCodeCheckStatus === 283260002
                ? `導入予定 ${formatDateYearMonth(
                    typeof merchantData?.agxSecurityCodeCheckDate === "string"
                      ? merchantData?.agxSecurityCodeCheckDate
                      : merchantData?.agxSecurityCodeCheckDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 text-right text-[20px]">
            ③ 不正配送先情報の活用
          </Label>
          <div className="col-span-8">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxIllegalDeliveryDestinationStatus === 283260000
                ? "導入済み"
                : merchantData?.agxIllegalDeliveryDestinationStatus ===
                  283260001
                ? "導入予定なし"
                : merchantData?.agxIllegalDeliveryDestinationStatus ===
                  283260002
                ? `導入予定 ${formatDateYearMonth(
                    typeof merchantData?.agxIllegalDeliveryDestinationDate === "string"
                      ? merchantData?.agxIllegalDeliveryDestinationDate
                      : merchantData?.agxIllegalDeliveryDestinationDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 text-right text-[20px]">
            ④ 属性・行動分析
          </Label>
          <div className="col-span-8">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBehaviorAnalysisStatus === 283260000
                ? "導入済み"
                : merchantData?.agxBehaviorAnalysisStatus === 283260001
                ? "導入予定なし"
                : merchantData?.agxBehaviorAnalysisStatus === 283260002
                ? `導入予定 ${formatDateYearMonth(
                    typeof merchantData?.agxBehaviorAnalysisDate === "string"
                      ? merchantData?.agxBehaviorAnalysisDate
                      : merchantData?.agxBehaviorAnalysisDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 text-right text-[20px]">
            ⑤ その他の対策
          </Label>
          <div className="col-span-8">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxOtherMeasuresStatus === 283260000
                ? "導入済み"
                : merchantData?.agxOtherMeasuresStatus === 283260001
                ? "導入予定なし"
                : merchantData?.agxOtherMeasuresStatus === 283260002
                ? `導入予定 ${formatDateYearMonth(
                    typeof merchantData?.agxOtherMeasuresDate === "string"
                      ? merchantData?.agxOtherMeasuresDate
                      : merchantData?.agxOtherMeasuresDate?.toISOString().slice(0, 10)
                  )}`
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-4 text-right text-[20px]">対策内容</Label>
          <div className="col-span-8">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxOtherMeasuresDescription ?? ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default AdditionalInformation;
