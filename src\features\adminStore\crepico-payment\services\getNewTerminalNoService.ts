import apiService from '@/services/api';
import { API_ENDPOINTS } from '@/config/api-endpoints';

interface TerminalNoItem {
  label: string;
  value: string;
}

interface GetNewTerminalNoResponse {
  data: {
    newTerminalNo: TerminalNoItem[];
  };
}

class GetNewTerminalNoService {
  async getData(merchantNo: string): Promise<GetNewTerminalNoResponse> {
    return apiService.get(API_ENDPOINTS.ADMIN_STORE_NEW_TERMINAL_NO.GET_DATA(btoa(merchantNo)));
  }
}

export const getNewTerminalNoService = new GetNewTerminalNoService();
