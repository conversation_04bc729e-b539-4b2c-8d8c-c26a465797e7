import { Card, CardContent } from "@/components/ui/card";
import { formatNumber } from "@/utils/dateUtils";
import { formatNumberJP } from "@/utils/helper";
import { FileText, File } from "lucide-react";
import { Link } from "react-router-dom";

export interface Column {
  key: string;
  label: string;
  className?: string;
}

export interface SectionProps {
  columns: Column[];
  data: any[];
  renderCell?: (column: Column, item: any, index: number) => React.ReactNode;
  className?: string;
  routePrefix?: string; // '/store-invoice-receipt/paygate' or '/store-invoice-receipt/crepico'
}

export const Section = ({ 
  columns, 
  data, 
  renderCell, 
  className = "", 
  routePrefix = "" 
}: SectionProps) => {

  // Convert url from admin-invoice-receipt to invoice-receipt
  const convertUrl = (url: string) => {
    if (url.startsWith('/admin-invoice-receipt')) {
      return url.replace('/admin-invoice-receipt', '/invoice-receipt');
    }
    return url;
  }

  const defaultRenderCell = (column: Column, item: any, index: number) => {
    const value = item[column.key];

    if (column.key === 'month') {
      return <div className="w-[40%] text-right">{value}</div>;
    }
    
    // Handle invoice column
    if (column.key.includes('invoice') && value) {
      return (
        <Link to={`${routePrefix}${value.startsWith('/') ? convertUrl(value) : `/${value}`}`}>
          {item['paymentDue'] !== '-' ? (
            <File className="h-6 w-6 mx-auto text-[#C44546] hover:text-[#C44546]/80 cursor-pointer" />
          ) : (
            <File className="h-6 w-6 mx-auto text-gray-500 hover:text-gray-600 cursor-pointer" />
          )}
        </Link>
      );
    }
    
    // Handle receipt column
    if (column.key.includes('receipt') && value) {
      return (
        <Link to={`${routePrefix}${value.startsWith('/') ? convertUrl(value) : `/${value}`}`}>
          <FileText className="h-6 w-6 mx-auto text-[#1D9987] hover:text-[#1D9987]/80" />
        </Link>
      );
    }
    
    // Handle payment due date styling
    if (column.key === 'paymentDue' && value !== '-') {
      return <span className="text-[#C44546]">{value}</span>;
    }

    if (column.key === 'total') {
      return formatNumber(value);
    }
    
    return value;
  };

  const cellRenderer = renderCell || defaultRenderCell;

  return (
    <Card className={`border-none shadow-none ${className}`}>
      <CardContent>
        <div className="text-center">
          {/* Header */}
          <div className="">
            <div className="flex space-x-6">
              {columns.map((column) => (
                <div 
                  key={column.key}
                  className={`flex-1 font-medium text-[#707070] lg:text-[18px] text-[16px] px-4 py-3 border-b border-[#707070] ${column.className || ''}`}
                >
                  {column.label}
                </div>
              ))}
            </div>
          </div>
          
          {/* Body */}
          <div>
            {data.map((item, index) => (
              <div key={index} className="flex hover:bg-gray-50 space-x-6">
                {columns.map((column) => (
                  <div 
                    key={column.key}
                    className="flex-1 px-4 py-3 text-[#707070] lg:text-[18px] text-[16px] flex items-center justify-center"
                  >
                    {cellRenderer(column, item, index)}
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
