import { useQuery } from '@tanstack/react-query';
import { invoiceService } from '../services/invoiceReceiptService';
import { MonthlyCostByStoreType } from '../types';

export const useGetDataMonthlyCostByStore = (merchantNo: string, yearMonth: string) => {
  return useQuery({
    queryKey: ['admin-monthly-cost-by-store', merchantNo, yearMonth],
    queryFn: async (): Promise<MonthlyCostByStoreType> => {
      if (!merchantNo || !yearMonth) {
        throw new Error('Merchant number and year month are required');
      }
      const response = await invoiceService.getDataMonthlyCostByStore(merchantNo, yearMonth);
      return response.data;
    },
    enabled: !!merchantNo && !!yearMonth,
  });
};
