export interface GetAreaSettingResponse {
    agxAreas: AgxArea[]
    agxSubAreas: AgxSubArea[]
    agxSubAreaModal: AgxSubAreaModal[]
}

export interface AgxArea {
    agx_areaid: string
    agxAreaName: string
}

export interface AgxSubArea {
    agxSubAreaid: string
    agxSubAreaName: string
    agxAreaid: string
    agxAreaName: string
    agx_sub_area_id: string
}

export interface AgxSubAreaModal {
    agxSubAreaid: any
    agxStoreName: string
    agxMerchantNo: string
    agxSubAreaName: any
    agxAreaid: any
}

export interface AgxAreaParam {
    agx_areaid: string,
    agxMerchantCreatedNo: string,
    agxAreaName: string,
    agxSubAreaids: string[]
}

export interface AgxSubAreaParam {
    agx_sub_areaid: string,
    agxSubAreaName: string,
    agxMerchantCreatedNo: string,
    agxMerchantNos: string[]
}

export interface DataAreaSettingType{
  agxAreas: AgxArea[],
  agxSubAreas: AgxSubArea[],
  agxSubAreaModal: AgxSubAreaModal[],
  loading: boolean,
  error: boolean
}
