import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  return (
    <div className={`flex items-center justify-center min-h-[calc(100vh-225px)] ${className}`}>
      <div className={`animate-spin rounded-full border-b-2 border-[#1D9987] ${sizeClasses[size]}`}></div>
    </div>
  );
}; 