import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { invoiceService } from '../services/invoiceReceiptService';

interface GmoInfo {
  memberId: string | null;
  cardNo: string | null;
}

interface GmoPaymentResponse {
  LinkUrl: string;
}

// Hook để lấy thông tin GMO
export const useGetGmoInfo = () => {
  const { user } = useAuthStore();

  return useQuery({
    queryKey: ['gmo-info', user?.agxMerchantNo],
    queryFn: async (): Promise<GmoInfo> => {
      if (!user?.agxMerchantNo) {
        throw new Error('Merchant number not found');
      }
      const response = await invoiceService.getGmoInfo(btoa(user.agxMerchantNo));
      return response.data;
    },
    enabled: !!user?.agxMerchantNo,
  });
};

// Hook để lấy URL thanh toán GMO (mutation vì nó trigger action)
export const useGetGmoPaymentUrl = () => {
  const { user } = useAuthStore();

  return useMutation({
    mutationFn: async (): Promise<GmoPaymentResponse> => {
      if (!user?.agxMerchantNo) {
        throw new Error('Merchant number not found');
      }
      const response = await invoiceService.getGmoPaymentUrl(btoa(user.agxMerchantNo));
      return response.data;
    },
    onSuccess: (data) => {
      if (data.LinkUrl) {
        window.open(data.LinkUrl, '_blank');
      }
    },
    onError: (error) => {
      console.error('Failed to get GMO payment URL:', error);
    },
  });
};

// Hook để lấy URL chỉnh sửa thẻ tín dụng GMO (mutation vì nó trigger action)
export const useGetGmoLinkplusUrl = () => {
  return useMutation({
    mutationFn: async ({ memberId, cardNo }: { memberId: string; cardNo: string }): Promise<GmoPaymentResponse> => {
      if (!memberId || !cardNo) {
        throw new Error('Member ID or card number not found');
      }
      const response = await invoiceService.getGmoLinkplusUrl(btoa(memberId), cardNo);
      return response.data;
    },
    onSuccess: (data) => {
      if (data.LinkUrl) {
        window.open(data.LinkUrl, '_blank');
      }
    },
    onError: (error) => {
      console.error('Failed to get GMO linkplus URL:', error);
    },
  });
};

// Hook tổng hợp cho tất cả GMO functionalities
export const useGmoServices = () => {
  const gmoInfoQuery = useGetGmoInfo();
  const gmoPaymentMutation = useGetGmoPaymentUrl();
  const gmoLinkplusMutation = useGetGmoLinkplusUrl();

  const handlePaymentGMO = () => {
    gmoPaymentMutation.mutate();
  };

  const handleCreateLinkplusUrl = () => {
    const gmoData = gmoInfoQuery.data;
    if (gmoData?.memberId && gmoData?.cardNo) {
      gmoLinkplusMutation.mutate({
        memberId: gmoData.memberId,
        cardNo: gmoData.cardNo,
      });
    }
  };

  return {
    // GMO Info
    gmoInfo: gmoInfoQuery.data,
    
    // GMO Payment
    handlePaymentGMO,
    isLoadingPayment: gmoPaymentMutation.isPending,
    
    // GMO Linkplus
    handleCreateLinkplusUrl,
    isLoadingLinkplus: gmoLinkplusMutation.isPending,
    
    // General loading state
    isLoading: gmoInfoQuery.isLoading || gmoPaymentMutation.isPending || gmoLinkplusMutation.isPending,
  };
};
