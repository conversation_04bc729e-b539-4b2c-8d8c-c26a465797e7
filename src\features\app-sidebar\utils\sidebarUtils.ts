import { AccountTypes, TypeStore } from '@/types/globalType';
import { getStoreMenuItems, getAdminStoreMenuItems, MenuItem } from '../constants/sidebarConstants';

// Helper function to check if URL is external
export const isExternalUrl = (url: string): boolean => {
  return url.startsWith('http://') || url.startsWith('https://');
};

export const getMenuItems = (statusAccount: AccountTypes | undefined, typeStore: TypeStore): MenuItem[] => {
  switch (statusAccount) {
    case AccountTypes.ADMIN_STORE:
      return getAdminStoreMenuItems();
    case AccountTypes.STORE:
      return getStoreMenuItems(typeStore);
    default:
      return getStoreMenuItems(typeStore);
  }
};

export const isMenuActive = (pathname: string, menuUrl: string): boolean => {
  return pathname === menuUrl || (menuUrl && pathname.startsWith(menuUrl));
};

export const hasActiveSubItem = (pathname: string, subItems?: Array<{ url: string }>): boolean => {
  return subItems?.some(subItem => pathname === subItem.url) || false;
};

export const formatLastLogin = (lastLogin: string | undefined): string => {
  if (!lastLogin) return '20xx/mm/dd hh:mm:ss';
  return new Date(lastLogin).toLocaleString('ja-JP');
};

export const toggleSubmenuState = (
  currentState: Record<string, boolean>,
  menuTitle: string
): Record<string, boolean> => {
  return {
    ...currentState,
    [menuTitle]: !currentState[menuTitle]
  };
}; 