import React from 'react';
import { AdminDepositPage } from '@/features/adminStore/adminDeposit';
import { useAuthStore } from '@/features/auth/slices/authStore';

const AdminDeposit: React.FC = () => {
  const { user } = useAuthStore();

  // Use actual merchant number from auth store
  const agxMerchantNo = user?.agxMerchantNo;

  if (!agxMerchantNo) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">認証情報が見つかりません。</h2>
      </div>
    );
  }

  return <AdminDepositPage agxMerchantNo={agxMerchantNo} />;
};

export default AdminDeposit;
