import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { InvoiceDetailResponse } from "./invoiceService";

class InvoiceMonthlyService {
    async getData(merchantNo: string, invoiceNo: string): Promise<InvoiceDetailResponse> {
        const response = await apiService.get<InvoiceDetailResponse>(
            API_ENDPOINTS.INVOICE_RECEIPT.GET_DATA_INVOICE_MONTHLY(merchantNo, invoiceNo)
        );
        return response;
    }
}

export default new InvoiceMonthlyService();