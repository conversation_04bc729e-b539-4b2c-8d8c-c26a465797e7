<svg id="グループ_23" data-name="グループ 23" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="33.205" height="24.904" viewBox="0 0 33.205 24.904">
  <defs>
    <clipPath id="clip-path">
      <path id="パス_71" data-name="パス 71" d="M25,12.957v2.189h2.4a1.756,1.756,0,0,0,.286-.027,1.1,1.1,0,0,0,0-2.135,1.756,1.756,0,0,0-.286-.027Z" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="-7.073" y1="5.458" x2="-6.79" y2="5.458" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#015824"/>
      <stop offset="0.234" stop-color="#186f24"/>
      <stop offset="0.745" stop-color="#469f24"/>
      <stop offset="1" stop-color="#59b224"/>
    </linearGradient>
    <clipPath id="clip-path-2">
      <path id="パス_72" data-name="パス 72" d="M27.117,0a4.152,4.152,0,0,0-4.151,4.151V8.462H28.84c.135,0,.3.009.406.014,1.33.067,2.31.75,2.31,1.935a1.972,1.972,0,0,1-1.89,1.906v.047c1.347.094,2.379.835,2.379,2,0,1.263-1.133,2.076-2.641,2.076H22.966V24.9h6.089a4.151,4.151,0,0,0,4.15-4.151V0Z" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="-2.243" y1="1" x2="-2.145" y2="1" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-3">
      <path id="パス_73" data-name="パス 73" d="M25,9.7v2.022h2.177a1.89,1.89,0,0,0,.237-.019,1.007,1.007,0,0,0,0-1.984,1.89,1.89,0,0,0-.237-.019Z" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-3" x1="-7.691" y1="7.519" x2="-7.383" y2="7.519" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-4">
      <path id="パス_74" data-name="パス 74" d="M4.151,0A4.152,4.152,0,0,0,0,4.151V14.4a8.28,8.28,0,0,0,3.591.931,2,2,0,0,0,2.223-2.054V8.462h3.57v4.816c0,1.883-1.156,3.394-5.123,3.394A17.828,17.828,0,0,1,0,16.157V24.9H6.088a4.151,4.151,0,0,0,4.15-4.151V0Z" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-4" y1="1" x2="0.098" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0d0157"/>
      <stop offset="0.211" stop-color="#0c1569"/>
      <stop offset="0.738" stop-color="#0a4594"/>
      <stop offset="1" stop-color="#0a58a5"/>
    </linearGradient>
    <clipPath id="clip-path-5">
      <path id="パス_75" data-name="パス 75" d="M15.634,0a4.152,4.152,0,0,0-4.151,4.151V9.584c1.052-.9,2.87-1.465,5.825-1.332a18.844,18.844,0,0,1,3.259.494V10.5a8.1,8.1,0,0,0-3.143-.918c-2.243-.169-3.592.927-3.592,2.866s1.349,3.035,3.592,2.865a8.218,8.218,0,0,0,3.143-.916v1.756a18.964,18.964,0,0,1-3.259.5c-2.955.133-4.773-.43-5.825-1.333V24.9h6.088a4.151,4.151,0,0,0,4.15-4.151V0Z" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-5" x1="-1.122" y1="1" x2="-1.024" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4d0000"/>
      <stop offset="0.155" stop-color="#6b0006"/>
      <stop offset="0.564" stop-color="#b80017"/>
      <stop offset="0.855" stop-color="#e80021"/>
      <stop offset="1" stop-color="#fb0026"/>
    </linearGradient>
  </defs>
  <g id="グループ_14" data-name="グループ 14">
    <g id="グループ_13" data-name="グループ 13" clip-path="url(#clip-path)">
      <rect id="長方形_74" data-name="長方形 74" width="3.534" height="2.189" transform="translate(24.996 12.957)" fill="url(#linear-gradient)"/>
    </g>
  </g>
  <g id="グループ_16" data-name="グループ 16">
    <g id="グループ_15" data-name="グループ 15" clip-path="url(#clip-path-2)">
      <rect id="長方形_75" data-name="長方形 75" width="10.239" height="24.904" transform="translate(22.966 0)" fill="url(#linear-gradient-2)"/>
    </g>
  </g>
  <g id="グループ_18" data-name="グループ 18">
    <g id="グループ_17" data-name="グループ 17" clip-path="url(#clip-path-3)">
      <rect id="長方形_76" data-name="長方形 76" width="3.25" height="2.022" transform="translate(24.996 9.7)" fill="url(#linear-gradient-3)"/>
    </g>
  </g>
  <g id="グループ_20" data-name="グループ 20">
    <g id="グループ_19" data-name="グループ 19" clip-path="url(#clip-path-4)">
      <rect id="長方形_77" data-name="長方形 77" width="10.238" height="24.904" transform="translate(0 0)" fill="url(#linear-gradient-4)"/>
    </g>
  </g>
  <g id="グループ_22" data-name="グループ 22">
    <g id="グループ_21" data-name="グループ 21" clip-path="url(#clip-path-5)">
      <rect id="長方形_78" data-name="長方形 78" width="10.238" height="24.904" transform="translate(11.483 0)" fill="url(#linear-gradient-5)"/>
    </g>
  </g>
</svg>
