import { useQuery } from '@tanstack/react-query';
import { agreementService } from '@/features/agreement/services/agreementService';
import { AgreementData } from '../type';

interface UseGetAgreementReturn {
  data: AgreementData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useGetAgreement = (merchantNo: string): UseGetAgreementReturn => {
    const {data, isLoading, error, refetch} = useQuery({
      queryKey: ['agreement-data', merchantNo],
      queryFn: async () => {
          const result = await agreementService.getData(btoa(merchantNo));
          return result.data;
      },
      enabled: !!merchantNo,
      staleTime: 0,
      gcTime: 0,
    });

    return { data, isLoading, error: error?.message || null, refetch };
}; 