// Calendar related types
export interface CalendarItem {
  period: string;
  transferDate: string;
  isHighlighted: boolean;
}

export interface TransferCycleInfoProps {
  isAdmin?: boolean;
}

export interface CalendarTableProps {
  data: CalendarItem[];
  isAdmin?: boolean;
}

export interface PageTitleProps {
  title: string;
}

export interface AdminActionsProps {
  className?: string;
}

// Paygate related types
export interface PaygateData {
  id: string;
  password: string;
  appSerialNumber: string;
}

export interface PaygateSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export interface AdminOverviewProps {
  className?: string;
}

export interface PaymentTerminalSetupProps {
  appSerialNumber: string;
  isAdmin?: boolean;
  className?: string;
}

export interface TerminalManagementProps {
  id: string;
  password: string;
  isAdmin?: boolean;
  className?: string;
}

export interface AdminToolsProps {
  className?: string;
}

export interface ErrorDisplayProps {
  error: string;
  className?: string;
}

export interface LoadingSpinnerProps {
  message?: string;
  className?: string;
}
