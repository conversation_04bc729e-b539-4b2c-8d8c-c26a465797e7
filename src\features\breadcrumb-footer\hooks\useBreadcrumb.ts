import { useLocation } from 'react-router-dom';
import { parseBreadcrumb, shouldDisplayBreadcrumb, BreadcrumbItem } from '../utils/breadcrumbUtils';

export interface UseBreadcrumbReturn {
  breadcrumbs: BreadcrumbItem[];
  shouldDisplay: boolean;
  currentPath: string;
}

/**
 * Custom hook for breadcrumb functionality
 * @returns Object containing breadcrumb data and display state
 */
export const useBreadcrumb = (): UseBreadcrumbReturn => {
  const location = useLocation();
  
  const shouldDisplay = shouldDisplayBreadcrumb(location.pathname);
  const breadcrumbs = shouldDisplay ? parseBreadcrumb(location.pathname) : [];
  
  return {
    breadcrumbs,
    shouldDisplay,
    currentPath: location.pathname,
  };
}; 