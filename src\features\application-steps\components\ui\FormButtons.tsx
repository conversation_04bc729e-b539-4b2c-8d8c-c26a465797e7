import React from "react";
import { Button } from "@/components/ui/button";
import Box from "@/components/ui/box";
import { cn } from "@/lib/utils";

interface FormButtonsProps {
  onSave?: () => void;
  onNext?: () => void;
  onBack?: () => void;
  isSubmitting?: boolean;
  showBackButton?: boolean;
  className?: string;
}

export const FormButtons: React.FC<FormButtonsProps> = ({
  onSave,
  onNext,
  onBack,
  isSubmitting = false,
  showBackButton = false,
  className = "",
}) => {

  return (
    <Box className={cn("flex flex-col sm:flex-row justify-center sm:justify-start items-center gap-3 sm:gap-4 pt-4 md:pt-6 lg:pt-8 px-4 sm:px-4 lg:px-0", className)}>
      <Button
        type="button"
        onClick={onSave}
        disabled={isSubmitting}
        className="w-full sm:w-[140px] ml-0 mr-0 sm:ml-[199px] sm:mr-[89px] md:w-[160px] lg:w-[184px] h-[50px] md:h-[60px] lg:h-[66px] text-base md:text-xl lg:text-[1.75rem] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 bg-red-400 rounded-lg md:rounded-xl hover:bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-red-400"
      >
        一時保存
      </Button>
      
      {showBackButton && (
        <Button
          type="button"
          onClick={onBack}
          className="w-full sm:w-[140px] md:w-[160px] lg:w-[187px] h-[50px] md:h-[60px] lg:h-[66px] text-base md:text-xl lg:text-[1.75rem] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 bg-gray-400 rounded-lg md:rounded-xl hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400"
        >
          戻る
        </Button>
      )}
      
      <Button
        type="submit"
        onClick={onNext}
        disabled={isSubmitting}
        className="w-full sm:w-[140px] md:w-[160px] lg:w-[187px] h-[50px] md:h-[60px] lg:h-[66px] text-base md:text-xl lg:text-[1.75rem] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 bg-teal-500 rounded-lg md:rounded-xl hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500"
      >
        次へ
      </Button>
    </Box>
  );
};
