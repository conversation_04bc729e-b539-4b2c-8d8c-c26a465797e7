import React, { useMemo } from 'react';
import { Link, useParams, useSearchParams } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAdminDepositDetail } from '../hooks/useAdminDepositDetail';
import { AdminDepositDetailCSVData } from '../types';
import { formatNumber } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import { PDFTemplates } from '@/services/pdfTemplates';
import { CSVLink } from 'react-csv';
import _ from 'lodash';

interface AdminDepositDetailPageProps {
  agxMerchantNo: string;
}

export const AdminDepositDetailPage: React.FC<AdminDepositDetailPageProps> = ({ agxMerchantNo }) => {
  const { transferDate, transactionType, merchantNo, paymentBId } = useParams<{
    transferDate: string;
    transactionType?: string;
    merchantNo?: string;
    paymentBId?: string;
  }>();
  const [searchParams] = useSearchParams();
    const area = searchParams.get('area');
    const subArea = searchParams.get('subArea');
    const merchant = searchParams.get('merchant');

  // Fetch data using React Query
  const { data, isLoading, error } = useAdminDepositDetail(
    agxMerchantNo,
    transferDate || '',
    transactionType,
    merchantNo || merchant,
    paymentBId,
    area,
    subArea
  );

  // CSV Export data
  const csvHeaders = [
    { label: '利用日', key: 'transactionDate' },
    { label: '加盟店ID', key: 'merchantNo' },
    { label: '加盟店名', key: 'storeName' },
    { label: '取引種別', key: 'transactionType' },
    { label: '売上額', key: 'salesAmount' },
    { label: '振込日', key: 'paymentDate' },
    { label: '会員番号', key: 'memberId' }
  ];

  const csvData: AdminDepositDetailCSVData[] = useMemo(() => {
    if (!data?.data) return [];

    return data.data.map(item => ({
      transactionDate: item.transactionDate,
      merchantNo: item.merchantNo,
      storeName: item.storeName,
      transactionType: `${mapTransactionType.get(item.transactionType)}${item.groupCodeName}`,
      salesAmount: item.salesAmount,
      paymentDate: item.paymentDate,
      memberId: item.memberId
    }));
  }, [data]);

  // Get unique merchant numbers for display
  const displayMerchantNos = useMemo(() => {
    if (!data?.data) return '';
    if (merchantNo) return merchantNo;

    const uniqueMerchants = _.uniqBy(data.data, 'merchantNo');
    return uniqueMerchants.map((item, index) =>
      index === 0 ? item.merchantNo : ` , ${item.merchantNo}`
    ).join('');
  }, [data, merchantNo]);



  const handleExportPDF = async () => {
    if (!data) {
      alert('エクスポートするデータがありません。');
      return;
    }

    try {
      await PDFTemplates.generateAdminDepositDetailPDF({
        data,
        transferDate: transferDate || '',
        merchantNos: displayMerchantNos
      });
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF export failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mx-6 my-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">データが見つかりません。</h2>
      </div>
    );
  }

  return (
    <div className=" px-6 py-4 mb-16 text-lg text-[#6F6F6E]">
      {/* Summary Information */}
      <div className="mb-6 space-y-2">
        <div>
          <span>加盟店ID: </span>
          <span>{displayMerchantNos}</span>
        </div>
        <div>
          <span>件数: </span>
          <span>{formatNumber(data.total)}</span>
        </div>
        <div>
          <span>売上額の合計: </span>
          <span>{formatNumber(data.totalSales)}</span>
        </div>
      </div>
      {/* Export Buttons */}
      <div className="flex gap-4 mb-6 text-[#6F6F6E] text-lg">
        {/* @ts-expect-error : type mismatch due to version node */}
        <CSVLink
          data={csvData}
          headers={csvHeaders}
          filename={`deposit-detail-${transferDate}.csv`}
          enclosingCharacter=""
        >
          <Button variant="outline">
            CSV Download
          </Button>
        </CSVLink>

        <Button
          variant="outline"
          onClick={handleExportPDF}
        >
          PDF Download
        </Button>
      </div>

      {/* Detail Table */}
      <div className="overflow-x-auto">
        <Table className="w-full">
          <TableHeader>
            <TableRow className="border-b border-gray-200">
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">利用日</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">加盟店ID</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">加盟店名</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">取引種別</TableHead>
              <TableHead className="px-4 py-2 text-right font-normal text-[#6F6F6E] text-lg">売上額</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">振込日</TableHead>
              <TableHead className="px-4 py-2 text-left font-normal text-[#6F6F6E] text-lg">会員番号</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.data.map((item, index) => (
              <TableRow key={index} className="border-b border-gray-100 hover:bg-gray-50">
                <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.transactionDate}</TableCell>
                <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.merchantNo}</TableCell>
                <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.storeName}</TableCell>
                <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">
                  {`${mapTransactionType.get(item.transactionType)}${item.groupCodeName}`}
                </TableCell>
                <TableCell className="px-4 py-3 text-right text-[#6F6F6E] text-lg">
                  {formatNumber(item.salesAmount)}
                </TableCell>
                <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.paymentDate}</TableCell>
                <TableCell className="px-4 py-3 text-[#6F6F6E] text-lg">{item.memberId}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
