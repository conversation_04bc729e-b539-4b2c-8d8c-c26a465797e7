import React from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, PointElement, LineElement, registerables } from 'chart.js';
import { Chart } from 'react-chartjs-2';
import { Card, CardContent } from '@/components/ui/card';

// Register Chart.js components
ChartJS.register(...registerables);

interface SummaryChartProps {
    chartLabel: string[];
    lineData: number[];
    barData: number[];
    barLastYearData: number[];
}

const SummaryChart: React.FC<SummaryChartProps> = ({
    chartLabel,
    lineData,
    barData,
    barLastYearData
}) => {
    // Chart data configuration
    const chartData = {
        labels: chartLabel || [],
        datasets: [
            {
                type: "line" as const,
                backgroundColor: "#FF7F50",
                borderColor: "#FF7F50",
                borderWidth: 3,
                label: "売上件数",
                yAxisID: 'y',
                data: lineData || [],
                tension: 0, // 曲線的彎度，設 0 表示直線
                fill: false // 是否填滿色彩
            },
            {
                type: "bar" as const,
                backgroundColor: "rgba(245, 196, 39, 0.8)",
                borderColor: "rgba(245, 196, 39, 0.8)",
                borderWidth: 1,
                label: "昨年の売上金額",
                yAxisID: 'y1',
                data: barLastYearData || [] ,
                stack: 'Stack 0', // tách 2 cột
            },
            {
                type: "bar" as const,
                backgroundColor: "rgba(54, 162, 235, 1)",
                borderColor: "rgba(54, 162, 235, 1)",
                borderWidth: 1,
                label: "売上金額",
                yAxisID: 'y1',
                data: barData || [],
                stack: 'Stack 1',
            }
        ],
    };

    // Chart options
    const chartOptions: any = {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                gridLines: {
                    display: false
                },
                ticks: {
                    beginAtZero: true
                },
                stacked: true,
            },
            y:
            {
                position: 'right',
                ticks: {
                    callback: function (label) {
                        return Number(label).toLocaleString();
                    },
                    min: 0
                },
                grid: {
                    borderDash: [2, 10]
                },
                stacked: true
            },
            y1: {
                position: 'left',
                ticks: {
                    callback: function (label, index, labels) {
                        return Number(label).toLocaleString();
                    },
                    min: 0
                },
                grid: {
                    borderDash: [8, 4]
                },
                stacked: true
            }
        },
    };

    return (
        <Card className="mb-4 sm:mb-6 border-0 shadow-none">
            <CardContent className="p-0 pt-4 sm:pt-6">
                <div className="h-96">
                    <Chart type="bar" data={chartData} options={chartOptions} />
                </div>
            </CardContent>
        </Card>
    );
};

export default SummaryChart;
