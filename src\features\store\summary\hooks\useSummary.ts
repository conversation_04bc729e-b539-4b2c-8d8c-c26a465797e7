import { useQuery } from '@tanstack/react-query';
import { summaryService } from '@/features/store/summary/services/summaryService';
import { SummaryState } from '@/features/store/summary/types/summaryType';

export const useSummary = (merchantNo: string) => {
  const {
    data: response,
    error,
    refetch
  } = useQuery({
    queryKey: ['summary', merchantNo],
    queryFn: () => summaryService.getSummaryData(merchantNo),
    enabled: !!merchantNo,
  });

  const getTotalData = (data: any) => {
    if (!data) return [];
    let sumValue = 0;
    const result = [];
    for (let index = 0; index < 12; index++) {
      sumValue += (data.creditData[index] || 0) + (data.electronicData[index] || 0) + (data.qrData[index] || 0);
      result.push(sumValue);
      sumValue = 0;
    }
    return result;
  };
  

  // Transform the response data to match the expected format
  const summaryData: SummaryState = {
    chartLabel: response?.data?.chartLabel || [],
    creditData: response?.data?.creditData || [],
    electronicData: response?.data?.electronicData || [],
    qrData: response?.data?.qrData || [],
    lineData: response?.data?.lineData || [],
    totalData: getTotalData(response?.data),
    error: !!error
  };
  return {
    summaryData,
    refetch
  };
};
