import Box from "@/components/ui/box";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from "react-router-dom";
import { FormButtons } from "./ui/FormButtons";
import { ThreePartNumberInput, PrefectureSelect } from "./ui/AddressInputs";
import { STEP } from "@/constants/common.constant";
import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import useAddressHoujin from "../hooks/useAddressHoujin";
import { houjinSchema, type HoujinFormData } from "@/features/application-steps/schemas/houjin.schema";
import ConfirmDialog from "@/components/ConfirmDialog";
import { FormInput } from "./ui/Field";

const Houjin = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {
    // State để track khi user đã cố gắng validate form
    const [hasAttemptedValidation, setHasAttemptedValidation] = useState(false);
    const [show, setShow] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitted },
        trigger,
        setValue,
        getValues,
        watch
    } = useForm<HoujinFormData>({
        resolver: zodResolver(houjinSchema),
        mode: "onBlur",
        defaultValues: {
            agxCorporateName: "",
            agxCorporatePhoneticName: "",
            agxCorporateEnglishName: "",
            agxCorporateNumber: "",
            agxCorporatePostalCode: "",
            agxCorporatePrefecture: "北海道",
            agxCorporateAddress1: "",
            agxCorporatePhoneticAddress1: "",
            agxCorporateAddress2: "",
            agxCorporatePhoneticAddress2: "",
            agxCorporatePhoneNumber1: "",
            agxCorporatePhoneNumber2: "",
            agxCorporatePhoneNumber3: "",
            agxCorporateFaxNumber1: "",
            agxCorporateFaxNumber2: "",
            agxCorporateFaxNumber3: "",
        }
    });

    // Sử dụng useAddress hook
    const {
        // States
        agxCorporatePostalCode,
        stateErrorFaxNumber,
        agxCorporatePhoneNumber1,
        agxCorporatePhoneNumber2,
        agxCorporatePhoneNumber3,
        stateErrorPhoneNumber,
        agxCorporateFaxNumber1,
        agxCorporateFaxNumber2,
        agxCorporateFaxNumber3,
        isLoadingAddress,
        handleFindAddress,
        
        // Handlers
        handleChangeAgxCorporatePostalCode,
        handleSetDataAgxCorporatePostalCode,
        handleChangeAgxCorporatePhoneNumber1,
        handleChangeAgxCorporatePhoneNumber2,
        handleChangeAgxCorporatePhoneNumber3,
        handleSetDataAgxCorporatePhoneNumber,
        handleChangeAgxCorporateFaxNumber1,
        handleChangeAgxCorporateFaxNumber2,
        handleChangeAgxCorporateFaxNumber3,
        handleSetDataAgxCorporateFaxNumber,
    } = useAddressHoujin(agxMerchantParams);

    const onSubmit = async () => {
        // Set flag để hiển thị validation errors
        setHasAttemptedValidation(true);
        
        // Validate form trước khi tiến hành
        const isValid = await trigger();
        if (!isValid) {
            return;
        }
        
        setAgxMerchantParams({
            ...agxMerchantParams,
            ...getValues(),
        } as AgxMerchantParams);

        setStep(STEP.DAIHYOUSHA);
    };



    const handleSave = async () => {
        
        updateMerchant({ ...agxMerchantParams, ...getValues() });
        setShow(false);
    };

    const showConfirmDialog = async () => {
        // Set flag để hiển thị validation errors
        setHasAttemptedValidation(true);
        const isValid = await trigger();
        if (!isValid) {
            return false;
        }
        setShow(true);
    }

    useEffect(() => {
        if(agxMerchantParams) {
            setValue("agxCorporateName", agxMerchantParams.agxCorporateName || "");
            setValue("agxCorporatePhoneticName", agxMerchantParams.agxCorporatePhoneticName || "");
            setValue("agxCorporateEnglishName", agxMerchantParams.agxCorporateEnglishName || "");
            setValue("agxCorporateNumber", agxMerchantParams.agxCorporateNumber || "");
            setValue("agxCorporatePostalCode", agxMerchantParams.agxCorporatePostalCode || "");
            setValue("agxCorporatePrefecture", agxMerchantParams.agxCorporatePrefecture || "北海道");
            setValue("agxCorporateAddress1", agxMerchantParams.agxCorporateAddress1 || "");
            setValue("agxCorporateAddress2", agxMerchantParams.agxCorporateAddress2 || "");
            setValue("agxCorporatePhoneticAddress1", agxMerchantParams.agxCorporatePhoneticAddress1 || "");
            setValue("agxCorporatePhoneticAddress2", agxMerchantParams.agxCorporatePhoneticAddress2 || "");
            
            // Xử lý số điện thoại
            const phoneNumber = agxMerchantParams.agxCorporatePhoneNumber;
            if (phoneNumber && phoneNumber.includes("-")) {
                const phoneParts = phoneNumber.split("-");
                setValue("agxCorporatePhoneNumber1", phoneParts[0] || "");
                setValue("agxCorporatePhoneNumber2", phoneParts[1] || "");
                setValue("agxCorporatePhoneNumber3", phoneParts[2] || "");
            } else {
                setValue("agxCorporatePhoneNumber1", "");
                setValue("agxCorporatePhoneNumber2", "");
                setValue("agxCorporatePhoneNumber3", "");
            }
            
            // Xử lý số fax
            const faxNumber = agxMerchantParams.agxCorporateFaxNumber;
            if (faxNumber && faxNumber.includes("-")) {
                const faxParts = faxNumber.split("-");
                setValue("agxCorporateFaxNumber1", faxParts[0] || "");
                setValue("agxCorporateFaxNumber2", faxParts[1] || "");
                setValue("agxCorporateFaxNumber3", faxParts[2] || "");
            } else {
                setValue("agxCorporateFaxNumber1", "");
                setValue("agxCorporateFaxNumber2", "");
                setValue("agxCorporateFaxNumber3", "");
            }
        }   
    }, [agxMerchantParams, setValue]);

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box className="mx-auto mb-32 md:mb-32 lg:mb-32 mt-4 md:mt-6 lg:mt-[41px] space-y-4 md:space-y-6 lg:space-y-[41px]">
                    
                    <FormInput
                        label="法人名"
                        name="agxCorporateName"
                        placeholder="法人名（全角）"
                        register={register}
                        errors={errors}
                        required
                    />
                   
                    <FormInput
                        label="法人名（カナ)"
                        name="agxCorporatePhoneticName"
                        placeholder="ホウジンメイカナ（全角カナ）"
                        register={register}
                        errors={errors}
                        required
                    />

                    <FormInput
                        label="法人名（英字)"
                        name="agxCorporateEnglishName"
                        placeholder="Houjinmei English（半角英数字）"
                        register={register}
                        errors={errors}
                        required
                    />

                    {/* Corporate number with search link */}
                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 relative md:items-baseline items-start">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                            法人番号<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full md:w-[250px] lg:w-[388px] flex flex-col">
                            <input
                                {...register("agxCorporateNumber")}
                                maxLength={13}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="1234567890123"
                            />
                            <div className="min-h-[16px] md:min-h-[16px] md:min-h-[20px] mt-1">
                                {errors.agxCorporateNumber && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                                        {errors.agxCorporateNumber.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Link to="#" className="w-full md:flex-1 items-center text-xs md:text-sm lg:text-[1rem] text-[#1D9987] hover:underline underline mt-2 md:mt-0">
                            法人番号がご不明な場合はこちらから確認してください
                        </Link>
                    </Box>

                    <Box className="flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-10 md:items-baseline items-start relative">
                        <Label className="w-full md:w-[22.5%] text-base md:text-xl lg:text-[1.75rem] required text-[#6F6F6E] md:pt-[13px] lg:pt-[13px]">
                        郵便番号<span className="text-[#FF0000] text-base md:text-xl lg:text-[1.75rem] ml-1">*</span>
                        </Label>
                        <Box className="w-full md:w-[250px] lg:w-[388px] flex flex-col">
                            <input
                                data-pattern="^(\d{7}|\d{3}-\d{4})$"
                                {...register("agxCorporatePostalCode")}
                                onChange={handleChangeAgxCorporatePostalCode}
                                onBlur={() => handleSetDataAgxCorporatePostalCode(setValue, trigger)}
                                maxLength={8}
                                value={agxCorporatePostalCode}
                                className="w-full border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                placeholder="000-0000"
                            />
                            <div className="min-h-[16px] md:min-h-[16px] md:min-h-[20px] mt-1">
                                {errors.agxCorporatePostalCode && (
                                    <span className="block font-bold text-[rgba(255,0,0,1)] text-xs md:text-sm">
                                        {errors.agxCorporatePostalCode.message}
                                    </span>
                                )}
                            </div>
                        </Box>
                        <Box className="w-full md:flex-1">
                            <Button
                                className="bg-[#A5A4A6] h-[40px] md:h-[50px] lg:h-[66px] w-full md:w-[200px] lg:w-[323px] text-sm md:text-base lg:text-[1.75rem] hover:bg-[#1D9987] text-white px-4 md:px-6 lg:px-8 py-2 md:py-3 rounded-lg transition-colors shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed" 
                                onClick={() => handleFindAddress(setValue, trigger)}
                                disabled={isLoadingAddress || !agxCorporatePostalCode}
                                type="button"
                            >
                                {isLoadingAddress ? "検索中..." : "住所情報を入力"}
                            </Button>
                        </Box>
                    </Box>

                    {/* Select prefecture */}
                    <PrefectureSelect
                        label="都道府県"
                        required
                        value={watch("agxCorporatePrefecture")}
                        onChange={(e) => setValue("agxCorporatePrefecture", e.target.value)}
                        error={errors.agxCorporatePrefecture?.message}
                    />

                    <FormInput
                        label="市町村"
                        name="agxCorporateAddress1"
                        placeholder="〇〇市〇〇区〇〇（全角）"
                        register={register}
                        errors={errors}
                        required
                    />

                    <FormInput
                        label="市町村（カナ)"
                        name="agxCorporatePhoneticAddress1"
                        placeholder="マルマルシマルマルクマルマル（全角カナ）"
                        register={register}
                        errors={errors}
                        required
                    />

                    <FormInput
                        label="丁目番地建物名"
                        name="agxCorporateAddress2"
                        placeholder="１丁目２ー３（全角）"
                        register={register}
                        errors={errors}
                        required
                    />

                    <FormInput
                        label="丁目番地建物名（カナ)"
                        name="agxCorporatePhoneticAddress2"
                        placeholder="１チョウメ２ー３（全角カナ）"
                        register={register}
                        errors={errors}
                        required
                    />

                    {/* Phone number */}
                    <ThreePartNumberInput
                        label="電話番号"
                        required
                        value1={agxCorporatePhoneNumber1}
                        value2={agxCorporatePhoneNumber2}
                        value3={agxCorporatePhoneNumber3}
                        onChange1={handleChangeAgxCorporatePhoneNumber1}
                        onChange2={handleChangeAgxCorporatePhoneNumber2}
                        onChange3={handleChangeAgxCorporatePhoneNumber3}
                        onBlur={() => handleSetDataAgxCorporatePhoneNumber(
                            setValue,
                            trigger,
                        )}
                        error={errors.agxCorporatePhoneNumber1?.message || errors.agxCorporatePhoneNumber2?.message || errors.agxCorporatePhoneNumber3?.message}
                        showError={stateErrorPhoneNumber || isSubmitted || hasAttemptedValidation}
                    />

                    {/* FAX */}
                    <ThreePartNumberInput
                        label="FAX"
                        required={false}
                        value1={agxCorporateFaxNumber1}
                        value2={agxCorporateFaxNumber2}
                        value3={agxCorporateFaxNumber3}
                        onChange1={handleChangeAgxCorporateFaxNumber1}
                        onChange2={handleChangeAgxCorporateFaxNumber2}
                        onChange3={handleChangeAgxCorporateFaxNumber3}
                        onBlur={() => handleSetDataAgxCorporateFaxNumber(
                            setValue, 
                            trigger,
                        )}
                        error={errors.agxCorporateFaxNumber1?.message || errors.agxCorporateFaxNumber2?.message || errors.agxCorporateFaxNumber3?.message}
                        showError={stateErrorFaxNumber || isSubmitted || hasAttemptedValidation}
                    />

                    {/* Form buttons */}
                    <FormButtons
                        onSave={showConfirmDialog}
                        onNext={onSubmit}
                        isSubmitting={isUpdating}
                        showBackButton={false}
                    />
                </Box>  
            </form>
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                title="入力内容を一時保存します。"
                cancelLabel="戻る"
                confirmLabel="一時保存"
                confirmVariant="danger"
                onCancel={() => setShow(false)}
                onConfirm={handleSave}
            />
        </>
    )
}

export default Houjin;