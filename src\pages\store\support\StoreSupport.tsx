import React, { useState } from 'react';
import { CrepicoModal } from '@/features/app-sidebar/components/CrepicoModal';

const StoreSupport: React.FC = () => {
  const [showModal, setShowModal] = useState(false);

  const paygateLink = {
    id: 'paygate',
    label: 'PAYGATE',
    url: 'https://help-paygate.smaregi.jp/hc/'
  };

  const crepicoLink = {
    id: 'crepico',
    label: 'クレピコ',
    url: 'https://www.seiko-sol.co.jp/products/crepico/crepico_user/'
  };

  const handleCrepicoClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  return (
    <>
      <div className="wrapper-body py-24 px-8 md:py-32 md:px-10 lg:py-32 lg:px-20 xl:py-32 xl:px-28">
        <div className="grid grid-cols-1 xl:grid-cols-2 xl:gap-x-72 gap-y-20 items-center pr-20">
          {/* PAYGATE Section */}
          <div>
            <a
              id={paygateLink.id}
              href={paygateLink.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-full h-48 py-16 px-2 border-2 border-gray-400 rounded-lg hover:shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl"
            >
              {paygateLink.label}
            </a>
          </div>

          {/* CREPICO Section */}
          <div>
            <button
              id={crepicoLink.id}
              onClick={handleCrepicoClick}
              className="flex items-center justify-center w-full h-48 py-16 px-2 border-2 border-gray-400 rounded-lg hover:shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl"
            >
              {crepicoLink.label}
            </button>
          </div>
        </div>
      </div>

      {/* CREPICO Modal */}
      <CrepicoModal
        isOpen={showModal}
        onClose={handleModalClose}
        targetUrl={crepicoLink.url}
      />
    </>
  );
};

export default StoreSupport;
