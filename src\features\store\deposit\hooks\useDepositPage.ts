import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { depositService } from '@/features/store/deposit/services/depositService';

export const useDepositPage = (merchantNo: string) => {
  const [transferDate, setTransferDate] = useState<string>('');
  const [dlEnable, setDlEnable] = useState<boolean>(false);

  // Fetch available dates
  const {
    data: datesResponse,
    isLoading: datesLoading,
    error: datesError
  } = useQuery({
    queryKey: ['deposit-dates', merchantNo],
    queryFn: () => depositService.getDateFromAgxPaymentManagement(merchantNo),
    enabled: !!merchantNo
  });

  // Fetch deposit data for selected date
  const {
    data: depositResponse,
    isLoading: depositLoading,
    error: depositError,
    refetch: refetchDeposit
  } = useQuery({
    queryKey: ['deposit-data', merchantNo, transferDate],
    queryFn: () => depositService.getDepositData(merchantNo, transferDate),
    enabled: !!merchantNo && !!transferDate
  });

  // Set initial date when dates are loaded
  useEffect(() => {
    if (datesResponse?.data?.length > 0 && !transferDate) {
      const firstDate = datesResponse.data[0];
      setTransferDate(firstDate);
    }
  }, [datesResponse, transferDate]);

  // Enable download when deposit data is loaded
  useEffect(() => {
    setDlEnable(!!depositResponse?.data);
  }, [depositResponse]);

  const handleDateChange = (newDate: string) => {
    setTransferDate(newDate);
    setDlEnable(false);
  };

  return {
    // Data
    dates: datesResponse?.data || [],
    depositData: depositResponse?.data,
    transferDate,
    datesLoading,
    depositLoading,
    // Error states
    error: datesError || depositError,
    // Actions
    setTransferDate: handleDateChange,
    refetchDeposit,
    dlEnable
  };
};
