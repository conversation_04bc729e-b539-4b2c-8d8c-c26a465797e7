import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';
import SummaryPage from '@/features/store/summary/components/SummaryPage';

const SummaryPaygate: React.FC = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user?.agxMerchantNo || '';
  return (
    <SummaryPage agxMerchantNo={agxMerchantNo} type={STORE.PAYGATE} />
  );
};

export default SummaryPaygate;
