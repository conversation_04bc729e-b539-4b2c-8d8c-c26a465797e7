import { Card, CardContent } from "@/components/ui/card";
import { useCallback, useEffect, useState } from "react";
import { useQueryAgxFeeRate } from "../hooks/useQueryAgxFeeRate";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";

const moneyData = {
  money_Core_Crepico: 99800,
  money_Credit_Card_And_QRcode: 500,
  money_All_Crepico: 1500,
};

interface ChokipayInformationProps {
  merchantData: GetMerchantStatusResponse;
}

function ChokipayInformation({ merchantData }: ChokipayInformationProps) {
  const [initialFee, setInitialFee] = useState("");
  const [monthlyFee, setMonthlyFee] = useState("");

  const { data: feeRate } = useQueryAgxFeeRate({
    agxBusinessType: merchantData?.agxBusinessType,
  });
  const caclMoney = useCallback(
    (agxNumberOfTerminal, agxSettlementPackage1, agxSettlementPackage2) =>
      () => {
        const moneyCrepico =
          parseInt(agxNumberOfTerminal) *
          ((agxSettlementPackage1
            ? moneyData.money_Credit_Card_And_QRcode
            : 0) +
            (agxSettlementPackage2 ? moneyData.money_All_Crepico : 0));
        setInitialFee(
          (agxNumberOfTerminal * moneyData.money_Core_Crepico)
            .toLocaleString("ja-JP", { style: "currency", currency: "JPY" })
            .substring(1)
        );
        setMonthlyFee(
          moneyCrepico
            .toLocaleString("ja-JP", { style: "currency", currency: "JPY" })
            .substring(1)
        );
      },
    []
  );

  useEffect(() => {
    caclMoney(
      merchantData?.agxNumberOfTerminal,
      merchantData?.agxSettlementPackage1,
      merchantData?.agxSettlementPackage2
    )();
  }, []);
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <div className="col-span-3"></div>
          <div className="col-span-9">
            <div className="grid grid-cols-12 items-center gap-6">
              <Label className="col-span-4 text-left text-[20px]">
                ご利用になる端末<span className="float-right">:</span>
              </Label>
              <div className="col-span-8">
                <div className="w-full h-10 rounded-md flex items-center px-3">
                  クレピコ AT-M100
                </div>
              </div>
            </div>
            <div className="grid grid-cols-12 items-center gap-6">
              <Label className="col-span-4 text-left text-[20px]">
                端末台数<span className="float-right">:</span>
              </Label>
              <div className="col-span-8">
                <div className="w-full h-10 rounded-md flex items-center px-3">
                  {merchantData?.agxNumberOfTerminal ?? ""}台
                </div>
              </div>
            </div>
          </div>
        </div>

        <h4 className="col-span-12 text-lg font-bold mt-5">決済種別</h4>

        <div className="grid grid-cols-12 items-center gap-6">
          <div className="col-span-12">
            <table className="w-full border-none">
              <tbody>
                <tr className="border-b border-gray-300">
                  <th className="w-[22px]"></th>
                  <th className="w-[22px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[156px] text-center font-bold">
                    <span>ご利用状況</span>
                  </th>
                  <th className="w-[220px] text-center font-bold">
                    <span>月額費用(税抜)</span>
                  </th>
                  <th className="w-[166px] text-center font-bold">
                    <span>決済手数料率</span>
                  </th>
                </tr>
                <tr>
                  <td colSpan={3} className="w-[167px]">
                    <span>クレジットカード</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>◯</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>
                      {merchantData?.agxSettlementPackage1 ||
                      merchantData?.agxSettlementPackage2
                        ? 500
                        : ""}
                      円
                      <sup>
                        <span className="text-[11px]">*</span>
                      </sup>
                    </span>
                  </td>
                  <td className="w-[156px]"></td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>VISA・Mastercard</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>◯</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span>
                      <span id="span_credit_visa_rate">
                        {feeRate?.creditVisaRate}
                      </span>
                      <span className="text-[11px]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>JCB他</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>◯</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span>
                      <span id="span_credit_jcb_rate">
                        {feeRate?.creditJcbRate}
                      </span>
                      <span className="text-[11px]">%</span>
                      <sup>
                        <span className="text-[11px]">*</span>
                      </sup>
                    </span>
                  </td>
                </tr>
                <tr>
                  <td colSpan={3} className="w-[157px]">
                    <span>電子マネー</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>
                      {merchantData?.agxSettlementPackage2 ? (
                        <>
                          1000円
                          <sup>
                            <span className="text-[11px]">*</span>
                          </sup>
                        </>
                      ) : (
                        "-"
                      )}
                    </span>
                  </td>
                  <td className="w-[156px]"></td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>交通系</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_traffic_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_traffic_rate_display"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? ""
                          : "none",
                      }}
                    >
                      <span id="span_transportation_rate">
                        {feeRate?.transportationRate}
                      </span>
                      <span className="text-[11px]">%</span>
                    </span>
                    <span
                      id="settlement_traffic_rate_hiden"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? "none"
                          : "",
                      }}
                    >
                      -
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>nanaco</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_nanaco_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_nanaco_rate_display"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? ""
                          : "none",
                      }}
                    >
                      <span id="span_nanaco_rate">{feeRate?.nanacoRate}</span>
                      <span className="text-[11px]">%</span>
                    </span>
                    <span
                      id="settlement_nanaco_rate_hiden"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? "none"
                          : "",
                      }}
                    >
                      -
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>WAON</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_waon_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_waon_rate_display"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? ""
                          : "none",
                      }}
                    >
                      <span id="span_waon_rate">{feeRate?.waonRate}</span>
                      <span className="text-[11px]">%</span>
                    </span>
                    <span
                      id="settlement_waon_rate_hiden"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? "none"
                          : "",
                      }}
                    >
                      -
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>Edy</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_edy_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_edy_rate_display"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? ""
                          : "none",
                      }}
                    >
                      <span id="span_edy_rate">{feeRate?.edyRate}</span>
                      <span className="text-[11px]">%</span>
                    </span>
                    <span
                      id="settlement_edy_rate_hiden"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? "none"
                          : "",
                      }}
                    >
                      -
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>iD</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_aid_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_aid_rate_display"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? ""
                          : "none",
                      }}
                    >
                      <span id="span_id_rate">{feeRate?.idRate}</span>
                      <span className="text-[11px]">%</span>
                    </span>
                    <span
                      id="settlement_aid_rate_hiden"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? "none"
                          : "",
                      }}
                    >
                      -
                    </span>
                  </td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>QUICPay</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_QUICPay_display">
                      {merchantData?.agxSettlementPackage2 ? "◯" : "-"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_QUICPay_rate_display"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? ""
                          : "none",
                      }}
                    >
                      <span id="span_quicpay_rate">{feeRate?.quicpayRate}</span>
                      <span className="text-[11px]">%</span>
                    </span>
                    <span
                      id="settlement_QUICPay_rate_hiden"
                      style={{
                        display: merchantData?.agxSettlementPackage2
                          ? "none"
                          : "",
                      }}
                    >
                      -
                    </span>
                  </td>
                </tr>
                <tr>
                  <td colSpan={3} className="w-[157px]">
                    <span>QRコード決済</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_code_display">◯</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px]"></td>
                </tr>
                <tr>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>Bank Pay</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_bankpay_display">◯</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_bankpay_rate_display">
                      <span id="span_qr_bankpay_rate">
                        {feeRate?.qrBankpayRate}
                      </span>
                      <span className="text-[11px]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span>Bank Pay以外</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display">◯</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="text-[11px]">%</span>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div className="grid grid-cols-12 items-start gap-6">
          <div className="col-span-12">
            <div className="flex items-start h-5">
              <span className="text-lg mr-1">*</span>
              <p className="mt-0.5">
                1 月額費用発生月の直近3ヶ月にご利用がある場合、
                決済端末1台分の基本プラン月額費用は値引きされます。
              </p>
            </div>
            <div className="ml-4">
              詳細は月額費用の請求・領収書ページをご参照ください。
            </div>
            <div className="flex items-start h-5 mt-2">
              <span className="text-lg mr-1">*</span>
              <p className="mt-0.5">
                2
                JCB・AMEX・Diners・Discover・QUICPayは、株式会社ジェーシービーと既にご契約がある場合、
                既存の契約条件を引き継ぐことがあります。
              </p>
            </div>
            <div className="mt-2 text-red-500">
              【ご注意】 月額費用は、それぞれの決済種別において、
              決済事業者の審査 (約1-3ヶ月)
              後に端末上でご利用できる状態になった時から発生します。
              <br />
              (基本プラン月額費用以外は、ご利用有無にかかわらず、
              発生する手数料ですので、 ご注意ください。)
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default ChokipayInformation;
