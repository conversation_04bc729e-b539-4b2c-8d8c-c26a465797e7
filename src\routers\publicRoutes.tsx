import { lazy, ReactNode, Suspense } from "react"
import { AccountTypes, TypeStore } from "@/types/globalType"
import { Outlet } from "react-router-dom"
import { Layout } from "@/components/layout/layout-public/Layout"
import { Login } from "@/pages/auth/Login"
import MailRegister from "@/pages/auth/MailRegister"
import { RegisterAccount } from "@/pages/auth/RegisterAccount"
import Unauthorized from "@/pages/Unauthorized"
import NotFound from "@/pages/NotFound"

import ContactLayout from "@/components/layout/layout-contact/Layout"
import { LoadingSpinner } from "@/components/LoadingSpinner";

// eslint-disable-next-line
const DocumentPage = lazy(() => import("@/pages/document/Document"));
// eslint-disable-next-line
const DocumentCompletedPage = lazy(() => import("@/pages/document/DocumentCompleted"));
// eslint-disable-next-line
const ContactPage = lazy(() => import("@/pages/contact/Contact"));
// eslint-disable-next-line
const ContactCompletedPage = lazy(() => import("@/pages/contact/ContactCompleted"));
export interface RouteConfig {
  path: string
  element: ReactNode
  children?: RouteConfig[]
  requiredAccountType?: AccountTypes[]
  requiredTypeStore?: TypeStore[]
}

const publicRoutes: RouteConfig[] = [
  {
    path: "/",
    element: (
      <Layout>
        <Outlet />
      </Layout>
    ),
    children: [
      {
        path: "",
        element: <Login />,
      },
      {
        path: "login",
        element: <Login />,
      },
      {
        path: "signup/:type/:token",
        element: <RegisterAccount />,
      },
      {
        path: "mail/register",
        element: <MailRegister />,
      },
    ],
  },
  {
    path: "/",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Outlet />
      </Suspense>
    ),
    children: [
      {
        path: "document",
        element: (
          <ContactLayout>
            <DocumentPage />
          </ContactLayout>
        ),
  },
  {
        path: "document-completed/:type",
        element: (
          <ContactLayout>
            <DocumentCompletedPage />
          </ContactLayout>
        ),
  },
  {
        path: "contact",
        element: (
          <ContactLayout>
            <ContactPage />
          </ContactLayout>
        ),
  },
  {
        path: "contact-completed",
        element: (
          <ContactLayout>
            <ContactCompletedPage />
          </ContactLayout>
        ),
      },
    ],
  },
  {
    path: "/unauthorized",
    element: <Unauthorized />,
  },
  {
    path: "*",
    element: <NotFound />,
  },
];

export default publicRoutes
