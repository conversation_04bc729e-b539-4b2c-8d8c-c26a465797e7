import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { GetMerchantStatusResponse } from "@/features/status/types";

class MerchantStatusService{
    async getMerchantStatus(agxMerchantNo: string): Promise<GetMerchantStatusResponse> {
        const response = await apiService.get<{ data: GetMerchantStatusResponse }>(`${API_ENDPOINTS.MERCHANT.AGX_MERCHANT_USER(agxMerchantNo)}`);
        return response.data;
    }
}

export const merchantStatusService = new MerchantStatusService();
export default merchantStatusService; 