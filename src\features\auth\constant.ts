import { IBusinessType, IEntityType } from "./types";

export const BUSINESS_TYPES: IBusinessType[] = [
    { id: 283260000, label: 'クリニック' },
    { id: 283260001, label: '薬局' },
    { id: 283260002, label: '歯科' },
    { id: 283260003, label: '病院 (病床数 20-199床)' },
    { id: 283260004, label: '病院 (病床数 200床以上)' },
    { id: 283260005, label: '薬局 (年商100億円以上)' },
    { id: 283260008, label: '事業 (中小企業向け医療経営プログラム)' },
];

export const ENTITY_TYPES: IEntityType[] = [
    { id: 283260000, label: '法人' },
    { id: 283260001, label: '個人' },
];

export const BUSINESS_TYPE_LABELS: Record<number, string> = {
    283260000: 'クリニック',
    283260001: '薬局',
    283260002: '歯科',
    283260003: '病院 (病床数 20-199床)',
    283260004: '病院 (病床数 200床以上)',
    283260005: '薬局 (年商100億円以上)',
    283260008: '事業 (中小企業向け医療経営プログラム)',
};

export const ENTITY_TYPE_LABELS: Record<number, string> = {
    283260000: '法人',
    283260001: '個人',
};

export const ERROR_MESSAGES: Record<number, string> = {
    43: "Tokenの有効期限が切れました。",
    47: "アカウントが既に存在されましたので、再度ご確認お願い致します。",
    400: "リクエストの形式が正しくありません。",
    500: "サーバーエラーが発生されました。",
};