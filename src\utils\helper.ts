import { TERMINAL_COLOR } from "@/features/config/constant";


export function getTerminalColorName(code?: number): string {
    if (code == null) return '--';
    return TERMINAL_COLOR[code] ?? '--';
}

export function formatNumberJP(num: number | string): string {
    return Number(num).toLocaleString("ja-JP");
}

export const convertPhoneAndFax = (value: string) => {
    const result = [];
    if (value && value.length > 0) {
        const arraySplit = value.split("-");
        result.push(arraySplit[0]);
        result.push(arraySplit[1]);
        result.push(arraySplit[2]);
    } else {
        result.push('');
        result.push('');
        result.push('');
    }
    return result;
}

export const convertDate = (value: any) => {
    const result = [];
    if (value && value.length > 0) {
        const arraySplit = value.split("-");
        result.push(arraySplit[0]);
        result.push(arraySplit[1].startsWith('0') ? arraySplit[1].substring(1, 2) : arraySplit[1]);
        //result.push(arraySplit.length === 3 ? arraySplit[2].substring(0, 2) : '');
        if(arraySplit.length === 3){
            result.push(arraySplit[2].startsWith('0') ? arraySplit[2].substring(1, 2) : arraySplit[2]);
        }else{
            result.push('');
        }
    } else {
        result.push('');
        result.push('');
        result.push('');
    }
    return result;
}
