import { useToast } from '@/hooks/use-toast';
import { useMutation } from '@tanstack/react-query';
import { MailRegisterParams } from '../types';
import { mailRegisterService } from '../services/mailRegisterService';

export const useSendMail = () => {
    const { toast } = useToast();
    const sendMailMutation =  useMutation({
        mutationFn: async (data: MailRegisterParams) => {
              return await mailRegisterService.sendMail(data);
            },
        onSuccess: () => {
            toast({
                variant: "default",
                title: "メール送信が完了しました。",
                description: "",
            });
        },
        onError: (error: any) => {
            toast({
                variant: "destructive",
                title: "メール送信が失敗しました。",
                description: "",
            });
        }
    });
    return {
        sendMail: sendMailMutation.mutate,
        sendMailAsync: sendMailMutation.mutateAsync,
        isLoading: sendMailMutation.isPending,
        isError: sendMailMutation.isError,
        error: sendMailMutation.error,
        reset: sendMailMutation.reset,
    };
};