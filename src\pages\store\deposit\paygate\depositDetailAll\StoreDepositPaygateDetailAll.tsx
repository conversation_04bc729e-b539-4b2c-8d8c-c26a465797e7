import React from 'react';
import { useParams } from 'react-router-dom';
import { DepositDetailAllPage } from '@/features/store/deposit/components/DepositDetailAllPage';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';

const StoreDepositPaygateDetailAll: React.FC = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user?.agxMerchantNo || '';
  const { transferDate } = useParams<{ transferDate: string }>();
  return <DepositDetailAllPage agxMerchantNo={agxMerchantNo} transferDate={transferDate} />;
};

export default StoreDepositPaygateDetailAll;
