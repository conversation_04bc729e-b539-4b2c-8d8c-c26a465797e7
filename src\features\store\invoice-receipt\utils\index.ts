import axiosBase from 'axios';
// Helper function để decode base64
export const base64Decode = (str?: string | null): string => {
  if (!str) return '';
  try {
    return atob(str);
  } catch {
    return str; // Return original if decode fails
  }
};

export const axiosPDF = axiosBase.create({
    baseURL: '/',
    headers: {
        'Content-Type': 'application/x-font-ttf',
    },
    responseType: 'blob',
})