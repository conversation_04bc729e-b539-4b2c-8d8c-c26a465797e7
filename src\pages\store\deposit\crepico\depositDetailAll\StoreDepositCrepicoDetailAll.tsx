import React from 'react';
import { useParams } from 'react-router-dom';
import { DepositDetailAllPage } from '@/features/store/deposit/components/DepositDetailAllPage';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';

const StoreDepositCrepicoDetailAll: React.FC = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user?.memberType ? user.agxMerchantNo : user.agxNewMerchantNo || '';
  const { transferDate } = useParams<{ transferDate: string }>();
  return <DepositDetailAllPage agxMerchantNo={agxMerchantNo} transferDate={transferDate} />;
};

export default StoreDepositCrepicoDetailAll;
