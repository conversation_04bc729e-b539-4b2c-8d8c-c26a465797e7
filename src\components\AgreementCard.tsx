import { Card, CardContent } from "@/components/ui/card";

const AgreementCard = ({ data, isDataEmpty }: { data: { title: string, url: string, isDisplay: boolean }[], isDataEmpty: boolean }) => {

  const handleClickCard = (url: string) => {
    window.open(url, "_blank");
  }

  if (isDataEmpty) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-[#6F6F6E]">利用規約がありません。</h2>
      </div>
    )
  }

  return (
    <div className="py-28 px-24">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {data.filter((item) => item.isDisplay).map((item, index) => (
          <Card 
            key={index}
            className="cursor-pointer transition-all duration-200 hover:shadow-md border-2 border-gray-400"
            onClick={() => handleClickCard(item.url)}
          >
            <CardContent className="p-4 flex items-center justify-center min-h-[60px]">
              <h3 className="text-[20px] font-medium text-center text-gray-500">{item.title}</h3>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AgreementCard;