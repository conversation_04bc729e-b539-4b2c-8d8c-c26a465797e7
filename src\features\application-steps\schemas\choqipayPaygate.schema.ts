import { z } from "zod";

// Schema validation cho form choqipay paygate
export const choqipayPaygateSchema = z.object({
  agxNumberOfTerminal: z.number({
    required_error: "※必須項目です、値を入力してください。",
    invalid_type_error: "※半角数値で入力してください。"
  })
  .min(1,  "※1 ～ 10 の値を入力してください。")
  .max(10, "※1 ～ 10 の値を入力してください。"),
  agxSettlementCard: z.boolean().optional(),
  agxSettlementTraffic: z.boolean().optional(),
  agxSettlementNanaco: z.boolean().optional(),
  agxSettlementWaon: z.boolean().optional(),
  agxSettlementEdy: z.boolean().optional(),
  agxSettlementAid: z.boolean().optional(),
  agxSettlementQuicpay: z.boolean().optional(),
  agxSettlementQrCode: z.boolean().optional(),
  agxSettlementPackage1: z.boolean().optional(),
  agxSettlementPackage2: z.boolean().optional(),
});

export type ChoqipayPaygateFormData = z.infer<typeof choqipayPaygateSchema>; 