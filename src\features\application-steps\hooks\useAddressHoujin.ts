import { useEffect, useState } from "react";
import { adAddressService } from "../services/addressService";
import { AgxMerchantParams } from "../types";

interface UseAddressReturn {

    // States
    agxCorporatePostalCode: string;
    agxCorporatePhoneNumber1: string;
    agxCorporatePhoneNumber2: string;
    agxCorporatePhoneNumber3: string;
    stateErrorPhoneNumber: boolean;
    agxCorporateFaxNumber1: string;
    agxCorporateFaxNumber2: string;
    agxCorporateFaxNumber3: string;
    stateErrorFaxNumber: boolean;

    isLoadingAddress: boolean;
    handleFindAddress: (setValue: any, trigger: any) => Promise<void>;
    
    // Handlers
    handleChangeAgxCorporatePostalCode: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxCorporatePostalCode: (setValue: any, trigger: any) => void;
    handleChangeAgxCorporatePhoneNumber1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxCorporatePhoneNumber2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxCorporatePhoneNumber3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxCorporatePhoneNumber: (setValue: any, trigger: any) => void;
    handleChangeAgxCorporateFaxNumber1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxCorporateFaxNumber2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxCorporateFaxNumber3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxCorporateFaxNumber: (setValue: any, trigger: any) => void;

}

const useAddressHoujin = (agxMerchantParams: AgxMerchantParams): UseAddressReturn => {
    // States for handling postal code
    const [agxCorporatePostalCode, setAgxCorporatePostalCode] = useState('');
    
    // States for handling phone numbers
    const [agxCorporatePhoneNumber1, setAgxCorporatePhoneNumber1] = useState('');
    const [agxCorporatePhoneNumber2, setAgxCorporatePhoneNumber2] = useState('');
    const [agxCorporatePhoneNumber3, setAgxCorporatePhoneNumber3] = useState('');
    const [stateErrorPhoneNumber, setStateErrorPhoneNumber] = useState(false);
    
    // States for handling fax numbers
    const [agxCorporateFaxNumber1, setAgxCorporateFaxNumber1] = useState('');
    const [agxCorporateFaxNumber2, setAgxCorporateFaxNumber2] = useState('');
    const [agxCorporateFaxNumber3, setAgxCorporateFaxNumber3] = useState('');
    const [stateErrorFaxNumber, setStateErrorFaxNumber] = useState(false);

    const [isLoadingAddress, setIsLoadingAddress] = useState(false);

    const handleFindAddress = async (setValue: any, trigger: any) => {
        setIsLoadingAddress(true);
        try {
            const { data } = await adAddressService.getData(agxCorporatePostalCode);

            if (data) {
                setValue("agxCorporatePrefecture", data.kenName);
                setValue("agxCorporateAddress1", data.cityTownName);
                setValue("agxCorporatePhoneticAddress1", data.cityTownFuri);
                
                await trigger(["agxCorporatePrefecture", "agxCorporateAddress1", "agxCorporatePhoneticAddress1"]);
            } else {
                setValue("agxCorporatePrefecture", "");
                setValue("agxCorporateAddress1", "");
                setValue("agxCorporatePhoneticAddress1", "");
                
            }
        } catch (error) {
            setValue("agxCorporatePrefecture", "");
            setValue("agxCorporateAddress1", "");
            setValue("agxCorporatePhoneticAddress1", "");
            await trigger(["agxCorporatePrefecture", "agxCorporateAddress1", "agxCorporatePhoneticAddress1"]);
        } finally {
            setIsLoadingAddress(false);
        }
    };

    // Handler for postal code input
    const handleChangeAgxCorporatePostalCode = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        if (value.trim().includes('-')) {
            if (value.trim().length <= 8) {
                setAgxCorporatePostalCode(value);
            }
        } else {
            if (value.trim().length <= 7) {
                setAgxCorporatePostalCode(value);
            }
        }
    };

    const handleSetDataAgxCorporatePostalCode = (setValue: any, trigger: any) => {
        let value = agxCorporatePostalCode;
        if (agxCorporatePostalCode.trim().length > 3 && !agxCorporatePostalCode.trim().includes('-')) {
            value = agxCorporatePostalCode.substring(0, 3) + '-' + agxCorporatePostalCode.substring(3, agxCorporatePostalCode.trim().length);
        }
        setAgxCorporatePostalCode(value);
        setValue("agxCorporatePostalCode", value);
        trigger(["agxCorporatePostalCode"]);
    };

    // Handlers for phone numbers
    const handleChangeAgxCorporatePhoneNumber1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxCorporatePhoneNumber1(e.target.value);
    };
    
    const handleChangeAgxCorporatePhoneNumber2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxCorporatePhoneNumber2(e.target.value);
    };
    
    const handleChangeAgxCorporatePhoneNumber3 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxCorporatePhoneNumber3(e.target.value);
    };

    const handleSetDataAgxCorporatePhoneNumber = (setValue: any, trigger: any) => {
        setStateErrorPhoneNumber(true);
        setValue("agxCorporatePhoneNumber1", agxCorporatePhoneNumber1?.trim());
        setValue("agxCorporatePhoneNumber2", agxCorporatePhoneNumber2?.trim());
        setValue("agxCorporatePhoneNumber3", agxCorporatePhoneNumber3?.trim());
        setValue("agxCorporatePhoneNumber", `${agxCorporatePhoneNumber1?.trim()}-${agxCorporatePhoneNumber2?.trim()}-${agxCorporatePhoneNumber3?.trim()}`);
        trigger(["agxCorporatePhoneNumber1", "agxCorporatePhoneNumber2", "agxCorporatePhoneNumber3"]);
    };

    // Handlers for fax numbers  
    const handleChangeAgxCorporateFaxNumber1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxCorporateFaxNumber1(e.target.value.trim());
    };
    
    const handleChangeAgxCorporateFaxNumber2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxCorporateFaxNumber2(e.target.value.trim());
    };
    
    const handleChangeAgxCorporateFaxNumber3 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxCorporateFaxNumber3(e.target.value.trim());
    };

    const handleSetDataAgxCorporateFaxNumber = (setValue: any, trigger: any) => {
        setStateErrorFaxNumber(true);
        setValue("agxCorporateFaxNumber1", agxCorporateFaxNumber1.trim());
        setValue("agxCorporateFaxNumber2", agxCorporateFaxNumber2.trim());
        setValue("agxCorporateFaxNumber3", agxCorporateFaxNumber3.trim());
        setValue("agxCorporateFaxNumber", `${agxCorporateFaxNumber1.trim()}-${agxCorporateFaxNumber2.trim()}-${agxCorporateFaxNumber3.trim()}`);
        trigger(["agxCorporateFaxNumber1", "agxCorporateFaxNumber2", "agxCorporateFaxNumber3"]);
    };

    const setErrorStates = () => {
        setStateErrorPhoneNumber(true);
        setStateErrorFaxNumber(true);
    };

    useEffect(() => {
        if(agxMerchantParams) {
            setAgxCorporatePostalCode(agxMerchantParams?.agxCorporatePostalCode);
            setAgxCorporatePhoneNumber1(agxMerchantParams?.agxCorporatePhoneNumber?.split("-")[0]);
            setAgxCorporatePhoneNumber2(agxMerchantParams?.agxCorporatePhoneNumber?.split("-")[1]);
            setAgxCorporatePhoneNumber3(agxMerchantParams?.agxCorporatePhoneNumber?.split("-")[2]);
            setAgxCorporateFaxNumber1(agxMerchantParams?.agxCorporateFaxNumber?.split("-")[0]);
            setAgxCorporateFaxNumber2(agxMerchantParams?.agxCorporateFaxNumber?.split("-")[1]);
            setAgxCorporateFaxNumber3(agxMerchantParams?.agxCorporateFaxNumber?.split("-")[2]);
        }
    }, [agxMerchantParams]);

    return {
        // States
        agxCorporatePostalCode,
        agxCorporatePhoneNumber1,
        agxCorporatePhoneNumber2,
        agxCorporatePhoneNumber3,
        stateErrorPhoneNumber,
        agxCorporateFaxNumber1,
        agxCorporateFaxNumber2,
        agxCorporateFaxNumber3,
        stateErrorFaxNumber,
        isLoadingAddress,
        handleFindAddress,
        
        // Handlers
        handleChangeAgxCorporatePostalCode,
        handleSetDataAgxCorporatePostalCode,
        handleChangeAgxCorporatePhoneNumber1,
        handleChangeAgxCorporatePhoneNumber2,
        handleChangeAgxCorporatePhoneNumber3,
        handleSetDataAgxCorporatePhoneNumber,
        handleChangeAgxCorporateFaxNumber1,
        handleChangeAgxCorporateFaxNumber2,
        handleChangeAgxCorporateFaxNumber3,
        handleSetDataAgxCorporateFaxNumber,
    };
};

export default useAddressHoujin;
    