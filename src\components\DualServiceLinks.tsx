import React from 'react';

interface ServiceLink {
  id: string;
  label: string;
  url: string;
}

interface DualServiceLinksProps {
  paygateLink: ServiceLink;
  crepicoLink: ServiceLink;
  showCrepicoInfo?: boolean;
  crepicoInfo?: {
    username: string;
    password: string;
  };
}

const DualServiceLinks: React.FC<DualServiceLinksProps> = ({
  paygateLink,
  crepicoLink,
  showCrepicoInfo = false,
  crepicoInfo
}) => {
  return (
    <div className="wrapper-body py-24 px-8 md:py-32 md:px-10 lg:py-32 lg:px-20 xl:py-32 xl:px-28">
          <div className="grid grid-cols-1 xl:grid-cols-2 xl:gap-x-72 gap-y-20 items-center pr-20">
            {/* PAYGATE Section */}
            <div>
              <a
                id={paygateLink.id}
                href={paygateLink.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-full h-48 py-16 px-2 border-2 border-gray-400 rounded-lg hover:shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl"
              >
                {paygateLink.label}
              </a>
            </div>

            {/* CREPICO Section */}
            <div>
              <a
                id={crepicoLink.id}
                href={crepicoLink.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-full h-48 py-16 px-2 border-2 border-gray-400 rounded-lg  hover:shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl"
              >
                {crepicoLink.label}
              </a>
            </div>
          </div>

          {/* CREPICO Support Information - Aligned with CREPICO column */}
          {showCrepicoInfo && crepicoInfo && (
            <div className="grid grid-cols-1 py-8 xl:grid-cols-2 xl:gap-x-72 gap-y-20 items-center px-20">
              {/* Empty space for PAYGATE column */}
              <div></div>
              {/* CREPICO Support Information */}
              <div className="text-left text-[#6F6F6E] text-xl">
                <div className="pb-7">
                  <span>※CREPICOサポートサイトへアクセスします。</span><br />
                  <span className='pl-6'>ユーザー名、パスワードを求められた際は</span><br />
                  <span className='pl-6'>下記の情報を入力してください。</span>
                </div>
                <div className="flex pb-2 pl-6">
                  <span>ユーザー名(ID)</span>
                  <span className="pr-2">:</span>
                  <span>{crepicoInfo.username}</span>
                </div>
                <div className="flex pl-6">
                  <span>パスワード</span>
                  <span className="pr-2">:</span>
                  <span>{crepicoInfo.password}</span>
                </div>
              </div>
            </div>
          )}
    </div>
  );
};

export default DualServiceLinks;
