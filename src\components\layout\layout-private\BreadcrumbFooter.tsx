import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import { useBreadcrumb } from '@/features/breadcrumb-footer/hooks/useBreadcrumb';
import { BreadcrumbItem } from '@/features/breadcrumb-footer/utils/breadcrumbUtils';

export const BreadcrumbFooter: React.FC = () => {
  const { breadcrumbs, shouldDisplay } = useBreadcrumb();
  
  if (!shouldDisplay || breadcrumbs.length === 0) {
    return null;
  }
  
  return (
    <div className="px-10 py-2">
      <nav className="flex items-center space-x-2" aria-label="Breadcrumb">
        {breadcrumbs.map((item: BreadcrumbItem, index: number) => (
          <React.Fragment key={item.path}>
            {index > 0 && (
              <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0" aria-hidden="true" />
            )}
            {item.isClickable ? (
              <Link
                to={item.path}
                className=" text-[16px] lg:text-[18px] text-gray-500 hover:text-gray-700 hover:underline transition-colors duration-200 truncate"
                aria-current={item.isActive ? 'page' : undefined}
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-[16px] lg:text-[18px] text-[#6F6F6E] font-medium truncate" aria-current="page">
                {item.label}
              </span>
            )}
          </React.Fragment>
        ))}
      </nav>
    </div>
  );
}; 