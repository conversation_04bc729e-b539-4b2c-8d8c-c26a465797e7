import React from 'react';

interface ProgressBarProps {
  progress: number;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ progress, className = '' }) => {
  return (
    <div className={`bg-white border mt-[50px] rounded-[11px] border-[#707070] border-solid w-full max-w-full max-md:mt-10 ${className}`}>
      <div className="rounded-[11px] h-[20px] overflow-hidden">
        <div 
          className="bg-[rgba(26,164,146,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] h-full rounded-[11px] transition-all duration-300 ease-in-out"
          style={{ width: `${Math.max(Math.min(progress, 100), 0)}%` }}
        />
      </div>
    </div>
  );
};