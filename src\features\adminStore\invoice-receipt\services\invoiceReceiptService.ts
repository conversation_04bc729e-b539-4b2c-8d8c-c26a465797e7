import apiService from "@/services/api";
import { API_ENDPOINTS } from "@/config/api-endpoints";

import { CreditCardMonthlyFeeResponse, InvoiceMonthlyResponse, InvoiceReceiptResponse, MonthlyCostByStoreResponse, ReceiptMonthlyResponse } from "@/features/adminStore/invoice-receipt/types";

interface AgxInvoiceDetailResponse {
  data: {
    deadlineDate: string;
    invoiceStatus: number;
    invoiceUrl: string;
    receiptUrl: string;
    total: number;
    agxStatus?: number;
  };
}

interface GmoInfoResponse {
  data: {
    memberId: string | null;
    cardNo: string | null;
  };
}

interface GmoPaymentResponse {
  data: {
    LinkUrl: string;
  };
}

class InvoiceService {
  async getDataInvoiceReceipt(merchantNo: string): Promise<InvoiceReceiptResponse> {
    const response = await apiService.get(API_ENDPOINTS.ADMIN_INVOICE_RECEIPT.GET_DATA_INVOICE_RECEIPT(btoa(merchantNo))) as InvoiceReceiptResponse;
    return response;
  }

  async getDataInvoiceMonthly(merchantNo: string, yearMonth: string): Promise<InvoiceMonthlyResponse> {
    const response = await apiService.get(API_ENDPOINTS.ADMIN_INVOICE_RECEIPT.GET_DATA_INVOICE_MONTHLY(btoa(merchantNo), yearMonth)) as InvoiceMonthlyResponse;
    return response;
  }

  async getDataReceiptMonthly(merchantNo: string, yearMonth: string): Promise<ReceiptMonthlyResponse> {
    const response = await apiService.get(API_ENDPOINTS.ADMIN_INVOICE_RECEIPT.GET_DATA_RECEIPT_MONTHLY(btoa(merchantNo), yearMonth)) as ReceiptMonthlyResponse;
    return response;
  }

  async getDataCreditCardMonthlyFee(merchantNo: string, yearMonth: string, type: string): Promise<CreditCardMonthlyFeeResponse> {
    const response = await apiService.get(API_ENDPOINTS.ADMIN_INVOICE_RECEIPT.GET_DATA_CREDIT_CARD_MONTHLY_FEE(btoa(merchantNo), yearMonth, type)) as CreditCardMonthlyFeeResponse;
    return response;
  }

  async getDataMonthlyCostByStore(merchantNo: string, yearMonth: string): Promise<MonthlyCostByStoreResponse> {
    const response = await apiService.get(API_ENDPOINTS.ADMIN_INVOICE_RECEIPT.GET_DATA_MONTHLY_COST_BY_STORE(merchantNo, yearMonth)) as MonthlyCostByStoreResponse;
    return response;
  }

  // Lấy thông tin GMO member
  async getGmoInfo(merchantNo: string): Promise<GmoInfoResponse> {
    const response = await apiService.get(API_ENDPOINTS.INVOICE_RECEIPT.GET_GMO_INFO(merchantNo)) as GmoInfoResponse;
    return response;
  }

  // Lấy URL thanh toán GMO cho registration
  async getGmoPaymentUrl(merchantNo: string): Promise<GmoPaymentResponse> {
    const response = await apiService.post(API_ENDPOINTS.INVOICE_RECEIPT.GET_GMO_PAYMENT_URL(merchantNo)) as GmoPaymentResponse;
    return response;
  }

  // Lấy URL chỉnh sửa thẻ tín dụng GMO
  async getGmoLinkplusUrl(memberId: string, cardNo: string): Promise<GmoPaymentResponse> {
    const response = await apiService.post(API_ENDPOINTS.INVOICE_RECEIPT.GET_GMO_LINK_PLUS_URL(memberId, cardNo)) as GmoPaymentResponse;
    return response;
  }
}

export const invoiceService = new InvoiceService();