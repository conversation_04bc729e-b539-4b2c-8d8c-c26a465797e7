import pdfMake from 'pdfmake/build/pdfmake';
import axios from 'axios';

// Convert blob to base64
async function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = function () {
      const result = this.result as string;
      const base64 = result.split('base64,')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

export interface PDFConfig {
  pageSize?: string | { width: number; height: number };
  pageOrientation?: 'portrait' | 'landscape';
  defaultStyle?: any;
  background?: (currentPage: number) => any;
  styles?: any;
  content: any[];
  filename: string;
}

export { blobToBase64 };

export class PDFService {
  private static fontLoaded = false;
  private static fontCache: { [key: string]: string } = {};

  /**
   * Load Japanese font for PDF generation
   * Caches the font to avoid multiple downloads
   */
  static async loadJapaneseFont(): Promise<boolean> {
    if (this.fontLoaded) {
      return true;
    }

    try {
      // Try to load from cache first
      if (this.fontCache['ipaexg']) {
        this.setupFont(this.fontCache['ipaexg']);
        this.fontLoaded = true;
        return true;
      }

      const response = await axios.get('/ipaexg.ttf', { responseType: 'blob' });
      if (response.status !== 200) {
        throw new Error(`Failed to load font: HTTP ${response.status}`);
      }

      const font = await blobToBase64(response.data);
      this.fontCache['ipaexg'] = font;
      this.setupFont(font);
      this.fontLoaded = true;
      return true;
    } catch (error) {
      console.error('Failed to load Japanese font:', error);
      return false;
    }
  }

  /**
   * Setup font configuration for pdfMake
   */
  static setupFont(font: string): void {
    pdfMake.vfs = { "ipaexg.ttf": font };
    pdfMake.fonts = {
      IPAEXGothic: {
        normal: 'ipaexg.ttf',
        bold: 'ipaexg.ttf',
        italics: 'ipaexg.ttf',
        bolditalics: 'ipaexg.ttf'
      },
      Roboto: {
        normal: 'ipaexg.ttf',
        bold: 'ipaexg.ttf',
        italics: 'ipaexg.ttf',
        bolditalics: 'ipaexg.ttf'
      }
    };
  }

  /**
   * Setup multiple fonts for pdfMake
   */
  static setupMultipleFonts(yugothib: string, arial: string): void {
    pdfMake.vfs = { "yugothib.ttf": yugothib, "arial.ttf": arial };
    pdfMake.fonts = {
      yugothib: {
        normal: 'yugothib.ttf',
        bold: 'yugothib.ttf',
        italics: 'yugothib.ttf',
        bolditalics: 'yugothib.ttf'
      },
      arial: {
        normal: 'arial.ttf',
        bold: "arial.ttf",
        italics: 'arial.ttf',
        bolditalics: 'arial.ttf'
      },
    };
  }

  /**
   * Create PDF with pre-setup fonts (does not override existing font configuration)
   */
  static async createPDFWithSetupFonts(config: PDFConfig): Promise<void> {
    try {
      const docDefinition: any = {
        pageSize: config.pageSize || 'A4',
        pageOrientation: config.pageOrientation || 'portrait',
        content: config.content
      };

      // Add background if provided
      if (config.background) {
        docDefinition.background = config.background;
      }

      // Add styles if provided
      if (config.styles) {
        docDefinition.styles = config.styles;
      }

      // Use the provided default style without overriding fonts
      if (config.defaultStyle) {
        docDefinition.defaultStyle = config.defaultStyle;
      }

      // Add small delay to ensure fonts are properly registered
      await new Promise(resolve => setTimeout(resolve, 200));

      pdfMake.createPdf(docDefinition).download(config.filename);
    } catch (error) {
      console.error('PDF creation failed:', error);
      throw new Error('PDF作成に失敗しました。');
    }
  }

  /**
   * Create and download PDF with Japanese font support
   */
  static async createPDF(config: PDFConfig): Promise<void> {
    try {
      // Check if fonts are already configured (to avoid overriding setupMultipleFonts)
      if (pdfMake.fonts && Object.keys(pdfMake.fonts).length > 0) {
        // Fonts already configured, use createPDFWithSetupFonts instead
        return this.createPDFWithSetupFonts(config);
      }

      const fontLoaded = await this.loadJapaneseFont();

      const docDefinition: any = {
        pageSize: config.pageSize || 'A4',
        pageOrientation: config.pageOrientation || 'portrait',
        content: config.content
      };

      // Add background if provided
      if (config.background) {
        docDefinition.background = config.background;
      }

      // Add styles if provided
      if (config.styles) {
        docDefinition.styles = config.styles;
      }

      // Setup font and default style based on font loading status
      if (fontLoaded) {
        docDefinition.fonts = {
          IPAEXGothic: {
            normal: 'ipaexg.ttf',
            bold: 'ipaexg.ttf',
            italics: 'ipaexg.ttf',
            bolditalics: 'ipaexg.ttf'
          }
        };
        docDefinition.defaultStyle = {
          font: 'IPAEXGothic',
          ...config.defaultStyle
        };
        // Add small delay to ensure font is properly registered
        await new Promise(resolve => setTimeout(resolve, 100));
      } else {
        // Fallback to default font if Japanese font fails to load
        docDefinition.defaultStyle = config.defaultStyle || {};
      }

      pdfMake.createPdf(docDefinition).download(config.filename);
    } catch (error) {
      console.error('PDF creation failed:', error);
      // Try to create PDF without custom fonts as fallback
      try {
        const fallbackDefinition: any = {
          pageSize: config.pageSize || 'A4',
          pageOrientation: config.pageOrientation || 'portrait',
          defaultStyle: config.defaultStyle || {},
          content: config.content
        };
        
        if (config.background) {
          fallbackDefinition.background = config.background;
        }
        
        if (config.styles) {
          fallbackDefinition.styles = config.styles;
        }
        pdfMake.createPdf(fallbackDefinition).download(config.filename);
      } catch (fallbackError) {
        console.error('Fallback PDF creation also failed:', fallbackError);
        throw new Error('PDF作成に失敗しました。');
      }
    }
  }

  /**
   * Create standard deposit PDF header
   */
  static createDepositHeader(
    title: string,
    merchantNo: string,
    storeName: string,
    transferDate: string,
    dateTitle: string
  ): any[] {
    return [
      {
        text: dateTitle,
        alignment: 'right',
        margin: [0, 0, 0, 5],
        fontSize: 10
      },
      {
        text: title,
        fontSize: 22,
        alignment: 'center',
        margin: [0, 0, 0, 20]
      },
      {
        margin: [0, 0, 0, 20],
        columns: [
          {
            width: '*',
            stack: [
              { text: `加盟店ID: ${merchantNo}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `店舗名: ${storeName}`, margin: [0, 0, 0, 3], fontSize: 11 },
              { text: `振込日: ${transferDate}`, margin: [0, 0, 0, 3], fontSize: 11 }
            ]
          }
        ]
      }
    ];
  }

  /**
   * Create standard table structure
   */
  static createTable(
    headers: string[],
    data: any[][],
    widths?: (string | number)[],
    fontSize: number = 8
  ): any {
    return {
      table: {
        widths: widths || headers.map(() => '*'),
        body: [headers, ...data]
      },
      fontSize,
      layout: 'lightHorizontalLines'
    };
  }

  /**
   * Reset font cache (useful for testing or memory management)
   */
  static resetFontCache(): void {
    this.fontLoaded = false;
    this.fontCache = {};
  }
}

export default PDFService;
