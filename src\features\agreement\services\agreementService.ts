import apiService from '@/services/api';
import { API_ENDPOINTS } from '@/config/api-endpoints';
import { AgreementResponse } from '@/features/agreement/type';

class AgreementService {
  /**
   * Get paygate data for a merchant
   * @param merchantNo - Merchant number
   * @returns Paygate data
   */
  async getData(merchantNo: string): Promise<AgreementResponse> {
    const response = await apiService.get<AgreementResponse>(API_ENDPOINTS.AGREEMENT.GET_DATA(merchantNo));
    return response;
  }
}

export const agreementService = new AgreementService();
export default agreementService;
