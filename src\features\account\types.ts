import { emailSchema, nameSchema, passwordSchema } from "./schema";
import * as z from "zod";

export enum EditMode {
    None = "none",
    Name = "name",
    Email = "email",
    Password = "password",
}

export type PasswordFormData = z.infer<typeof passwordSchema>;
export type NameFormData = z.infer<typeof nameSchema>;
export type EmailFormData = z.infer<typeof emailSchema>;

export type ChangeInfoRequest= {
    contactId: string;
    firstName: string;
    lastName: string;
    email: string;
}

export type ChangePasswordRequest = {
    contactId: string;
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
}

export type ChangeInfoResponse = {
    contactId: string
    firstName: string
    lastName: string
    fullName: any
    email: string
}

export type ChangePasswordResponse = {
    status:     number;
    message:    string;
    statusCode: number;
    timestamp:  Date;
}