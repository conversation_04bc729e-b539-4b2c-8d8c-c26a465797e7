import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { ReceiptData, ReceiptResponse } from "@/features/store/invoice-receipt/types";

class ReceiptService {
    async getData(invoiceNo: string): Promise<ReceiptData> {
        const response = await apiService.get<ReceiptResponse>(API_ENDPOINTS.INVOICE_RECEIPT.GET_DATA(invoiceNo)) as ReceiptResponse;
        return response.data;
    }
}

export const receiptService = new ReceiptService();