import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DialogDescription } from '@radix-ui/react-dialog';

const CREPICO_INFO = {
  username: 'cp-user',
  password: 'cp-user1'
} as const;

interface CrepicoModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetUrl: string;
}

export const CrepicoModal: React.FC<CrepicoModalProps> = React.memo(({
  isOpen,
  onClose,
  targetUrl
}) => {
  const handleAccessClick = () => {
    window.open(targetUrl, '_blank', 'noopener,noreferrer');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="p-6 lg:rounded-[25px] rounded-[25px] bg-white w-[450px] h-auto [&>button]:hidden">
        <DialogHeader className="flex items-center justify-center flex-1">
          <DialogTitle className="text-[20px] text-[#6F6F6E] font-normal text-center leading-relaxed mb-4">
            CREPICOサポートサイトへアクセスします。<br />
            ユーザー名、パスワードを求められた際は<br />
            下記情報を入力してください。
          </DialogTitle>
          <DialogDescription></DialogDescription>
          
          <div className="text-left space-y-2 mb-6">
            <div className="flex">
              <span className="text-[#6F6F6E]">ユーザー名(ID)</span>
              <span className="mx-2 text-[#6F6F6E]">:</span>
              <span className="text-[#6F6F6E] font-medium">{CREPICO_INFO.username}</span>
            </div>
            <div className="flex">
              <span className="text-[#6F6F6E]">パスワード</span>
              <span className="mx-2 text-[#6F6F6E]">:</span>
              <span className="text-[#6F6F6E] font-medium">{CREPICO_INFO.password}</span>
            </div>
          </div>
        </DialogHeader>
        
        <DialogFooter className="!flex !flex-row !gap-3 !justify-center !items-center !w-full sm:!justify-center">
          <Button
            variant="secondary"
            onClick={onClose}
            className="bg-gray-400 hover:bg-gray-500 text-white rounded-xl text-[16px] font-[400]"
            style={{ width: '122px', height: '42px' }}
          >
            戻る
          </Button>
          <Button
            onClick={handleAccessClick}
            className="bg-[#c94e4e] hover:bg-[#c93838] text-white rounded-xl text-[16px] font-[400]"
            style={{ width: '122px', height: '42px' }}
          >
            アクセス
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});

CrepicoModal.displayName = 'CrepicoModal';
