import { Card, CardContent } from "@/components/ui/card";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { convertPhoneAndFax } from "@/utils/helper";

interface StoreInformationProps {
  merchantData: GetMerchantStatusResponse;
}

function StoreInformation({ merchantData }: StoreInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            店舗名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            フリガナ<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            英語表記名称<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreEnglishName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            WEBサイトURL
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxUrl ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            ブランド名
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxBrandName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">営業日</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxBusinessDate ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">定休日</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxRegularHoliday ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">営業時間</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxBusinesssHours ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            郵便番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePostalCode ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            都道府県<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePrefecture ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所１<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所1（フリガナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePhoneticAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">住所2</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStoreAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所2（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md px-3 flex items-center">
              {merchantData?.agxStorePhoneticAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            電話番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-16 h-10 rounded-md px-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[0] || ""}
            </div>
            <span>-</span>
            <div className="w-16 h-10 rounded-md px-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[1] || ""}
            </div>
            <span>-</span>
            <div className="w-16 h-10 rounded-md px-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStorePhoneNumber)[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">FAX番号</Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-16 h-10 rounded-md px-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[0] || ""}
            </div>
            <span>-</span>
            <div className="w-16 h-10 rounded-md px-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[1] || ""}
            </div>
            <span>-</span>
            <div className="w-16 h-10 rounded-md px-3 flex items-center">
              {convertPhoneAndFax(merchantData?.agxStoreFaxNumber)[2] || ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default StoreInformation;
