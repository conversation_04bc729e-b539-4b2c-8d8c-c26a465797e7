pipeline {  
    agent any
    tools {
        nodejs 'node_24'
    }
    stages {
        stage('checkout') {
            steps {
                checkout scm
            }
        }
        stage('install') {
            steps {
                sh 'npm i'
            }
        }
        stage('build') {
            steps {
                sh 'npm run build:stg'
                sh 'sudo chmod -R 777 /var/lib/jenkins/workspace/choqipay-new/dist'
            }
        }
    }
}

