export interface GetMerchantStatusResponse {
    agxMerchantNo:                       string;
    agxCorporateName:                    string;
    agxMerchantid:                       string;
    agxContactId:                        string;
    agxBankPhoneticName:                 string;
    agxSettlementEdy:                    boolean;
    agxSettlementAid:                    boolean;
    agxSettlementJcb:                    boolean;
    agxSettlementNanaco:                 boolean;
    agxPcidssExpectedComplianceDate:     Date;
    agxPcidssStatus:                     number;
    agxSettlementQrCode:                 boolean;
    agxSettlementQuicpay:                boolean;
    agxSettlementWaon:                   boolean;
    agxUrl:                              null;
    agxCardInformationRetentionStatus:   number;
    agxNoRetainingCardInfoDate:          Date;
    agxSettlementCard:                   boolean;
    agxSecurityCodeCheckStatus:          number;
    agxSecurityCodeCheckDate:            Date;
    agxOtherMeasuresStatus:              number;
    agxOtherMeasuresDate:                Date;
    agxBrandName:                        null;
    agxIllegalDeliveryDestinationStatus: number;
    agxIllegalDeliveryDestinationDate:   Date;
    agxSettlementTraffic:                boolean;
    agxRepresentativeFaxNumber:          null;
    agxRepresentativeAddress1:           string;
    agxRepresentativePhoneticAddress1:   string;
    agxRepresentativeAddress2:           null;
    agxRepresentativePhoneticAddress2:   null;
    agxRepresentativeName:               string;
    agxRepresentativePhoneticName:       string;
    agxRepresentativeGender:             number;
    agxRepresentativeAddressCopyFlag:    boolean;
    agxRepresentativeBirthday:           Date;
    agxRepresentativePostalCode:         string;
    agxRepresentativePrefecture:         string;
    agxRepresentativePhoneticPrefecture: string;
    agxRepresentativePhoneNumber:        string;
    agxAccountHolder:                    string;
    agxAccountNo:                        string;
    agxBusinessDate:                     null;
    agxBusinesssHours:                   null;
    agxRegularHoliday:                   null;
    agxOtherMeasuresDescription:         null;
    agxBehaviorAnalysisStatus:           number;
    agxBehaviorAnalysisDate:             Date;
    agxStoreFaxNumber:                   null;
    agxStoreAddress1:                    string;
    agxStorePhoneticAddress1:            string;
    agxStoreAddress2:                    null;
    agxStorePhoneticAddress2:            null;
    agxStoreName:                        string;
    agxStorePhoneticName:                string;
    agxStoreAddressCopyFlag1:            boolean;
    agxStoreAddressCopyFlag2:            boolean;
    agxStorePostalCode:                  string;
    agxStorePrefecture:                  string;
    agxStorePhoneticPrefecture:          string;
    agxStorePhoneNumber:                 string;
    agxNumberOfEmployees:                number;
    agxContactEmail:                     string;
    agxContactName:                      string;
    agxContactPhoneticName:              string;
    agxContactPhoneNumber:               string;
    agxBranchName:                       string;
    agxBranchPhoneticName:               string;
    agxBranchNo:                         string;
    agxBranchType:                       number;
    agxMonthlySales:                     number;
    agxThreeDSecureStatus:               number;
    agxThreeDSecureDate:                 Date;
    agxBusinessOpportunityRelatedSales:  boolean;
    agxCorporateFaxNumber:               null;
    agxCorporateAddress1:                string;
    agxCorporatePhoneticAddress1:        string;
    agxCorporateAddress2:                null;
    agxCorporatePhoneticAddress2:        null;
    agxCorporatePhoneticName:            string;
    agxCorporateEnglishName:             string;
    agxCorporateNumber:                  string;
    agxCorporatePostalCode:              string;
    agxCorporatePrefecture:              string;
    agxCorporatePhoneticPrefecture:      string;
    agxCorporatePhoneNumber:             string;
    agxSpecifiedContinuousServices:      boolean;
    agxApplicationStatus:                number;
    agxColorOfTerminal:                  number;
    agxNumberOfTerminal:                 number;
    agxStoreEnglishName:                 string;
    agxDoorToDoorSales:                  boolean;
    agxFoundingDate:                     Date;
    agxCapital:                          number;
    agxPyramidScheme:                    boolean;
    agxBankName:                         string;
    agxBankNo:                           string;
    agxBankType:                         number;
    agxTelemarketingSales:               boolean;
    agxAccountType:                      number;
    agxBusinessType:                     number;
    agxBusinessForm:                     number;
    agxMedicalInstitutionCode:           string;
    agxEmsEmployeeNo:                    string;
    agxMunMerchantNo:                    null;
    agxStoreBranchNo:                    null;
    agxJcbExistsMembershipFlag:          null;
    agxJcbMerchantNumber:                null;
    memberType:                          boolean;
    agxSettlementPackage1:               boolean;
    agxSettlementPackage2:               boolean;
    agxNewTerminalNo1:                   null;
    agxNewTerminalNo2:                   null;
    agxNewTerminalNo3:                   null;
    agxNewTerminalNo4:                   null;
    agxNewTerminalNo5:                   null;
    agxNewTerminalNo6:                   null;
    agxNewTerminalNo7:                   null;
    agxNewTerminalNo8:                   null;
    agxNewTerminalNo9:                   null;
    agxNewTerminalNo10:                  null;
}

export interface GetAgxFeeRateResponse {
    qrOtherBankpayRate: string;
    creditVisaRate:     string;
    quicpayRate:        string;
    edyRate:            string;
    creditJcbRate:      string;
    transportationRate: string;
    creditUnionRate:    string;
    qrBankpayRate:      string;
    waonRate:           string;
    idRate:             string;
    nanacoRate:         string;
}