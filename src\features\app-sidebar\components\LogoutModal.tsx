import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DialogDescription } from '@radix-ui/react-dialog';

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const LogoutModal: React.FC<LogoutModalProps> = React.memo(({
  isOpen,
  onClose,
  onConfirm
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="p-6 lg:rounded-[25px] rounded-[25px] bg-white w-[372px] h-[315px] [&>button]:hidden">
        <DialogHeader className="flex items-center justify-center flex-1">
          <DialogTitle className="text-[20px] text-[#6F6F6E] font-normal text-center leading-relaxed">
            ログアウトします<br />
            よろしいですか？
          </DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <DialogFooter className="!flex !flex-row !gap-3 !justify-center !items-center !w-full sm:!justify-center">
          <Button
            variant="secondary"
            onClick={onClose}
            className="bg-gray-400 hover:bg-gray-500 text-white rounded-xl text-[16px] font-[400]"
            style={{ width: '122px', height: '42px' }}
          >
            戻る
          </Button>
          <Button
            onClick={onConfirm}
            className="bg-[#c94e4e] hover:bg-[#c93838] text-white rounded-xl text-[16px] font-[400]"
            style={{ width: '122px', height: '42px' }}
          >
            ログアウト
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});

LogoutModal.displayName = 'LogoutModal'; 