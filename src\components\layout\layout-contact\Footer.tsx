import React from "react";
import logoIndex02 from "/images/logo_index02.png";

function Footer() {
  return (
    <footer className="-mt-[60px]">
      {/* Phần nền xanh */}
      <div className="bg-[#13ae9c] text-white py-8">
        <div className="max-w-screen-xl mx-auto px-4 flex flex-col items-center">
          {/* Menu links */}
          <ul className="flex space-x-16 text-md text-white font-semibold pb-16">
            <li>
              <a
                href="https://choqi.co.jp/index.html#company"
                className="hover:underline"
              >
                COMPANY
              </a>
            </li>
            <li>
              <a
                href="/contact"
                className="flex items-center space-x-1 hover:underline"
              >
                <svg
                  className="w-4 h-4 fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                >
                  <path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z" />
                </svg>
                <span>CONTACT</span>
              </a>
            </li>
          </ul>

          {/* Logo */}
          <div className="footer-logo">
            <a href="../">
              <img className="h-[101px]" src={logoIndex02} alt="ChoQi Logo" />
            </a>
          </div>
        </div>
      </div>

      {/* Phần trắng phía dưới */}
      <div className="bg-white text-sm font-semibold text-gray-700">
        <div className="max-w-screen-xl mx-auto px-20 py-4 flex flex-col md:flex-row justify-between items-center md:items-start space-y-2 md:space-y-0">
          {/* Chính sách */}
          <div className="flex space-x-6">
            <a
              href="https://choqi.co.jp/privacypolicy.html"
              className="hover:underline"
            >
              プライバシーポリシー
            </a>
            <a
              href="https://choqi.co.jp/choqipay/rule.pdf"
              className="hover:underline"
            >
              利用規約
            </a>
          </div>

          {/* Bản quyền */}
          <div className="text-center md:text-right">
            <p>© ChoQi Co., Ltd.</p>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
