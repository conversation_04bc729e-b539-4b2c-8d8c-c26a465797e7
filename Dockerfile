# Stage 1: Build app với Node 24
FROM node:24-alpine AS builder

WORKDIR /app
COPY . .

RUN npm install
RUN npm run build

# Stage 2: Serve app tĩnh bằng "serve"
FROM node:24-alpine

WORKDIR /app

# Cài gói serve (dùng để chạy file tĩnh React)
RUN npm install -g serve

# Copy build từ stage 1
COPY --from=builder /app/dist ./dist

# Expose port 30099 (serve mặc định dùng 3009)
EXPOSE 3009

# Chạy ứng dụng
CMD ["serve", "-s", "dist", "-l", "3009"]
