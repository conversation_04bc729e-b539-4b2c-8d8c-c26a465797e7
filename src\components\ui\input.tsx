import * as React from "react"

import { cn } from "@/lib/utils"

interface InputProps extends React.ComponentProps<"input"> {
  error?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, ...props }, ref) => {
    return (
      <div className="relative">
      <input
        type={type}
        className={cn(
          "bg-white text-[1.75rem] border w-full h-[66px] px-[19px] rounded-[13px] border-[#707070] border-solid text-[28px] text-[rgba(112,112,112,1)] placeholder:text-[rgba(112,112,112,0.5)] focus:outline-none shadow-md max-md:pr-5",
          className
        )}
        ref={ref}
        {...props}
      />
      {error && (
        <span className="absolute top-full left-0 mt-1 font-bold text-[rgba(255,0,0,1)] text-sm z-10" aria-label="required">
          {error}
        </span>
      )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
