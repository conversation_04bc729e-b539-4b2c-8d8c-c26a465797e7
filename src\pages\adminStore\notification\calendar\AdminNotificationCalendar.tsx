import React from 'react';
import { Link } from 'react-router-dom';
import {
  PageTitle,
  TransferCycleInfo,
  CalendarTable,
  CALENDAR_DATA
} from '@/features/adminStore/notification';

const AdminNotificationCalendar: React.FC = () => {

  return (
    <div className="p-1 sm:p-2">
      <PageTitle title="振込日と対象期間が確認できます。" />

      <TransferCycleInfo isAdmin={true} />

      <CalendarTable data={CALENDAR_DATA} isAdmin={true} />

    </div>
  );
};

export default AdminNotificationCalendar;
