import React from 'react';
import { SidebarFooter } from '@/components/ui/sidebar';
import { formatLastLogin } from '@/features/app-sidebar/utils/sidebarUtils';

interface SidebarFooterProps {
  lastSuccessfulLogin?: string;
}

export const AppSidebarFooter: React.FC<SidebarFooterProps> = React.memo(({ 
  lastSuccessfulLogin 
}) => {
  return (
    <SidebarFooter className="p-4 pl-6">
      <div className="mt-4 text-[20px] text-[#6F6F6E]">
        <p>前回ログイン</p>
        <p>{formatLastLogin(lastSuccessfulLogin)}</p>
      </div>
    </SidebarFooter>
  );
});

AppSidebarFooter.displayName = 'AppSidebarFooter'; 