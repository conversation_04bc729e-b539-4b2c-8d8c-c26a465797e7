import apiService from '@/services/api';
import { API_ENDPOINTS } from '@/config/api-endpoints';

class CrepicoPaymentService {
  /**
   * Search Crepico payment data
   * @param merchantNo - Merchant number
   * @param terminalIdentification - Terminal identification
   * @param paymentStatus - Payment status
   * @param paymentTypes - Payment types
   * @param transactionCategory - Transaction category
   * @param fromDate - From date
   * @param toDate - To date
   * @param fromSaleAmount - From sale amount
   * @param toSaleAmount - To sale amount
   * @param page - Page number
   * @param pageSize - Page size
   * @param sortValue - Sort value
   * @param sortDirectionValue - Sort direction value
   * @returns Promise with payment data
   */
  async searchCrepicoPaymentData(
    merchantNo: string,
    terminalIdentification: string,
    paymentStatus: string,
    paymentTypes: string,
    transactionCategory: string,
    fromDate: string,
    toDate: string,
    fromSaleAmount: string,
    toSaleAmount: string,
    page: number,
    pageSize: number,
    sortValue: string,
    sortDirectionValue: string
  ): Promise<any> {
    const params = {
      merchantNo,
      terminalIdentification,
      paymentStatus,
      paymentTypes,
      transactionCategories: transactionCategory,
      fromDate,
      toDate,
      fromSaleAmount,
      toSaleAmount,
      page: page.toString(),
      pageSize: pageSize.toString(),
      sortValue,
      sortDirection: sortDirectionValue
    };

    return apiService.get(API_ENDPOINTS.AGX_CREPICO_PAYMENT_DATA.SEARCH, params);
  }
}

export const crepicoPaymentService = new CrepicoPaymentService();
