import { CheckType } from "@/features/invoice/components/store/CheckType";
import { InvoiceCrepico } from "@/features/invoice/components/store/crepico";
import { InvoicePaygate } from "@/features/invoice/components/store/paygate/index"
import { useAuthStore } from "@/store";

const InvoicePage = () => {
  const { user } = useAuthStore();
  const { agxOldMerchantNo, agxNewMerchantNo } = user;
  if (user?.memberType && ((agxOldMerchantNo && !agxNewMerchantNo) || (!agxOldMerchantNo && !agxNewMerchantNo))) {
    return <InvoiceCrepico />
  } else if (!user?.memberType && agxNewMerchantNo) {
    return <CheckType />
  } else if (!user?.memberType && !agxNewMerchantNo) {
    return <InvoicePaygate />
  }

  return <CheckType />
}

export default InvoicePage