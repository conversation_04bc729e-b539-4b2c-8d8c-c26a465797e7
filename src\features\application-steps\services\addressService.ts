import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";

interface AddressData {
    kenName: string;
    cityTownName: string;
    cityTownFuri: string;
}

interface AddressResponse {
    data: AddressData;
}

// Mock service for address lookup
// In reality, this service will call actual API
export class AdAddressService {

    async getData(zip: string): Promise<AddressResponse> {
        const response = await apiService.get(API_ENDPOINTS.APPLICATION_STEPS.GET_ADDRESS(zip)) as AddressResponse;
        return response;
    }
    
}

export const adAddressService = new AdAddressService(); 