import React from 'react';
import { Link } from 'react-router-dom';
import {
  PageTitle,
  PaygateSection,
  PaymentTerminalSetup,
  TerminalManagement,
  usePaygateData
} from '@/features/store/notification';
import { LoadingSpinner } from '@/components/LoadingSpinner';

const StoreNotificationPaygate: React.FC = () => {
  const { paygateData } = usePaygateData();

  if (paygateData.loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="flex flex-col p-2">
      <div className="flex-1">
        <PageTitle title="決済端末の初期設定に必要な情報を確認できます。" />

        <PaygateSection title="決済端末の初期設定">
          <PaymentTerminalSetup
            appSerialNumber={paygateData.appSerialNumber}
            isAdmin={false}
          />
        </PaygateSection>

        <PaygateSection title="端末管理画面の初期設定">
          <TerminalManagement
            id={paygateData.id}
            password={paygateData.password}
            isAdmin={false}
          />
        </PaygateSection>
      </div>
    </div>
  );
};

export default StoreNotificationPaygate;
