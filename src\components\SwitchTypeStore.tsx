import { Link } from 'react-router-dom';

interface SwitchTypeStoreProps {
  paygateUrl: string;
  crepicoUrl: string;
}

const SwitchTypeStore = ({ paygateUrl, crepicoUrl }: SwitchTypeStoreProps) => {
    return (
        <div className="wrapper-body py-24 px-8 md:py-32 md:px-10 lg:py-32 lg:px-20 xl:py-32 xl:px-28">
            <div className="grid grid-cols-1 xl:grid-cols-2 xl:gap-x-72 gap-y-20 items-center pr-20">
                <Link 
                    to={paygateUrl}
                    className="flex items-center justify-center w-full h-48 py-16 px-2 border-2 border-gray-400 rounded-lg hover:shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl"
                >
                    PAYGATE
                </Link>

                <Link 
                    to={crepicoUrl}
                    className="flex items-center justify-center w-full h-48 py-16 px-2 border-2 border-gray-400 rounded-lg hover:shadow-md transition-shadow duration-200 text-[#6F6F6E] font-bold text-2xl"
                >
                    クレピコ
                </Link>
            </div>
        </div>
    );
}

export default SwitchTypeStore;