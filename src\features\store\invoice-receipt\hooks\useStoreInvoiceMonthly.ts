import { useQuery } from '@tanstack/react-query';
import invoiceMonthlyService from '@/features/store/invoice-receipt/services/invoiceMonthlyService';
import { InvoiceDetailResponse } from '@/features/store/invoice-receipt/services/invoiceService';

interface UseStoreInvoiceMonthlyReturn {
    data: InvoiceDetailResponse['data'] | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
}

export const useStoreInvoiceMonthly = (invoiceNo: string | undefined): UseStoreInvoiceMonthlyReturn => {
    const {
        data = null,
        isLoading: loading,
        error,
        refetch
    } = useQuery({
        queryKey: ['store-invoice-monthly', invoiceNo],
        queryFn: async () => {
            if (!invoiceNo) {
                throw new Error('請求書番号が見つかりません。');
            }
            const agxMerchantNo = atob(invoiceNo).split("-")[0];
            const result = await invoiceMonthlyService.getData(agxMerchantNo, invoiceNo);
            return result.data;
        },
        enabled: !!invoiceNo,
        staleTime: 0,
        gcTime: 0,
    });

    return { 
        data, 
        loading, 
        error: error?.message || null, 
        refetch 
    };
}; 