import { useMutation } from "@tanstack/react-query";
import DocumentDownloadService from "@/features/document/services/documentDownloadService";

const documentDownloadService = new DocumentDownloadService();

export const useDocumentDownloadMutation = (p0: {
  onSuccess: (response: any) => void;
  onError: (error: any) => void;
}) => {
  const downloadDocumentMutation = useMutation({
    mutationFn: (requestBody: any) =>
      documentDownloadService.create(requestBody),
    ...p0,
  });
  return {
    downloadDocument: downloadDocumentMutation.mutate,
    downloadDocumentAsync: downloadDocumentMutation.mutateAsync,
    isLoading: downloadDocumentMutation.isPending,
    isError: downloadDocumentMutation.isError,
    error: downloadDocumentMutation.error,
    reset: downloadDocumentMutation.reset,
  };
};
