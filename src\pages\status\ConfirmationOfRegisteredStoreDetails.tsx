import { PageHeader } from "@/components/PageHeader";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import ActionButtons from "@/features/status/components/ActionButtons";
import BankInformation from "@/features/status/components/BankInformation";
import BillingInfomation from "@/features/status/components/BillingInformation";
import ChokipayInformation from "@/features/status/components/ChokipayInformation";
import CorporateInformation from "@/features/status/components/CorporateInformation";
import RepresentativeInformation from "@/features/status/components/RepresentativeInformation";
import AdditionalInformation from "@/features/status/components/AdditionalInformation";
import StoreInformation from "@/features/status/components/StoreInformation";
import { useQueryMerchantStatus } from "@/features/status/hooks/useQueryMerchantStatus";
import { useAuthStore } from "@/store";

export function ConfirmationOfRegisteredStoreDetails() {
  const { user } = useAuthStore();
  const { data: merchantStatusData } = useQueryMerchantStatus({
    agxMerchantNo: user?.agxMerchantNo || "",
  });
  return (
    <div className="max-w-[1450px] mx-auto w-full px-4 md:px-6 py-4 md:py-8">
      <PageHeader title="加盟店申し込み ｜ 付帯情報" progress={100} />
      <div className="mx-auto space-y-6 md:space-y-8 lg:space-y-10 mb-32">
        <div className="min-h-screen">
          <div className="">
            {/* Header */}
            <div className="flex justify-between items-center mb-6 pt-4">
              <div></div>
              <div>
                <span className="text-teal-500 cursor-pointer text-[20px] hover:underline">
                  よくあるご質問
                </span>
              </div>
            </div>

            {/* Tabs Navigation */}
            <Tabs defaultValue="legal" className="w-full text-[20px]">
              <TabsList className="flex w-full justify-between bg-white rounded-none h-12 border-b-2 pb-[2px]">
                <TabsTrigger
                  value="billing"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md text-[20px]">
                    請求情報
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="legal"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md text-[20px]">
                    法人情報
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="representative"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md text-[20px]">
                    代表者情報
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="store"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md text-[20px]">
                    店舗情報
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="bank"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md text-[20px]">
                    銀行情報
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="text-[20px] group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md">
                    セキュリティ情報
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="choqipay"
                  className="
                    group 
                    text-[20px] items-end text-gray-600 
                    data-[state=active]:text-teal-700 
                    data-[state=active]:border-b-2 
                    data-[state=active]:border-teal-500 
                    border-t-0 border-l-0 border-r-0 flex rounded-none
                    pt-1 pl-1 pr-1
                  "
                >
                  <div className="text-[20px] group-data-[state=active]:bg-[#F6F6F6] rounded-tl-md rounded-tr-md">
                    チョキペイ情報
                  </div>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="billing">
                <BillingInfomation merchantData={merchantStatusData} />
              </TabsContent>
              <TabsContent value="legal">
                <CorporateInformation merchantData={merchantStatusData} />
                <div className="flex justify-between">
                  <div />
                  <ActionButtons />
                </div>
              </TabsContent>
              <TabsContent value="representative">
                <RepresentativeInformation merchantData={merchantStatusData} />
              </TabsContent>
              <TabsContent value="store">
                <StoreInformation merchantData={merchantStatusData} />
              </TabsContent>
              <TabsContent value="bank">
                <BankInformation merchantData={merchantStatusData} />
              </TabsContent>
              <TabsContent value="security">
                <AdditionalInformation merchantData={merchantStatusData} />
              </TabsContent>
              <TabsContent value="choqipay">
                <ChokipayInformation merchantData={merchantStatusData} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConfirmationOfRegisteredStoreDetails;
