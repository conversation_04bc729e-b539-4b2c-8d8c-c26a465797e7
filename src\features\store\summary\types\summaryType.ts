export interface SummaryData {
  chartLabel: string[];
  creditData: number[];
  electronicData: number[];
  qrData: number[];
  lineData: number[];
  totalData: number[];
  error: boolean;
}

export interface SummaryApiResponse {
  data: {
    chartLabel: string[];
    creditData: number[];
    electronicData: number[];
    qrData: number[];
    lineData: number[];
  };
}

export interface MonthlySummaryApiResponse {
  data: {
    chartLabel: string[];
    creditData: number[];
    electronicData: number[];
    qrData: number[];
    lineData: number[];
    barData: number[];
    barLastYearData: number[];
  };
}

export interface SummaryState {
  chartLabel: string[];
  creditData: number[];
  electronicData: number[];
  qrData: number[];
  lineData: number[];
  totalData: number[];
  error: boolean;
}

export interface MonthlySummaryState {
  chartLabel: string[];
  creditData: number[];
  electronicData: number[];
  qrData: number[];
  lineData: number[];
  barData: number[];
  barLastYearData: number[];
  error: boolean;
}

export const initialSummaryState: SummaryState = {
  chartLabel: [],
  creditData: [],
  electronicData: [],
  qrData: [],
  lineData: [],
  totalData: [],
  error: false
};

export const initialMonthlySummaryState: MonthlySummaryState = {
  chartLabel: [],
  creditData: [],
  electronicData: [],
  qrData: [],
  lineData: [],
  barData: [],
  barLastYearData: [],
  error: false
};
