import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { AgxFeeRateResponse } from "../types";

export class AgxFeeRateService {
    async getAllData(agxBusinessType: string): Promise<AgxFeeRateResponse> {
        const response = await apiService.get(API_ENDPOINTS.APPLICATION_STEPS.GET_AGX_FEE_RATE(agxBusinessType));
        return response as AgxFeeRateResponse;
    }
}

export const agxFeeRateService = new AgxFeeRateService();