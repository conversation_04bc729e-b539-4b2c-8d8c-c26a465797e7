import { useQuery } from '@tanstack/react-query';
import { invoiceService } from '../services/invoiceService';

// Types
export interface InvoiceDetail {
    name: string;
    quantity: number;
    unitPrice: number;
    amount: number;
}

export interface InvoiceData {
    invoiceCreatedDate: string;
    invoiceDeadlineDate: string;
    storeName: string;
    invoiceDetail: InvoiceDetail[];
    subTotal: number;
    tax: number;
    total: number;
}

interface UseStoreDetailsReturn {
    data: InvoiceData;
    loading: boolean;
    error: string | null;
    fetchData: (invoiceNo: string) => Promise<void>;
    refetch: () => void;
}

const initialData: InvoiceData = {
    invoiceCreatedDate: '',
    invoiceDeadlineDate: '',
    storeName: '',
    invoiceDetail: [],
    subTotal: 0,
    tax: 0,
    total: 0
};

export const useStoreInvoiceDetails = (invoiceNo?: string): UseStoreDetailsReturn => {
    const {
        data = initialData,
        isLoading: loading,
        error,
        refetch
    } = useQuery({
        queryKey: ['store-invoice-details', invoiceNo],
        queryFn: async () => {
            if (!invoiceNo) {
                throw new Error('Invoice number không được để trống');
            }
            
            const response = await invoiceService.getData(invoiceNo);
            
            if (!response?.data) {
                throw new Error('Không có dữ liệu trả về từ API');
            }
            
            return response.data;
        },
        enabled: !!invoiceNo,
        staleTime: 0,
        gcTime: 0,
    });

    // Legacy function for compatibility
    const fetchData = async (invoiceNumber: string) => {
        // This will be handled by changing the invoiceNo parameter
        // or calling refetch() when the query key changes
        refetch();
    };

    return {
        data,
        loading,
        error: error?.message || null,
        fetchData,
        refetch
    };
};

// Hook riêng cho AGX invoice detail
export const useAgxInvoiceDetail = (merchantNo?: string) => {
    const {
        data = null,
        isLoading: loading,
        error,
        refetch
    } = useQuery({
        queryKey: ['agx-invoice-detail', merchantNo],
        queryFn: async () => {
            if (!merchantNo) {
                throw new Error('Merchant number is required');
            }
            
            const response = await invoiceService.getAgxInvoiceDetail(merchantNo);
            return response.data;
        },
        enabled: !!merchantNo,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    return {
        data,
        loading,
        error: error?.message || null,
        refetch
    };
};

// Hook cho GMO info
export const useGmoInfo = (merchantNo?: string) => {
    const {
        data = null,
        isLoading: loading,
        error,
        refetch
    } = useQuery({
        queryKey: ['gmo-info', merchantNo],
        queryFn: async () => {
            if (!merchantNo) {
                throw new Error('Merchant number is required');
            }
            
            const response = await invoiceService.getGmoInfo(merchantNo);
            return response.data;
        },
        enabled: !!merchantNo,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    return {
        data,
        loading,
        error: error?.message || null,
        refetch
    };
};
