import { getStoreNotificationFaqUrl, getStoreNotificationRollpaperUrl } from "@/features/app-sidebar/constants/sidebarConstants";
import { useAuthStore } from "@/features/auth/slices/authStore";
import { AccountTypes, TypeStore } from "@/types/globalType";
import { useNavigate } from "react-router-dom";

export const useGetUrlRedirect = () => {
  const { user, typeStore } = useAuthStore();
  const navigate = useNavigate();

  const handleClickOpenNewTab = (url: string) => {
    window.open(url, "_blank");
  }

  const handleRollPaperClick = () => {
    const url = getStoreNotificationRollpaperUrl(typeStore!);

    // If it's a migrate store, navigate internally
    if (typeStore === TypeStore.STORE_MIGRATE) {
      navigate(url);
    } else {
      // For paygate and crepico, open in new tab
      window.open(url, "_blank");
    }
  }

  const handleFaqClick = () => {
    const url = getStoreNotificationFaqUrl(typeStore!);

    // If it's a migrate store, navigate internally
    if (typeStore === TypeStore.STORE_MIGRATE) {
      navigate(url);
    } else {
      // For paygate and crepico, open in new tab
      window.open(url, "_blank");
    }
  }

  const getUrlDeposit = () => {
    if (user?.statusAccount === AccountTypes.ADMIN_STORE) {
      return "/admin-store/deposit";
    }
    if (typeStore === TypeStore.STORE_CREPICO) {
      return "/store/deposit/crepico";
    }
    if (typeStore === TypeStore.STORE_PAYGATE) {
      return "/store/deposit/paygate";
    }
    return "/store/deposit";
  }

  const getUrlConfig = () => {
    if (user?.statusAccount === AccountTypes.ADMIN_STORE) {
      return "/admin-store/config";
    }
    return "/store/config";
  }

  const getUrlInvoiceReceipt = () => {
    if (user?.statusAccount === AccountTypes.ADMIN_STORE) {
      return "/admin-store/invoice-receipt";
    }
    if (typeStore === TypeStore.STORE_CREPICO) {
      return "/store/invoice-receipt/crepico";
    }
    if (typeStore === TypeStore.STORE_PAYGATE) {
      return "/store/invoice-receipt/paygate";
    }
    return "/store/invoice-receipt";
  }

  return {
    merchantNo: user?.agxMerchantNo,
    storeName: user?.agxStoreName,
    typeStore,
    accountType: user?.statusAccount,
    handleClickOpenNewTab,
    handleRollPaperClick,
    handleFaqClick,
    getUrlDeposit,
    getUrlConfig,
    getUrlInvoiceReceipt,
  }
}