
import React from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useSummary } from '@/features/store/summary/hooks/useSummary';
import { useAuthStore } from '@/features/auth/slices/authStore';
import SummaryHeader from './shared/SummaryHeader';
import SummaryChart from './shared/SummaryChart';
import SummaryTable from './shared/SummaryTable';

const SummaryPage: React.FC<{agxMerchantNo: string; type: string  }> = ({agxMerchantNo, type }) => {
  const { user } = useAuthStore();
  const storeName = user?.agxStoreName || '';

  // Use actual API hook
  const { summaryData } = useSummary(agxMerchantNo);

  // Show error if no merchant number
  if (!agxMerchantNo) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-[#6F6F6E]">認証情報が見つかりません。</h2>
      </div>
    );
  }

  return (
    <div className="px-2 sm:px-4 py-4 sm:py-6">
      <SummaryHeader
        title="売上金額・件数推移（振込日別）"
        currentPath={`/store/summary/${type}`}
        type={type}
        storeName={storeName}
        agxMerchantNo={agxMerchantNo}
      />

      <SummaryChart
        chartLabel={summaryData.chartLabel}
        lineData={summaryData.lineData}
        creditData={summaryData.creditData}
        electronicData={summaryData.electronicData}
        qrData={summaryData.qrData}
      />

      <SummaryTable
        chartLabel={summaryData.chartLabel}
        lineData={summaryData.lineData}
        creditData={summaryData.creditData}
        electronicData={summaryData.electronicData}
        qrData={summaryData.qrData}
        totalData={summaryData.totalData}
        isMonthly={false}
      />
    </div>
  );
};

export default SummaryPage;
