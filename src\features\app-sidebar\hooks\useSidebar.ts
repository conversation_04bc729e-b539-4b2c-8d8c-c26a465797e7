import { useState, useCallback, useMemo } from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { useLogout } from '@/features/auth/hooks/useLogout';
import { getMenuItems, toggleSubmenuState } from '../utils/sidebarUtils';

export const useSidebar = () => {
  const { user, typeStore } = useAuthStore();
  const { handleLogout } = useLogout();
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({});

  // Memoize menu items to prevent unnecessary re-renders
  const menuItems = useMemo(() => 
    getMenuItems(user?.statusAccount, typeStore), 
    [user?.statusAccount, typeStore]
  );

  const handleSubmenuToggle = useCallback((menuTitle: string) => {
    setOpenSubmenus(prev => toggleSubmenuState(prev, menuTitle));
  }, []);

  const handleLogoutClick = useCallback(() => {
    setShowLogoutModal(true);
  }, []);

  const handleConfirmLogout = useCallback(() => {
    setShowLogoutModal(false);
    handleLogout();
  }, [handleLogout]);

  const handleCancelLogout = useCallback(() => {
    setShowLogoutModal(false);
  }, []);

  return {
    user,
    menuItems,
    openSubmenus,
    showLogoutModal,
    handleSubmenuToggle,
    handleLogoutClick,
    handleConfirmLogout,
    handleCancelLogout,
  };
}; 