import React from 'react';
import DualServiceLinks from '@/components/DualServiceLinks';

const StoreNotificationFaq: React.FC = () => {
  const paygateLink = {
    id: 'paygate',
    label: 'PAYGATE',
    url: 'https://choqi.co.jp/choqipay/faq/faq_list2.html?_gl=1*1im6h43*_ga*MTc3MTcwMzI0NC4xNjk0NDE2MTQz*_ga_CKW2PPLYTQ*MTcwNTk3Nzc1My4xOTkuMS4xNzA1OTc5NDMzLjAuMC4w*_ga_6SPDYLRMQY*MTcwNTk3Nzc1My4zNDcuMS4xNzA1OTc5NDMzLjAuMC4w*_ga_E11ER9VJEZ*MTcwNTk3Nzc1My4zNDcuMS4xNzA1OTc5NDMzLjAuMC4w*_ga_SK2YTSMLQC*MTcwNTk3Nzc1My42MjcuMS4xNzA1OTc5NDMzLjYwLjAuMA..'
  };

  const crepicoLink = {
    id: 'crepico',
    label: 'クレピコ',
    url: 'https://choqi.co.jp/choqipay/faq/faq_list12.html'
  };

  const crepicoInfo = {
    username: 'cp-user',
    password: 'cp-user1'
  };

  return (
    <DualServiceLinks
      paygateLink={paygateLink}
      crepicoLink={crepicoLink}
      showCrepicoInfo={true}
      crepicoInfo={crepicoInfo}
    />
  );
};

export default StoreNotificationFaq;
