import Box from "@/components/ui/box";
import { CustomSelect } from "@/components/ui/select";
import { STEP } from "@/constants/common.constant";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { FormButtons } from "./ui/FormButtons";
import { ApplicationStepProps } from "../types";
import { FormField } from "./ui/Field";
import ConfirmDialog from "@/components/ConfirmDialog";

export const Additional1 = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating}: ApplicationStepProps) => {
    // State management cho checkbox và form
    const [isChecked, setIsChecked] = useState(true);
    const [show, setShow] = useState(false);
    
    // Year/Month states for date selectors
    const currentYear = new Date().getFullYear();
    const currentMonth = (new Date().getMonth() + 1) < 10 ? ("0" + (new Date().getMonth() + 1)) : (new Date().getMonth() + 1).toString();
    
    const [noRetainingCardInfoYear, setNoRetainingCardInfoYear] = useState(currentYear);
    const [noRetainingCardInfoMonth, setNoRetainingCardInfoMonth] = useState(currentMonth);
    const [pcidssExpectedComplianceYear, setPcidssExpectedComplianceYear] = useState(currentYear);
    const [pcidssExpectedComplianceMonth, setPcidssExpectedComplianceMonth] = useState(currentMonth);
    const [threeDSecureYear, setThreeDSecureYear] = useState(currentYear);
    const [threeDSecureMonth, setThreeDSecureMonth] = useState(currentMonth);
    const [securityCodeCheckYear, setSecurityCodeCheckYear] = useState(currentYear);
    const [securityCodeCheckMonth, setSecurityCodeCheckMonth] = useState(currentMonth);
    const [illegalDeliveryDestinationYear, setIllegalDeliveryDestinationYear] = useState(currentYear);
    const [illegalDeliveryDestinationMonth, setIllegalDeliveryDestinationMonth] = useState(currentMonth);
    const [behaviorAnalysisYear, setBehaviorAnalysisYear] = useState(currentYear);
    const [behaviorAnalysisMonth, setBehaviorAnalysisMonth] = useState(currentMonth);
    const [otherMeasuresYear, setOtherMeasuresYear] = useState(currentYear);
    const [otherMeasuresMonth, setOtherMeasuresMonth] = useState(currentMonth);
    
    const [formData, setFormData] = useState({
        // 特定商取引に関する確認
        agxDoorToDoorSales: 'false',
        agxTelemarketingSales: 'false',
        agxPyramidScheme: 'false',
        agxBusinessOpportunityRelatedSales: 'false',
        agxSpecifiedContinuousServices: 'false',
        
        // カード情報保護対策
        agxCardInformationRetentionStatus: '283260001',
        agxPcidssStatus: '',
        
        // 不正使用対策
        agxThreeDSecureStatus: '',
        agxSecurityCodeCheckStatus: '',
        agxIllegalDeliveryDestinationStatus: '',
        agxBehaviorAnalysisStatus: '',
        agxOtherMeasuresStatus: '',
        agxOtherMeasuresDescription: ''
    });

    // Generate year options (current year + 10 years)
    const generateYearOptions = () => {
        const years = [];
        for (let i = 0; i <= 10; i++) {
            const year = currentYear + i;
            years.push({ value: year.toString(), label: `${year}年` });
        }
        return years;
    };

    // Month options
    const monthOptions = [
        { value: "01", label: "1月" },
        { value: "02", label: "2月" },
        { value: "03", label: "3月" },
        { value: "04", label: "4月" },
        { value: "05", label: "5月" },
        { value: "06", label: "6月" },
        { value: "07", label: "7月" },
        { value: "08", label: "8月" },
        { value: "09", label: "9月" },
        { value: "10", label: "10月" },
        { value: "11", label: "11月" },
        { value: "12", label: "12月" }
    ];

    // Split date function to parse existing dates
    const splitDate = () => {
        const arrayDate1 = agxMerchantParams?.agxNoRetainingCardInfoDate?.split("-");
        const arrayDate2 = agxMerchantParams?.agxPcidssExpectedComplianceDate?.split("-");
        const arrayDate3 = agxMerchantParams?.agxThreeDSecureDate?.split("-");
        const arrayDate4 = agxMerchantParams?.agxSecurityCodeCheckDate?.split("-");
        const arrayDate5 = agxMerchantParams?.agxIllegalDeliveryDestinationDate?.split("-");
        const arrayDate6 = agxMerchantParams?.agxBehaviorAnalysisDate?.split("-");
        const arrayDate7 = agxMerchantParams?.agxOtherMeasuresDate?.split("-");

        if (arrayDate1?.length === 3) {
            setNoRetainingCardInfoYear(parseInt(arrayDate1[0]));
            setNoRetainingCardInfoMonth(arrayDate1[1]);
        }
        if (arrayDate2?.length === 3) {
            setPcidssExpectedComplianceYear(parseInt(arrayDate2[0]));
            setPcidssExpectedComplianceMonth(arrayDate2[1]);
        }
        if (arrayDate3?.length === 3) {
            setThreeDSecureYear(parseInt(arrayDate3[0]));
            setThreeDSecureMonth(arrayDate3[1]);
        }
        if (arrayDate4?.length === 3) {
            setSecurityCodeCheckYear(parseInt(arrayDate4[0]));
            setSecurityCodeCheckMonth(arrayDate4[1]);
        }
        if (arrayDate5?.length === 3) {
            setIllegalDeliveryDestinationYear(parseInt(arrayDate5[0]));
            setIllegalDeliveryDestinationMonth(arrayDate5[1]);
        }
        if (arrayDate6?.length === 3) {
            setBehaviorAnalysisYear(parseInt(arrayDate6[0]));
            setBehaviorAnalysisMonth(arrayDate6[1]);
        }
        if (arrayDate7?.length === 3) {
            setOtherMeasuresYear(parseInt(arrayDate7[0]));
            setOtherMeasuresMonth(arrayDate7[1]);
        }
    };

    // Load data từ props khi component mount
    useEffect(() => {
        if (agxMerchantParams) {
            setFormData({
                // Convert boolean to string for select options
                agxDoorToDoorSales: agxMerchantParams.agxDoorToDoorSales === true ? 'true' : 'false',
                agxTelemarketingSales: agxMerchantParams.agxTelemarketingSales === true ? 'true' : 'false',
                agxPyramidScheme: agxMerchantParams.agxPyramidScheme === true ? 'true' : 'false',
                agxBusinessOpportunityRelatedSales: agxMerchantParams.agxBusinessOpportunityRelatedSales === true ? 'true' : 'false',
                agxSpecifiedContinuousServices: agxMerchantParams.agxSpecifiedContinuousServices === true ? 'true' : 'false',
                agxCardInformationRetentionStatus: agxMerchantParams.agxCardInformationRetentionStatus?.toString() || '283260001',
                agxPcidssStatus: agxMerchantParams.agxPcidssStatus?.toString() || '283260001',
                agxThreeDSecureStatus: agxMerchantParams.agxThreeDSecureStatus?.toString() || '283260001',
                agxSecurityCodeCheckStatus: agxMerchantParams.agxSecurityCodeCheckStatus?.toString() || '283260001',
                agxIllegalDeliveryDestinationStatus: agxMerchantParams.agxIllegalDeliveryDestinationStatus?.toString() || '283260001',
                agxBehaviorAnalysisStatus: agxMerchantParams.agxBehaviorAnalysisStatus?.toString() || '283260001',
                agxOtherMeasuresStatus: agxMerchantParams.agxOtherMeasuresStatus?.toString() || '283260001',
                agxOtherMeasuresDescription: agxMerchantParams.agxOtherMeasuresDescription || ''
            });
            splitDate();
        }
    }, [agxMerchantParams]);

    // Handle field changes
    const handleFieldChange = (fieldName: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [fieldName]: value
        }));

        // Special logic for card information retention status
        if (fieldName === 'agxCardInformationRetentionStatus' && value === '283260001') {
            setFormData(prev => ({
                ...prev,
                agxPcidssStatus: '283260001'
            }));
        }
    };

    // Year/Month change handlers
    const handleChangeNoRetainingCardInfoYear = (value: string) => {
        setNoRetainingCardInfoYear(parseInt(value));
    };

    const handleChangeNoRetainingCardInfoMonth = (value: string) => {
        setNoRetainingCardInfoMonth(value);
    };

    const handleChangePcidssExpectedComplianceYear = (value: string) => {
        setPcidssExpectedComplianceYear(parseInt(value));
    };

    const handleChangePcidssExpectedComplianceMonth = (value: string) => {
        setPcidssExpectedComplianceMonth(value);
    };

    const handleChangeThreeDSecureYear = (value: string) => {
        setThreeDSecureYear(parseInt(value));
    };

    const handleChangeThreeDSecureMonth = (value: string) => {
        setThreeDSecureMonth(value);
    };

    const handleChangeSecurityCodeCheckYear = (value: string) => {
        setSecurityCodeCheckYear(parseInt(value));
    };

    const handleChangeSecurityCodeCheckMonth = (value: string) => {
        setSecurityCodeCheckMonth(value);
    };

    const handleChangeIllegalDeliveryDestinationYear = (value: string) => {
        setIllegalDeliveryDestinationYear(parseInt(value));
    };

    const handleChangeIllegalDeliveryDestinationMonth = (value: string) => {
        setIllegalDeliveryDestinationMonth(value);
    };

    const handleChangeBehaviorAnalysisYear = (value: string) => {
        setBehaviorAnalysisYear(parseInt(value));
    };

    const handleChangeBehaviorAnalysisMonth = (value: string) => {
        setBehaviorAnalysisMonth(value);
    };

    const handleChangeOtherMeasuresYear = (value: string) => {
        setOtherMeasuresYear(parseInt(value));
    };

    const handleChangeOtherMeasuresMonth = (value: string) => {
        setOtherMeasuresMonth(value);
    };

    // Convert form data to proper types for API
    const convertFormDataToApi = () => {
        return {
            ...agxMerchantParams, // Keep existing data
            // Convert string to boolean for 特定商取引に関する確認 fields
            agxDoorToDoorSales: formData.agxDoorToDoorSales === 'true',
            agxTelemarketingSales: formData.agxTelemarketingSales === 'true',
            agxPyramidScheme: formData.agxPyramidScheme === 'true',
            agxBusinessOpportunityRelatedSales: formData.agxBusinessOpportunityRelatedSales === 'true',
            agxSpecifiedContinuousServices: formData.agxSpecifiedContinuousServices === 'true',
            
            // Convert number fields
            agxCardInformationRetentionStatus: formData.agxCardInformationRetentionStatus ? parseInt(formData.agxCardInformationRetentionStatus) : 283260001,
            agxPcidssStatus: formData.agxPcidssStatus ? parseInt(formData.agxPcidssStatus) : 283260001,
            agxThreeDSecureStatus: formData.agxThreeDSecureStatus ? parseInt(formData.agxThreeDSecureStatus) : 283260001,
            agxSecurityCodeCheckStatus: formData.agxSecurityCodeCheckStatus ? parseInt(formData.agxSecurityCodeCheckStatus) : 283260001,
            agxIllegalDeliveryDestinationStatus: formData.agxIllegalDeliveryDestinationStatus ? parseInt(formData.agxIllegalDeliveryDestinationStatus) : 283260001,
            agxBehaviorAnalysisStatus: formData.agxBehaviorAnalysisStatus ? parseInt(formData.agxBehaviorAnalysisStatus) : 283260001,
            agxOtherMeasuresStatus: formData.agxOtherMeasuresStatus ? parseInt(formData.agxOtherMeasuresStatus) : 283260001,
            agxOtherMeasuresDescription: formData.agxOtherMeasuresDescription,

            // Add date fields - always use current state values
            agxNoRetainingCardInfoDate: `${noRetainingCardInfoYear}-${noRetainingCardInfoMonth}-01`,
            agxPcidssExpectedComplianceDate: `${pcidssExpectedComplianceYear}-${pcidssExpectedComplianceMonth}-01`,
            agxThreeDSecureDate: `${threeDSecureYear}-${threeDSecureMonth}-01`,
            agxSecurityCodeCheckDate: `${securityCodeCheckYear}-${securityCodeCheckMonth}-01`,
            agxIllegalDeliveryDestinationDate: `${illegalDeliveryDestinationYear}-${illegalDeliveryDestinationMonth}-01`,
            agxBehaviorAnalysisDate: `${behaviorAnalysisYear}-${behaviorAnalysisMonth}-01`,
            agxOtherMeasuresDate: `${otherMeasuresYear}-${otherMeasuresMonth}-01`
        };
    };

    const showConfirmDialog = async () => {
        setShow(true);
    }

    // Handle save
    const handleSave = async () => {
        updateMerchant(convertFormDataToApi());
        setShow(false);
    };

    // Handle submit/next
    const handleSubmit = () => {
        const apiData = convertFormDataToApi();
        setAgxMerchantParams(apiData);
        setStep(STEP.CHOQIPAY);
    };

    // Handle back
    const handleBack = () => {
        // Lưu dữ liệu form hiện tại vào agxMerchantParams trước khi back
        const apiData = convertFormDataToApi();
        setAgxMerchantParams(apiData);
        setStep(STEP.ADDITIONAL);
    };

    return (
        <>
            {/* Checkbox section */}
            <Box className="space-y-2 px-4 sm:px-0">
                <Box className="flex items-start sm:items-center gap-2 sm:gap-1 justify-start flex-col sm:flex-row">
                    <Checkbox 
                        id="checkbox" 
                        className="w-[18px] h-[18px] sm:w-[20px] sm:h-[20px] lg:w-[27px] lg:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0"
                        checked={isChecked}
                        onCheckedChange={(checked) => setIsChecked(checked === true)}
                    />
                    <Label className="text-[#C44546] text-sm sm:text-base lg:text-lg cursor-pointer lg:text-[1.75rem]" htmlFor="checkbox">
                        修正する必要がある場合は、このチェックを外してください
                    </Label>
                </Box>
                <p className="text-[#707070] pl-0 sm:pl-8 text-sm sm:text-base lg:text-[1.75rem]">
                修正がない場合は、ページ下より次に進んでください
                </p>
            </Box>
            
            {/* Form content - disabled when checkbox is checked */}
            <fieldset disabled={isChecked}>
                <Box className="mx-auto mb-16 space-y-4 sm:space-y-6 lg:space-y-8 xl:space-y-10 sm:mb-32 mt-6 sm:mt-10 px-4 sm:px-0">
                    <Box className="flex flex-col gap-4">
                        <Box className="space-y-4 sm:space-y-6 lg:space-y-8 xl:space-y-10">

                            {/* 特定商取引に関する確認 section */}
                            <h1 className="font-normal text-[#707070] mb-4 md:mb-6 text-base sm:text-lg lg:text-[1.75rem] px-0 sm:pl-[30px]">
                                特定商取引に関する確認<sup className="text-red-500">*</sup>
                            </h1>

                            <Box className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                <FormField label="訪問販売">
                                    <CustomSelect
                                        value={formData.agxDoorToDoorSales}
                                        className="w-[268px] h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                        onChange={(e) => handleFieldChange('agxDoorToDoorSales', e.target.value)}
                                        options={[
                                            { value: "false", label: "無" },
                                            { value: "true", label: "有" }
                                        ]}
                                    />
                                </FormField>

                                <FormField label="電話勧誘販売">
                                    <CustomSelect
                                        value={formData.agxTelemarketingSales}
                                        className="w-[268px] h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                        onChange={(e) => handleFieldChange('agxTelemarketingSales', e.target.value)}
                                        options={[
                                            { value: "false", label: "無" },
                                            { value: "true", label: "有" }
                                        ]}
                                    />
                                </FormField>

                                <FormField label="連鎖販売取引">
                                    <CustomSelect
                                        value={formData.agxPyramidScheme}
                                        className="w-[268px] h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                        onChange={(e) => handleFieldChange('agxPyramidScheme', e.target.value)}
                                        options={[
                                            { value: "false", label: "無" },
                                            { value: "true", label: "有" }
                                        ]}
                                    />
                                </FormField>

                                <FormField label="業務提供誘引販売取引">
                                    <CustomSelect
                                        value={formData.agxBusinessOpportunityRelatedSales}
                                        className="w-[268px] h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                        onChange={(e) => handleFieldChange('agxBusinessOpportunityRelatedSales', e.target.value)}
                                        options={[
                                            { value: "false", label: "無" },
                                            { value: "true", label: "有" }
                                        ]}
                                    />
                                </FormField>

                                <FormField label="特定継続的役務">
                                    <CustomSelect
                                        value={formData.agxSpecifiedContinuousServices}
                                        className="w-[268px] h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                        onChange={(e) => handleFieldChange('agxSpecifiedContinuousServices', e.target.value)}
                                        options={[
                                            { value: "false", label: "無" },
                                            { value: "true", label: "有" }
                                        ]}
                                    />
                                </FormField>
                            </Box>

                            <hr className="border-t border-[#707070]" />

                            {/* カード情報保護対策 section */}
                            <h1 className="font-normal text-[#707070] mb-4 md:mb-6 text-base sm:text-lg lg:text-[1.75rem] px-0 sm:pl-[30px]">
                                カード情報保護対策<sup className="text-red-500">*</sup>
                            </h1>

                            <Box className="space-y-3 sm:space-y-4 lg:space-y-6">
                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        クレジットカード情報の保持状況について
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                            value={formData.agxCardInformationRetentionStatus}
                                            onChange={(e) => handleFieldChange('agxCardInformationRetentionStatus', e.target.value)}
                                            options={[
                                                { value: "283260000", label: "保持している" },
                                                { value: "283260001", label: "保持していない" },
                                                { value: "283260002", label: "非保持化の予定あり" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxCardInformationRetentionStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={noRetainingCardInfoYear.toString()}
                                                    onChange={(e) => handleChangeNoRetainingCardInfoYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={noRetainingCardInfoMonth}
                                                    onChange={(e) => handleChangeNoRetainingCardInfoMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>
                                
                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        PCI DSSの準拠状況について
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors disabled:bg-gray-100 disabled:cursor-not-allowed"
                                            value={formData.agxPcidssStatus}
                                            onChange={(e) => handleFieldChange('agxPcidssStatus', e.target.value)}
                                            disabled={formData.agxCardInformationRetentionStatus === '283260001'}
                                            options={[
                                                { value: "283260000", label: "準拠している" },
                                                { value: "283260001", label: "準拠予定なし" },
                                                { value: "283260002", label: "準拠予定あり" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxPcidssStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={pcidssExpectedComplianceYear.toString()}
                                                    onChange={(e) => handleChangePcidssExpectedComplianceYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={pcidssExpectedComplianceMonth}
                                                    onChange={(e) => handleChangePcidssExpectedComplianceMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>
                            </Box>

                            <hr className="border-t border-[#707070]" />
                            
                            {/* 不正使用対策 section */}
                            <h1 className="font-normal text-[#707070] mb-4 md:mb-6 text-base sm:text-lg lg:text-[1.75rem] px-0 sm:pl-[30px]">
                                不正使用対策<sup className="text-red-500">*</sup>
                            </h1>

                            <Box className="space-y-3 sm:space-y-4 lg:space-y-6">
                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        本人認証サービス（3Dセキュア）
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                            value={formData.agxThreeDSecureStatus}
                                            onChange={(e) => handleFieldChange('agxThreeDSecureStatus', e.target.value)}
                                            options={[
                                                { value: "283260000", label: "導入済み" },
                                                { value: "283260001", label: "導入予定なし" },
                                                { value: "283260002", label: "導入予定" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxThreeDSecureStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={threeDSecureYear.toString()}
                                                    onChange={(e) => handleChangeThreeDSecureYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={threeDSecureMonth}
                                                    onChange={(e) => handleChangeThreeDSecureMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>

                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        セキュリティコードチェック
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                            value={formData.agxSecurityCodeCheckStatus}
                                            onChange={(e) => handleFieldChange('agxSecurityCodeCheckStatus', e.target.value)}
                                            options={[
                                                { value: "283260000", label: "導入済み" },
                                                { value: "283260001", label: "導入予定なし" },
                                                { value: "283260002", label: "導入予定" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxSecurityCodeCheckStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={securityCodeCheckYear.toString()}
                                                    onChange={(e) => handleChangeSecurityCodeCheckYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={securityCodeCheckMonth}
                                                    onChange={(e) => handleChangeSecurityCodeCheckMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>

                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        不正配送先情報の活用
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                            value={formData.agxIllegalDeliveryDestinationStatus}
                                            onChange={(e) => handleFieldChange('agxIllegalDeliveryDestinationStatus', e.target.value)}
                                            options={[
                                                { value: "283260000", label: "導入済み" },
                                                { value: "283260001", label: "導入予定なし" },
                                                { value: "283260002", label: "導入予定" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxIllegalDeliveryDestinationStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={illegalDeliveryDestinationYear.toString()}
                                                    onChange={(e) => handleChangeIllegalDeliveryDestinationYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={illegalDeliveryDestinationMonth}
                                                    onChange={(e) => handleChangeIllegalDeliveryDestinationMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>

                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        属性・行動分析
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                            value={formData.agxBehaviorAnalysisStatus}
                                            onChange={(e) => handleFieldChange('agxBehaviorAnalysisStatus', e.target.value)}
                                            options={[
                                                { value: "283260000", label: "導入済み" },
                                                { value: "283260001", label: "導入予定なし" },
                                                { value: "283260002", label: "導入予定" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxBehaviorAnalysisStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={behaviorAnalysisYear.toString()}
                                                    onChange={(e) => handleChangeBehaviorAnalysisYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={behaviorAnalysisMonth}
                                                    onChange={(e) => handleChangeBehaviorAnalysisMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>

                                <Box className="flex flex-col xl:grid xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="text-[#707070] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        その他の対策
                                    </Box>
                                    <Box className="flex flex-col items-start xl:items-end justify-end">
                                        <CustomSelect
                                            className="w-full h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                            value={formData.agxOtherMeasuresStatus}
                                            onChange={(e) => handleFieldChange('agxOtherMeasuresStatus', e.target.value)}
                                            options={[
                                                { value: "283260000", label: "導入済み" },
                                                { value: "283260001", label: "導入予定なし" },
                                                { value: "283260002", label: "導入予定" }
                                            ]}
                                        />
                                        <br />
                                        {formData.agxOtherMeasuresStatus === '283260002' && (
                                            <Box className="grid grid-cols-2 gap-2 mt-2 w-full">
                                                <CustomSelect
                                                    value={otherMeasuresYear.toString()}
                                                    onChange={(e) => handleChangeOtherMeasuresYear(e.target.value)}
                                                    options={generateYearOptions()}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                                <CustomSelect
                                                    value={otherMeasuresMonth}
                                                    onChange={(e) => handleChangeOtherMeasuresMonth(e.target.value)}
                                                    options={monthOptions}
                                                    className="h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] bg-white focus:border-[#1D9987] focus:outline-none transition-colors"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </Box>

                                <Box className="flex flex-col xl:flex-row gap-3 sm:gap-4 lg:gap-6">
                                    <Box className="w-full xl:w-[500px] text-[#707070] xl:pl-[210px] text-sm sm:text-base lg:text-[1.75rem] pt-2 lg:pt-[1.5rem] font-normal px-0 sm:px-4 lg:pl-[100px] flex-1">
                                        対策内容
                                    </Box>
                                    <Input 
                                        className="lg:w-[815px] h-[48px] sm:h-[52px] md:h-[60px] lg:h-[66px] border-[#707070] border-solid border-[1px] rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem] text-[#707070] placeholder:text-[#707070] placeholder:opacity-50 focus:border-[#1D9987] focus:outline-none transition-colors"
                                        value={formData.agxOtherMeasuresDescription}
                                        onChange={(e) => handleFieldChange('agxOtherMeasuresDescription', e.target.value)}
                                        placeholder="対策内容を入力してください"
                                    />
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                </Box>
            </fieldset>
            
            {/* Form buttons */}
            <FormButtons
                className="mb-32"
                onNext={() => handleSubmit()}
                onSave={showConfirmDialog}
                onBack={handleBack}
                isSubmitting={isUpdating || false}
                showBackButton={true}
            />
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                onConfirm={handleSave}
                title="入力内容を一時保存します。"
                confirmLabel="一時保存"
                confirmVariant="danger"
                cancelLabel="戻る"
                onCancel={() => setShow(false)}
            />
        </>
    );
};
