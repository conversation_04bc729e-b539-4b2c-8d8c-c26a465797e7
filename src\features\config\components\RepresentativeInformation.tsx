import { Card, CardContent } from "@/components/ui/card";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { convertDate, convertPhoneAndFax } from "@/utils/helper";
import { REPRESENTATIVE_GENDER } from "../constant";

interface RepresentativeInformationProps {
  merchantData: GetMerchantStatusResponse;
}

const startYear = 1912;
const endYear = new Date().getFullYear() * 1;

function RepresentativeInformation({ merchantData }: RepresentativeInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-14">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            お名前<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-fullh-10 rounded-md flex items-center px-3">
              <div className="w-full h-7 bg-transparent">
                {merchantData?.agxRepresentativeName ?? ""}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            お名前（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-8 pr0 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {merchantData?.agxRepresentativePhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            性別<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {REPRESENTATIVE_GENDER[merchantData?.agxRepresentativeGender ]|| ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            生年月日<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 flex space-x-1 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {convertDate(merchantData?.agxRepresentativeBirthday)[0]}年
            </div>
            <div className="w-full h-7 rounded-md px-3">
              {convertDate(merchantData?.agxRepresentativeBirthday)[1]}月
            </div>
            <div className="w-full h-7 rounded-md px-3">
              {convertDate(merchantData?.agxRepresentativeBirthday)[2]}日
            </div>
          </div>
        </div>

        {/* <div
          className="grid grid-cols-12 items-center gap-6"
          style={{
            display: merchantData?.agxBusinessForm === 283260001 ? "none" : "",
          }}
        >
          <div className="col-span-3"></div>
          <div className="col-span-6">
            <label className="flex items-center">
              <input
                id="copy_address_from_corporate2"
                type="checkbox"
                checked={merchantData?.agxStoreAddressCopyFlag1}
                disabled={true}
                className="opacity-60 mr-2"
              />
              法人所在地の入力内容をコピーする。
            </label>
          </div>
        </div> */}

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            郵便番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center text-[#6F6F6E]">
            <div className="w-full  h-7 rounded-md px-3">
              {merchantData?.agxRepresentativePostalCode ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            都道府県<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3 text-[#6F6F6E]">
            <div className="w-full  h-7 rounded-md px-3">
              {merchantData?.agxRepresentativePrefecture || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            市町村<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {merchantData?.agxRepresentativeAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            市町村（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {merchantData?.agxRepresentativePhoneticAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            丁目番地建物名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {merchantData?.agxRepresentativeAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            丁目番地建物名（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full h-7 rounded-md px-3">
              {merchantData?.agxRepresentativePhoneticAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            電話番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md pl-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativePhoneNumber
              )[0] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxRepresentativePhoneNumber)[1] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativePhoneNumber
              )[1] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxRepresentativePhoneNumber)[2] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativePhoneNumber
              )[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E] pr-3">FAX</Label>
          <div className="col-span-6 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md pl-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativeFaxNumber
              )[0] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxRepresentativeFaxNumber)[1] ?  "-" :""}</span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativeFaxNumber
              )[1] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxRepresentativeFaxNumber)[2] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(merchantData?.agxRepresentativeFaxNumber)[2] || ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default RepresentativeInformation;
