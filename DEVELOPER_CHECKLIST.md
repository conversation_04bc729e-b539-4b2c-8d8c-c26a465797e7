# Developer Checklist - Choqipay Project

## 📋 Checklist khi nhận task mới

### 🔍 **1. <PERSON><PERSON> tích yêu cầu (Requirements Analysis)**
- [ ] <PERSON><PERSON><PERSON> kỹ yêu cầu task và hiểu rõ mục tiêu
- [ ] Xá<PERSON> định loại task: UI Component, API Integration, Bug Fix, Feature Enhancement
- [ ] Kiểm tra các dependencies và constraints
- [ ] Xác định scope và boundary của task
- [ ] Thảo luận với team lead nếu có vấn đề chưa rõ

### 🏗️ **2. Thiết kế và Planning**
- [ ] Xác định cấu trúc component cần tạo/modify
- [ ] Lên kế hoạch API integration (nếu cần)
- [ ] Xác định state management strategy (Zustand store)
- [ ] Lên kế hoạch routing (nếu cần page mới)
- [ ] Xác định validation schema (Zod)
- [ ] Lên kế hoạch error handling

### 📁 **3. Cấu trúc file và folder**
- [ ] Tạo component trong đúng folder structure:
  - [ ] `src/features/[feature-name]/components/` cho components
  - [ ] `src/features/[feature-name]/hooks/` cho custom hooks
  - [ ] `src/features/[feature-name]/services/` cho API services
  - [ ] `src/features/[feature-name]/types.ts` cho TypeScript types
  - [ ] `src/features/[feature-name]/schema.ts` cho Zod schemas
- [ ] Đặt tên file theo convention: PascalCase cho components, camelCase cho others
- [ ] Export components sử dụng Named Export thay vì Default Export

### 🎨 **4. UI/UX Implementation**
- [ ] Sử dụng Shadcn UI components có sẵn
- [ ] Áp dụng Tailwind CSS classes cho styling
- [ ] Đảm bảo responsive design (mobile-first approach)
- [ ] Kiểm tra accessibility (ARIA labels, keyboard navigation)
- [ ] Sử dụng consistent color scheme và spacing
- [ ] Test trên các screen sizes khác nhau

### 🔧 **5. TypeScript Implementation**
- [ ] Định nghĩa interfaces/types cho props và data structures
- [ ] Sử dụng strict typing, tránh `any` type
- [ ] Implement proper type guards nếu cần
- [ ] Sử dụng generics cho reusable components
- [ ] Kiểm tra TypeScript compilation không có errors

### 📝 **6. Form Handling & Validation**
- [ ] Sử dụng React Hook Form cho form management
- [ ] Implement Zod schema validation
- [ ] Xử lý form errors và display user-friendly messages
- [ ] Implement proper form submission handling
- [ ] Kiểm tra form accessibility

### 🔄 **7. State Management**
- [ ] Sử dụng Zustand cho global state management
- [ ] Implement proper state persistence nếu cần
- [ ] Sử dụng React Query cho server state management
- [ ] Implement optimistic updates nếu cần
- [ ] Kiểm tra state synchronization

### 🌐 **8. API Integration**
- [ ] Sử dụng `apiService` từ `src/services/api.ts`
- [ ] Implement proper error handling cho API calls
- [ ] Sử dụng TanStack Query cho data fetching
- [ ] Implement loading states và error states
- [ ] Kiểm tra API response format và validation

### 🔐 **9. Authentication & Authorization**
- [ ] Kiểm tra authentication requirements
- [ ] Implement proper route protection nếu cần
- [ ] Sử dụng `useAuthStore` cho user state
- [ ] Kiểm tra role-based access control
- [ ] Implement proper logout handling

### 🛣️ **10. Routing**
- [ ] Thêm routes vào `src/routers/privateRoutes.tsx` hoặc `publicRoutes.tsx`
- [ ] Implement proper route protection
- [ ] Kiểm tra navigation flow
- [ ] Implement proper breadcrumb nếu cần

### 🧪 **11. Testing & Quality Assurance**
- [ ] Test component rendering
- [ ] Test user interactions (click, input, form submission)
- [ ] Test error scenarios
- [ ] Test loading states
- [ ] Test responsive behavior
- [ ] Kiểm tra console errors/warnings

### 🚀 **12. Performance Optimization**
- [ ] Sử dụng React.memo cho components nếu cần
- [ ] Implement lazy loading cho heavy components
- [ ] Optimize re-renders
- [ ] Kiểm tra bundle size impact
- [ ] Implement proper cleanup trong useEffect

### 🔍 **13. Code Quality**
- [ ] Chạy ESLint: `npm run lint`
- [ ] Kiểm tra TypeScript compilation
- [ ] Format code theo project conventions
- [ ] Kiểm tra naming conventions:
  - [ ] PascalCase cho components
  - [ ] camelCase cho variables/functions
  - [ ] UPPER_SNAKE_CASE cho constants
- [ ] Remove unused imports và variables
- [ ] Add proper JSDoc comments cho complex functions
- [ ] Clear console.log
- [ ] Sử dụng import alias

### 📱 **15. Mobile & Cross-browser Testing**
- [ ] Test trên Chrome, Firefox, Safari, Edge
- [ ] Test trên mobile devices (iOS, Android)
- [ ] Kiểm tra touch interactions
- [ ] Test responsive breakpoints

### 🔧 **16. Build & Deployment**
- [ ] Test build process: `npm run build`
- [ ] Kiểm tra production build
- [ ] Test staging environment nếu có
- [ ] Kiểm tra environment variables
- [ ] Verify API endpoints configuration

### 📚 **17. Documentation**
- [ ] Update component documentation nếu cần
- [ ] Add comments cho complex logic
- [ ] Update README nếu có thay đổi setup
- [ ] Document any new environment variables

### 🚀 **18. Pre-deployment Checklist**
- [ ] Code review completed
- [ ] All tests passing
- [ ] No console errors/warnings
- [ ] Performance acceptable
- [ ] Accessibility requirements met
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility checked

### 🎯 **19. Post-deployment Verification**
- [ ] Verify feature works in production
- [ ] Check for any runtime errors
- [ ] Monitor performance metrics
- [ ] Verify user feedback
- [ ] Update task status

---

## 🛠️ **Development Tools & Commands**

### Essential Commands:
```bash
# Development
npm run dev          # Start development server
npm run stg          # Start staging server

# Build
npm run build        # Production build
npm run build:dev    # Development build
npm run build:stg    # Staging build

```

### Key Technologies:
- **Frontend**: React 18 + TypeScript + Vite
- **UI Library**: Shadcn UI + Radix UI + Tailwind CSS
- **State Management**: Zustand + TanStack Query
- **Form Handling**: React Hook Form + Zod
- **Routing**: React Router DOM
- **HTTP Client**: Axios
- **Build Tool**: Vite

### Project Structure:
```
src/
├── components/          # Shared UI components
├── features/           # Feature-based modules
│   ├── [feature]/
│   │   ├── components/ # Feature-specific components
│   │   ├── hooks/      # Custom hooks
│   │   ├── services/   # API services
│   │   ├── types.ts    # TypeScript types
│   │   └── schema.ts   # Zod schemas
├── pages/              # Page components
├── routers/            # Routing configuration
├── services/           # Shared services
├── store/              # Global state stores
├── types/              # Global types
└── utils/              # Utility functions
```

---

## 📝 **Notes**
- Luôn follow TypeScript best practices
- Sử dụng Named Exports thay vì Default Exports
- Implement proper error boundaries
- Test thoroughly trước khi submit PR
- Communicate với team nếu có blockers
- Keep code clean và maintainable

---

*Last updated: [Date]*
*Version: 1.0* 