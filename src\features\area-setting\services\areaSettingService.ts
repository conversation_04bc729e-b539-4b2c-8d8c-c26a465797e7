import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { AgxAreaParam, AgxSubAreaParam, GetAreaSettingResponse } from "../type";

class AreaSettingService{
    async getAllData(merchantNo : string): Promise< GetAreaSettingResponse >{
        const response = await apiService.get<{ data: GetAreaSettingResponse }>(API_ENDPOINTS.AREA_SETTING.GET_DATA(merchantNo));
        return response.data;
    }

    async createArea(agxArea: AgxAreaParam) : Promise<any>{
        const response = await apiService.post<any>(API_ENDPOINTS.AGX_AREA.DEFAULT, agxArea);
        return response.data;
    }

    async deleteArea(id: string) : Promise<any>{
        const response = await apiService.delete<any>(API_ENDPOINTS.AGX_AREA.WITH_ID(id));
        return response.data;
    }

    async createSubArea(agxSubArea: AgxSubAreaParam) : Promise<any>{
        const response = await apiService.post<any>(API_ENDPOINTS.AGX_SUB_AREA.DEFAULT, agxSubArea);
        return response.data;
    }

    async deleteSubArea(id: string) : Promise<any>{
        const response = await apiService.delete<any>(API_ENDPOINTS.AGX_SUB_AREA.MODIFY(id));
        return response.data;
    }

}
export const areaSettingService = new AreaSettingService;
