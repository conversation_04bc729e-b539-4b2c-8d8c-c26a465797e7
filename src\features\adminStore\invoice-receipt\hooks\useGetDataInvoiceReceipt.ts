import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { invoiceService } from '../services/invoiceReceiptService';
import { InvoiceReceiptResponse } from '../types';

export const useGetDataInvoiceReceipt = () => {
  const { user } = useAuthStore();

  return useQuery({
    queryKey: ['admin-invoice-receipt', user?.agxMerchantNo],
    queryFn: async (): Promise<InvoiceReceiptResponse> => {
      if (!user?.agxMerchantNo) {
        throw new Error('Merchant number not found');
      }
      return await invoiceService.getDataInvoiceReceipt(user.agxMerchantNo);
    },
    enabled: !!user?.agxMerchantNo,
  });
}; 