import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import React from 'react'

interface IProps {
  agxSubArea: any;
  dataAreaSetting: any;
  showSubAreaModal: boolean;
  setShowSubAreaModal: (v: boolean) => void;
  handleChangeSubArea: any;
  handleSelectMerchant: any;
  handleCreateOrUpdateSubArea: any;
}

export const SubAreaModal = ({ agxSubArea, dataAreaSetting, showSubAreaModal, setShowSubAreaModal, handleChangeSubArea, handleSelectMerchant, handleCreateOrUpdateSubArea }: IProps) => {
  return (
    <Dialog open={showSubAreaModal} onOpenChange={setShowSubAreaModal}>
      <DialogContent className="w-full max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">サブエリアの作成</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <form>
          <div className="mb-6">
            <label htmlFor="subarea-name" className="block text-gray-700 font-medium mb-2">新規に作成するサブエリアの名前を入力してください。</label>
            <input
              type="text"
              id="subarea-name"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none"
              value={agxSubArea.agxSubAreaName}
              onChange={handleChangeSubArea}
            />
          </div>
          <div className="mb-2">
            <label className="block text-gray-700 font-medium mb-2">サブエリアに紐付けする店舗を選択して下さい。</label>
            <div className="border rounded-md overflow-hidden">
              {/* Fixed Header */}
              <div className="bg-gray-50 border-b">
                <div className="grid grid-cols-[48px_1fr_1fr_1fr] gap-0">
                  <div className="px-4 py-3 text-center font-medium text-gray-700"></div>
                  <div className="px-4 py-3 text-center font-medium text-gray-700">加盟店番号</div>
                  <div className="px-4 py-3 text-center font-medium text-gray-700">店舗名</div>
                  <div className="px-4 py-3 text-center font-medium text-gray-700">サブエリア名</div>
                </div>
              </div>

              {/* Scrollable Body */}
              <div className="max-h-80 overflow-y-auto">
                <div className="divide-y divide-gray-200">
                  {dataAreaSetting?.agxSubAreaModal?.map((item, index) => (
                    <div key={index} className="grid grid-cols-[48px_1fr_1fr_1fr] gap-0 hover:bg-gray-50 transition-colors">
                      <div className="px-4 py-3 text-center flex items-center justify-center">
                        <input
                          id={item.agxMerchantNo}
                          type="checkbox"
                          className="form-checkbox h-5 w-5 text-blue-600 rounded"
                          checked={!!agxSubArea.agxMerchantNos?.find((m) => m.agxMerchantNo === item.agxMerchantNo && m.isSelected)}
                          onChange={(e) => handleSelectMerchant(e, item.agxMerchantNo, item.agxSubAreaid)}
                        />
                      </div>
                      <div className="px-4 py-3 text-center flex items-center justify-center">{item.agxMerchantNo}</div>
                      <div className="px-4 py-3 text-center flex items-center justify-center">{item.agxStoreName}</div>
                      <div className="px-4 py-3 text-center flex items-center justify-center">{item.agxSubAreaName}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </form>

        <DialogFooter className="flex justify-end gap-2 mt-6">
          <Button
            className="text-[20px] w-[20%] max-md:w-full h-[48px] bg-gray-400 text-white rounded-[8px] hover:bg-gray-500 transition-colors"
            onClick={() => setShowSubAreaModal(false)}>閉じる</Button>
          <Button
            className="bg-[#19A492] text-white font-medium px-8 py-2 rounded-md h-[48px] hover:bg-[#15b19d] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-[18px]"
            onClick={handleCreateOrUpdateSubArea}>作成</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
