export interface InvoiceReceiptResponse {
  data: Array<{
    year?: number;
    month?: number;
    total?: number;
    invoiceUrl?: string;
    receiptUrl?: string;
    deadlineDate?: string;
  }>;
}

export interface InvoiceMonthlyResponse {
  data: InvoiceMonthlyType;
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}
export interface InvoiceMonthlyType {
  dataInvoices: dataInvoices[];
  invoiceCreatedDate: string;
  invoiceDeadlineDate: string;
  merchantPayments: MerchantPayment[];
  subTotal: number;
  tax: number;
  total: number;
}

export interface MerchantPayment {
  merchantNo: string;
  paymentAmount: number;
  salesAmount: number;
  storeName: string;
  sumTax: number;
}

export interface dataInvoices {
  amount: number;
  name: string;
  no: string;
  quantity: number;
  unitPrice: number;
  type: string;
}

export interface ReceiptMonthlyResponse {
  data: ReceiptMonthlyType;
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

export interface ReceiptMonthlyType {
  invoiceCreatedDate: string;
  paymentDate: string;
  subTotal: number;
  tax: number;
  total: number;
}

export interface CreditCardMonthlyFeeResponse {
  data: CreditCardMonthlyFeeType[];
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

export interface CreditCardMonthlyFeeType {
  amount: number;
  merchantNo: string;
  price: number;
  quantity: number;
  storeName: string;
}

export interface MonthlyCostByStoreResponse {
  data: MonthlyCostByStoreType;
  totalPage: number;
  totalElement: number;
  hasNext: boolean;
}

export interface MonthlyCostByStoreType {
  monthlyCostByStore: MonthlyCostByStore[];
  storeName: string;
  subTotal: number;
  tax: number;
  total: number;
}
export interface MonthlyCostByStore {
  amount: number;
  name: string;
  quantity: number;
  unitPrice: number;
}