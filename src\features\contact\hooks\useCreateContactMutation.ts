import { useMutation } from "@tanstack/react-query";
import DocumentDownloadService from "@/features/contact/services/contactService";

const contactService = new DocumentDownloadService();

export const useCreateContactMutation = () => {
  const contactMutation = useMutation({
    mutationFn: (requestBody: any) => contactService.create(requestBody),
  });

  return {
    createContact: contactMutation.mutate,
    createContactAsync: contactMutation.mutateAsync,
    isLoading: contactMutation.isPending,
    isError: contactMutation.isError,
    error: contactMutation.error,
    reset: contactMutation.reset,
  };
};
