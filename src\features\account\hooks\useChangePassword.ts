
import { useMutation } from "@tanstack/react-query"
import accountService from "../services/accountService";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

export const useChangePassword = () => {
    const { toast } = useToast();
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    const {mutate, isPending,mutateAsync, isError, error, reset } = useMutation({
        mutationFn: async (data: { contactId: string; oldPassword: string; newPassword: string; confirmPassword: string }) => {
            return await accountService.changePassword(data)
        },
        onSuccess: () => {
            setErrorMessage(null);
            toast({
                title: "パスワード変更成功",
                description: "パスワードが正常に変更されました",
                duration: 2000,
            })
        },
        onError: (error: any) => {
            // Handle specific error codes from server
            if (error?.response?.data?.statusCode === 40) {
                setErrorMessage("※新しいパスワードを前のパスワードと同じにすることはできません。");
            } else if (error?.response?.data?.statusCode === 50) {
                setErrorMessage("※旧パスワードは正しくないので、再度ご確認お願い致します。");
            } else {
                setErrorMessage(error?.response?.data?.message || "パスワードの変更に失敗しました");
            }
        }
    })

    return {
        changePassword: mutate,
        changePasswordAsync: mutateAsync,
        isLoading: isPending,
        isError,
        error,
        reset,
        errorMessage,
    }
}