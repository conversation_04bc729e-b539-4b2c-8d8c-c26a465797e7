import axios from '@/config/axios';
import { AdminDepositData, SelectorData, AdminDepositDetailData } from '../types';
import { API_ENDPOINTS } from '@/config/api-endpoints';

export class AdminDepositService {
  async getDateAdminStoreFromAgxPaymentManagement(agxMerchantNo: string): Promise<string[]> {
    const response = await axios.get(API_ENDPOINTS.ADMIN_DEPOSIT.DATES(agxMerchantNo));
    return response.data.data;
  }

  async getDataSelector(agxMerchantNo: string): Promise<SelectorData> {
    const response = await axios.get(API_ENDPOINTS.ADMIN_DEPOSIT.SELECTOR(agxMerchantNo));
    return response.data.data;
  }

  async getDataAdminDeposit(
    agxMerchantNoEncode: string,
    date: string = '',
    area: string = '',
    subArea: string = '',
    merchantNo: string = ''
  ): Promise<AdminDepositData> {

    const response = await axios.get(API_ENDPOINTS.ADMIN_DEPOSIT.DATA(agxMerchantNoEncode, date, area, subArea, merchantNo));
    return response.data.data;
  }

  async getDataAdminDepositDetail(
    agxMerchantNo: string,
    transferDate: string,
    transactionType: string = '',
    merchantNo: string = '',
    paymentBId: string = '',
    area: string = '',
    subArea: string = ''
  ): Promise<AdminDepositDetailData> {
    const response = await axios.get(API_ENDPOINTS.ADMIN_DEPOSIT.DETAIL_DATA(agxMerchantNo, transferDate, transactionType, merchantNo, paymentBId, area, subArea));
    if (paymentBId && transactionType && merchantNo) {
      const filteredData = response.data.data.data.filter((item: any) => {
        return item.merchantNo === merchantNo &&
               item.transactionType === transactionType;
        // Note: paymentBId filtering would depend on the actual API response structure
      });

      if (filteredData.length > 0) {
        return {
          data: filteredData,
          total: filteredData.length,
          totalSales: filteredData.reduce((sum: number, item: any) => sum + (item.salesAmount || 0), 0)
        };
      }
    }

    return response.data.data;
  }
}

export const adminDepositService = new AdminDepositService();
