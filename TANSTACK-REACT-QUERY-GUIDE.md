
# 📘 TanStack React Query Cheatsheet (v4+)

React Query (nay là **TanStack Query**) là thư viện giúp quản lý server-state (dữ liệu từ API) một cách tối ưu trong ứng dụng React.

---

## 🔹 `useQuery` – Fetch dữ liệu

```tsx
const { data, isLoading, isError, error, refetch } = useQuery({
  queryKey: ['todos'],
  queryFn: () => fetchTodos(),
});
```

### ✅ Các thuộc tính chính:
| Thuộc tính      | Kiểu dữ liệu        | Giải thích |
|----------------|---------------------|------------|
| `queryKey`     | `Array | string`    | Dùng để định danh duy nhất cho query. Bắt buộc. |
| `queryFn`      | `Function`          | Hàm async để gọi API. Bắt buộc. |
| `enabled`      | `boolean`           | Mặc định `true`. Nếu `false` → không fetch tự động. |
| `staleTime`    | `number` (ms)       | Thời gian dữ liệu được xem là **fresh** (0 = stale ngay). |
| `cacheTime` / `gcTime` | `number` (ms) | Bao lâu thì cache bị xóa sau khi không dùng (default 5 phút). |
| `retry`        | `boolean | number | (count, error) => boolean` | Retry khi `queryFn` fail. Mặc định 3 lần. |
| `retryDelay`   | `number | (attempt) => number` | Thời gian giữa mỗi lần retry. |
| `refetchOnMount` | `boolean | 'always'` | Refetch khi component mount lại. Default: `true` |
| `refetchOnWindowFocus` | `boolean`     | Refetch khi quay lại tab. Default: `true` |
| `refetchOnReconnect` | `boolean`      | Refetch khi mất mạng và kết nối lại. Default: `true` |
| `select`       | `(data) => any`     | Cho phép transform dữ liệu trước khi gán vào `data`. |
| `onSuccess`    | `(data) => void`    | Gọi khi query thành công. |
| `onError`      | `(error) => void`   | Gọi khi query lỗi. |
| `initialData`  | `any`               | Dữ liệu ban đầu nếu chưa có trong cache. |
| `keepPreviousData` | `boolean`       | Giữ data cũ khi queryKey thay đổi. |

---

## 🔹 `useMutation` – Thao tác ghi (POST, PUT, DELETE...)

```tsx
const mutation = useMutation({
  mutationFn: (newTodo) => createTodo(newTodo),
  onSuccess: () => queryClient.invalidateQueries(['todos'])
});

mutation.mutate({ text: 'Learn React Query' });
```

### ✅ Các tham số:
| Thuộc tính      | Kiểu dữ liệu       | Giải thích |
|----------------|--------------------|------------|
| `mutationFn`   | `Function`         | Bắt buộc. Hàm async xử lý mutate |
| `onSuccess`    | `(data, variables, context) => void` | Gọi khi mutate thành công |
| `onError`      | `(error, variables, context) => void` | Khi lỗi |
| `onSettled`    | `(data | error, variables, context) => void` | Dù thành công hay thất bại đều gọi |
| `retry`        | `boolean | number` | Retry khi lỗi |
| `retryDelay`   | `number`           | Delay giữa các lần retry |
| `mutationKey`  | `string`           | Dùng để định danh mutation nếu cần |

### ✅ Các state:
| Biến              | Giải thích |
|-------------------|-----------|
| `mutation.isPending` | Đang thực hiện |
| `mutation.isSuccess` | Thành công |
| `mutation.isError`   | Lỗi |
| `mutation.error`     | Thông tin lỗi |
| `mutation.data`      | Kết quả trả về |

---

## 🔹 `useQueryClient()` – Truy cập & thao tác cache

```tsx
const queryClient = useQueryClient();
```

### ⚙️ Các hàm thường dùng:
| Hàm | Mục đích |
|-----|----------|
| `getQueryData(queryKey)` | Lấy cache hiện tại |
| `setQueryData(queryKey, updater)` | Gán dữ liệu cache |
| `invalidateQueries(queryKey)` | Đánh dấu query stale → refetch |
| `refetchQueries(queryKey)` | Refetch ngay |
| `removeQueries(queryKey)` | Xóa khỏi cache |

---

## 🔹 Các trạng thái trong `useQuery`

| Biến trạng thái | Ý nghĩa |
|----------------|--------|
| `isLoading`    | Query đang lần đầu gọi hoặc chưa có cache |
| `isFetching`   | Bất kỳ lúc nào queryFn đang gọi API (kể cả sau `refetch`) |
| `isRefetching` | Đang gọi lại sau khi đã có dữ liệu (refetch từ cache) |
| `isSuccess`    | Query thành công |
| `isError`      | Query lỗi |
| `error`        | Đối tượng lỗi |

---

## 🔹 Lifecycle flow của `useQuery`
1. Lần đầu render → gọi `queryFn` nếu `enabled: true`
2. Dữ liệu fetch thành công → `data` được cache
3. Nếu `staleTime` hết hạn → query bị mark `stale`
4. Nếu có trigger (focus, reconnect, refetch) → sẽ gọi lại query
5. Nếu `gcTime` hết sau khi unmount → cache bị xóa

---

## 🔹 Tips & Best Practices
- Luôn đưa **tham số động** vào `queryKey` để cache đúng
- Dùng `select` để transform `data` tránh xử lý trong UI
- Sử dụng `keepPreviousData: true` khi paginating
- Tắt `refetchOnWindowFocus` nếu app bạn không cần realtime
- Xài `useMutation` cho POST/PUT/DELETE thay vì dùng `useQuery`

---

## 📌 Tổng kết thường dùng
```tsx
// useQuery
const query = useQuery({
  queryKey: ['key', param],
  queryFn: () => fetchData(param),
  staleTime: 5 * 60 * 1000,
  gcTime: 10 * 60 * 1000,
  enabled: !!param,
});

// useMutation + invalidate cache
const queryClient = useQueryClient();
const mutation = useMutation({
  mutationFn: updateItem,
  onSuccess: () => queryClient.invalidateQueries(['items'])
});
```

---

Nếu bạn cần phần mở rộng như `useInfiniteQuery`, `prefetchQuery`, hoặc quản lý offline mode, hãy hỏi thêm nhé.
