import React from 'react';
import { Link } from 'react-router-dom';
import { TerminalManagementProps } from '../types';

export const TerminalManagement: React.FC<TerminalManagementProps> = ({ 
  id, 
  password, 
  isAdmin = false, 
  className = "" 
}) => {
  return (
    <div className={className}>
      <div className="space-y-2 mb-4 px-8">
        <p className={`text-[20px] text-gray-500`}>
          ログインID : {id || ''}
        </p>
        <p className={`text-[20px] text-gray-500`}>
          初回パスワード : {password || ''}
        </p>
      </div>
      <div className="ml-8 mb-4 space-y-2">
          <div>
            <Link
              to="https://member.paygate.ne.jp/login"
              target="_blank"
              rel="noopener noreferrer"
              className="text-teal-500 hover:text-teal-800 underline text-base"
            >
              管理画面はこちらから
            </Link>
            <span className="text-sm text-gray-500 ml-2">
              | 端末管理画面は日々の決済情報の確認等ができます。
            </span>
          </div>
      </div>
    </div>
  );
};
