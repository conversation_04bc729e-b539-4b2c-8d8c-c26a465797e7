---
description: 
globs: 
alwaysApply: true
---
---
description: Specific guidelines for TypeScript usage, including strict typing and interface usage.
globs: **/*.{ts,tsx}
---
- Utilize TypeScript's features to ensure type safety.
- Prefer interfaces over types when defining object shapes.
- Use generics to create reusable components and functions.

- Enforce strict typing and avoid 'any' type as much as possible.
- Dùng Named Export (ví dụ : export const userService = new UserService()) thay vì Default Export (export default new UserService()).