import { useQuery } from '@tanstack/react-query';
import { depositService } from '@/features/store/deposit/services/depositService';

/**
 * Hook for managing deposit detail data for all transactions on a specific date
 * Returns ALL transactions for a transfer date (many records, various transaction types)
 */
export const useDepositDetailAll = (
  merchantNo: string,
  paymentDate: string
) => {
  const {
    data: detailAllResponse,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ['deposit-paygate-detail-all', merchantNo, paymentDate],
    queryFn: () => depositService.getDataStoreDepositDetail(merchantNo, paymentDate),
    enabled: !!merchantNo && !!paymentDate,
    staleTime: 0,
    gcTime: 0,
  });

  // Convert query error to string for compatibility
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'データの取得に失敗しました') : null;

  return {
    depositDetailAll: detailAllResponse,
    isLoading,
    error,
    refetch
  };
};
