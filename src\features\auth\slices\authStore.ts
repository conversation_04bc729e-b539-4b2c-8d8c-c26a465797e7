import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/features/auth/types';
import { AccountTypes } from '@/types/globalType';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  setUser: (user: User | null) => void;
  clearUser: () => void;
  updateUser: (updates: Partial<User>) => void;
  typeStore: number | null;
  setTypeStore: (typeStore: number | null) => void;
  clearTypeStore: () => void;
  setAccountType: (accountType: AccountTypes) => void;
  setAgxMerchantNo: (agxMerchantNo: string) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      typeStore: null,
      setUser: (user: User | null) => set({ 
        user, 
        isAuthenticated: !!user 
      }),

      clearUser: () => set({ 
        user: null, 
        isAuthenticated: false 
      }),

      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates }
          });
        }
      },

      setTypeStore: (typeStore: number | null) => set({ typeStore }),

      clearTypeStore: () => set({ typeStore: null }),

      setAccountType: (accountType: AccountTypes) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, statusAccount: accountType }
          });
        }
      },

      setAgxMerchantNo: (agxMerchantNo: string) => {
        const currentUser = get().user;
        if (currentUser) {
          set({ user: { ...currentUser, agxMerchantNo } });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        typeStore: state.typeStore
      }),
    }
  )
); 