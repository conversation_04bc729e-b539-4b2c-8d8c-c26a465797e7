import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/store';
import { ReceiptData } from '@/features/store/invoice-receipt/types';
import { receiptMonthlyService } from '../services/receiptMonthlyService';

interface UseStoreReceiptReturn {
    data: ReceiptData | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
}

export const useStoreReceiptMonthly = (invoiceNo: string | undefined): UseStoreReceiptReturn => {
    const { user } = useAuthStore();

    const {
        data = null,
        isLoading: loading,
        error,
        refetch
    } = useQuery({
        queryKey: ['store-receipt-monthly', user?.agxMerchantNo, invoiceNo],
        queryFn: async () => {
            if (!invoiceNo) {
                throw new Error('請求書番号が見つかりません。');
            }
            if (!user?.agxMerchantNo) {
                throw new Error('マーチャント番号が見つかりません。');
            }
            
            const result = await receiptMonthlyService.getData(btoa(user.agxMerchantNo), invoiceNo);
            return result;
        },
        enabled: !!invoiceNo && !!user?.agxMerchantNo,
        staleTime: 0,
        gcTime: 0,
    });

    return { 
        data, 
        loading, 
        error: error?.message || null, 
        refetch 
    };
}; 