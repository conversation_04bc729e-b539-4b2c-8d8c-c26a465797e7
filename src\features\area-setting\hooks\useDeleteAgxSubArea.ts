import { useToast } from '@/hooks/use-toast';
import { useMutation } from '@tanstack/react-query';
import { areaSettingService } from '../services/areaSettingService';
import { AgxAreaParam } from '../type';

export const useDeleteAgxSubArea = () => {
    const { toast } = useToast();
    const createArea =  useMutation({
        mutationFn: async (id: string) => {
            await areaSettingService.deleteSubArea(id);
        },
        onSuccess: () => {
            toast({
                variant: "default",
                title: '削除が完了しました。',
                description: "",
            });
        },
        onError: () => {
            toast({
                variant: "destructive",
                title: '削除が失敗しました。',
                description: "",
            });
        }
    });
    return {
        deleteAgxSubArea: createArea.mutate,
        deleteAgxSubAreaAsync: createArea.mutateAsync,
        isLoading: createArea.isPending,
        isError: createArea.isError,
        error: createArea.error,
        reset: createArea.reset,
    };
};
