import React from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, PointElement, LineElement, registerables } from 'chart.js';
import { Chart } from 'react-chartjs-2';
import { Card, CardContent } from '@/components/ui/card';

// Register Chart.js components
ChartJS.register(...registerables);

interface SummaryChartProps {
  chartLabel: string[];
  lineData: number[];
  creditData: number[];
  electronicData: number[];
  qrData: number[];
}

const SummaryChart: React.FC<SummaryChartProps> = ({
  chartLabel,
  lineData,
  creditData,
  electronicData,
  qrData
}) => {
  // Chart data configuration
  const chartData = {
    labels: chartLabel || [],
    datasets: [
      {
        type: "line" as const,
        backgroundColor: "#FF7F50",
        borderColor: "#FF7F50",
        borderWidth: 3,
        label: "売上件数",
        yAxisID: 'y1',
        data: lineData || [],
        tension: 0, // 曲線の彎度，設 0 表示直線
        fill: false // 是否填滿色彩
      },
      {
        type: "bar" as const,
        backgroundColor: "rgba(54, 162, 235, 1)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
        label: "クレジットカード決済",
        yAxisID: 'y',
        data: creditData || [],
      },
      {
        type: "bar" as const,
        backgroundColor: "rgba(52, 235, 73, 0.6)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
        label: "電子マネー決済",
        yAxisID: 'y',
        data: electronicData || [],
      },
      {
        type: "bar" as const,
        backgroundColor: "rgba(52, 168, 235, 0.6)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
        label: "QRコード決済",
        yAxisID: 'y',
        data: qrData || [],
      },
    ],
  };

  // Chart options
  const chartOptions: any = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: { beginAtZero: true },
        stacked: true,
      },
      y:
      {
        id: '売上件数',
        position: 'left',
        ticks: {
          callback: function (label, index, labels) {
            return Number(label).toLocaleString();
          },
          min: 0
        },
        grid: {
          borderDash: [5, 10]
        },
        stacked: true
      },
      y1: {
        id: '売上金額',
        position: 'right',
        ticks: {
          callback: function (label, index, labels) {
            return Number(label).toLocaleString();
          },
          min: 0
        },
        grid: {
          drawOnChartArea: false,
          borderDash: [8, 4],
        },
        stacked: true
      }
    },
  };

  return (
    <Card className="mb-4 sm:mb-6 border-0 shadow-none">
      <CardContent className="p-0 pt-4 sm:pt-6">
        <div className="h-96">
          <Chart type="bar" data={chartData} options={chartOptions} />
        </div>
      </CardContent>
    </Card>
  );
};

export default SummaryChart;
