import Box from "@/components/ui/box";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Link } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import { moneyData } from "../utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { FormButtons } from "./ui/FormButtons";
import { STEP } from "@/constants/common.constant";
import { FormField } from "./ui/Field";
import { choqipayCreficoSchema, type ChoqipayCreficoFormData } from "../schemas/choqipayCrefico.schema";
import ConfirmDialog from "@/components/ConfirmDialog";

export const ChoqiPayCrepico = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {

  const [initialFee, setInitialFee] = useState(0);
  const [monthlyFee, setMonthlyFee] = useState(0);
  const [show, setShow] = useState(false);

  const { register, handleSubmit, formState: { errors }, trigger, setValue, watch, getValues } = useForm<ChoqipayCreficoFormData>({
    resolver: zodResolver(choqipayCreficoSchema),
    mode: "onBlur",
    defaultValues: {
      agxNumberOfTerminal: 1,
      agxSettlementPackage1: true,
      agxSettlementPackage2: false,
    },
  });

  const caclMoney = useCallback((agxNumberOfTerminal: number, agxSettlementPackage1: boolean, agxSettlementPackage2: boolean) => {
    const isCrepico = !agxSettlementPackage1 && !agxSettlementPackage2 ? true : agxSettlementPackage1;
    const moneyCrepico = agxNumberOfTerminal * ((isCrepico ? moneyData.money_Credit_Card_And_QRcode : 0) + (agxSettlementPackage2 ? moneyData.money_All_Crepico : 0));
    setInitialFee(agxNumberOfTerminal * moneyData.money_core);
    setMonthlyFee(moneyCrepico);
  }, []);

  const handleChangeAgxNumberOfTerminal = (e) => {
    const value = parseInt(e.target.value, 10);
    setValue('agxNumberOfTerminal', value);
    trigger('agxNumberOfTerminal');
    if (Number.isInteger(value)) {
      caclMoney(value, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2);
    }
  };

  const onSubmit = async () => {

    const isValid = await trigger();
    if (!isValid) {
        return false;
    }

    setAgxMerchantParams({
      ...agxMerchantParams,
      ...getValues(),
    } as AgxMerchantParams);

    setStep(STEP.KIYAKU);
  };

  const handleChangeAgxSettlementPackage = (packageType: 'package1' | 'package2') => {
    const terminalCount = getValues().agxNumberOfTerminal;
    if (packageType === 'package1') {
      setValue('agxSettlementPackage1', true);
      setValue('agxSettlementPackage2', false);
      caclMoney(terminalCount, true, false);
    } else {
      setValue('agxSettlementPackage1', false);
      setValue('agxSettlementPackage2', true);
      caclMoney(terminalCount, false, true);
    }
  };

  const showConfirmDialog = async () => {
    const isValid = await trigger();
    if (!isValid) {
      return false;
    }
    setShow(true);
  }

  const onSave = () => {
    updateMerchant(
      {
        ...agxMerchantParams,
        ...getValues(),
      }
    );
    setShow(false);
  };

  const handleBack = () => {
    setAgxMerchantParams({
      ...agxMerchantParams,
      ...getValues(),
    } as AgxMerchantParams);

    setStep(STEP.ADDITIONAL1);
  };

  useEffect(() => {
    if(agxMerchantParams) {
      setValue('agxNumberOfTerminal', agxMerchantParams?.agxNumberOfTerminal);
      setValue('agxSettlementPackage1', agxMerchantParams?.agxSettlementPackage1);
      setValue('agxSettlementPackage2', agxMerchantParams?.agxSettlementPackage2);
      if(!agxMerchantParams?.agxSettlementPackage1 && !agxMerchantParams?.agxSettlementPackage2) {
        setValue('agxSettlementPackage1', true);
        setValue('agxSettlementPackage2', false);
      }
      if (agxMerchantParams?.agxNumberOfTerminal != null) {
        caclMoney(agxMerchantParams?.agxNumberOfTerminal, agxMerchantParams?.agxSettlementPackage1, agxMerchantParams?.agxSettlementPackage2);
      }
    }
  }, [agxMerchantParams, setValue]);

  return (
    <>
      <Box className="mx-auto space-y-6 md:space-y-8 lg:space-y-10 sm:mb-10 mt-6 sm:mt-10 px-4 sm:px-0">
        {/* 端末台数 Section */}
        <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
          <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 w-full">
            <FormField label="端末台数" required />
            <Box className="w-full sm:w-48 md:w-64">
              <Input
                type="number"
                {...register("agxNumberOfTerminal")}
                min={1}
                max={10}
                onBlur={handleChangeAgxNumberOfTerminal}
                className="w-full rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem]"
              />
              {errors.agxNumberOfTerminal && (
                <span className="text-red-500 text-sm font-bold">
                  {errors.agxNumberOfTerminal.message?.toString()}
                </span>
              )}
            </Box>
            <span className="text-[#707070] text-base sm:text-[1.75rem] font-normal">
              台
            </span>
            <span className="text-[#707070] text-base sm:text-[1.75rem] font-normal">|</span>
            <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal">
            ※99,800円 (税抜) / 台
          </Box>
          </Box>
          
        </Box>

        {/* ご利用になる端末 Section */}
        <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
          <FormField label="端末" required />
          <Box className="text-[#707070] text-base sm:text-[1.75rem]">
          CREPiCO AT-M100
          </Box>
          <Link
            to="#"
            onClick={() => {
              window.open("https://www.crepico.co.jp/", "_blank");
            }}
            className="text-[#19A492] text-base sm:text-[1.75rem] font-normal underline"
          >
            説明資料はこちらから
          </Link>
        </Box>

        {/* 決済種別 Section */}
        <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
          <FormField label="決済種別" required />
          <Box className="text-[#FF0002] text-base sm:text-[1.75rem] font-normal">
            決済種別により、月額費用（税抜）が発生します。ページ下部をご確認ください
          </Box>
        </Box>

        {/* Package Selection - Checkboxes */}
        <Box className="space-y-6">
          {/* 基本プラン - Credit Card + QR Code */}
          <Box className="px-0 sm:ml-[16%] items-center justify-start">
            <Box className="flex gap-3 sm:gap-4 items-center">
              <Checkbox
                id="package1"
                checked={getValues().agxSettlementPackage1}
                onCheckedChange={() => handleChangeAgxSettlementPackage('package1')}
                className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
              />
              <Label
                htmlFor="package1"
                className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
              >
                基本プラン：クレジットカード・QRコード決済（500円/月・直近3ヶ月内のご利用で0円）
              </Label>
            </Box>
          </Box>

          <hr className="w-full sm:w-[60%] mx-auto border-gray-300" />

          {/* Credit Card Brands */}
          <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
            <FormField />
            <Box className="flex flex-wrap items-center gap-[17px] w-full overflow-x-auto">
              <img
                src="/images/group_10.svg"
                alt="VISA"
                className="h-6 sm:h-8 flex-shrink-0 w-[56.69px] !h-[18.31px]"
              />
              <img
                src="/images/group_12.svg"
                alt="Mastercard"
                className="h-6 sm:h-8 flex-shrink-0 w-[41.7px] !h-[32.44px]"
              />
              <img
                src="/images/group_1398.png"
                alt="Union Pay"
                className="h-6 sm:h-8 flex-shrink-0 w-[38.97px] !h-[24.12px]"
              />
              <img
                src="/images/group_23.svg"
                alt="JCB"
                className="h-6 sm:h-8 flex-shrink-0 w-[33.21px] !h-[24.9px]"
              />
              <img
                src="/images/group_25.svg"
                alt="AMEX"
                className="h-6 sm:h-8 flex-shrink-0 w-[32.23px] !h-[32.14px]"
              />
              <img
                src="/images/group_27.png"
                alt="Diners"
                className="h-6 sm:h-8 flex-shrink-0 w-[39.18px] !h-[28.95px]"
              />
              <img
                src="/images/group_1257.png"
                alt="Discover"
                className="h-6 sm:h-8 flex-shrink-0 w-[41.26px] !h-[26.39px]"
              />
              <img
                src="/images/group_1259.png"
                alt="JCB"
                className="h-6 sm:h-8 flex-shrink-0 w-[45.35px] !h-[30.12px]"
              />
            </Box>
          </Box>

          {/* Credit Card Notice */}
          <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
            <FormField />
            <Box className="flex flex-col gap-2">
              <p className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem]">
                クレジットカードのお申し込みは必須となります
              </p>
              <p className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem]">
                ※
                JCB・AMEX・Diners・Discoverは、株式会社ジェーシービーと既にご契約がある場合、既存の契約条件を引き継ぐことがあります。
              </p>
            </Box>
          </Box>

          {/* QR Code Brands */}
          <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-2 lg:gap-6 pt-4 sm:pt-10">
            <FormField />
            <Box className="flex flex-wrap items-center gap-[17px] w-full overflow-x-auto">
              <img
                src="/images/group_1261.svg"
                alt="PayPay"
                className="h-6 sm:h-8 flex-shrink-0 w-[88.65px] !h-[22.26px]"
              />
              <img
                src="/images/group_1263.svg"
                alt="d払い"
                className="h-6 sm:h-8 flex-shrink-0 w-[70.51px] !h-[22.68px]"
              />
              <img
                src="/images/group_1265.svg"
                alt="au PAY"
                className="h-6 sm:h-8 flex-shrink-0 w-[85.8px] !h-[16.03px]"
              />
              <img
                src="/images/group_1268.svg"
                alt="Pay"
                className="h-6 sm:h-8 flex-shrink-0 w-[66.29px] !h-[26.71px]"
              />
              <img
                src="/images/group_1270.svg"
                alt="楽天ペイ"
                className="h-6 sm:h-8 flex-shrink-0 w-[79.21px] !h-[24.93px]"
              />
              <img
                src="/images/group_1272.svg"
                alt="d払い"
                className="h-6 sm:h-8 flex-shrink-0 w-[76.29px] !h-[26.71px]"
              />
              <img
                src="/images/group_1276.png"
                alt="ゆうちょPay"
                className="h-6 sm:h-8 flex-shrink-0 w-[106.29px] !h-[19.59px]"
              />
            </Box>
          </Box>

          <hr className="w-full sm:w-[60%] mx-auto border-gray-300" />

          {/* 追加オプション - All Payment Methods */}
          <Box className="px-0 sm:ml-[16%] items-center justify-start">
            <Box className="flex gap-3 sm:gap-4 items-center">
              <Checkbox
                id="package2"
                checked={getValues().agxSettlementPackage2}
                onCheckedChange={() => handleChangeAgxSettlementPackage('package2')}
                className="mt-4 w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0"
              />
              <Label
                htmlFor="package2"
                className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer mt-4"
              >
                追加オプション：電子マネー（1,000円/月）
              </Label>
            </Box>
          </Box>
        </Box>

        <hr className="w-full sm:w-[60%] mx-auto border-gray-300" />

        {/* Electronic Money Details */}
        <Box className="px-0 sm:ml-[25%] lg:flex-row items-start lg:items-center gap-[51px]">
          <FormField />
          <Box className="flex flex-col px-0 sm:ml-10 w-full space-y-[51px]">
            {/* 交通系電子マネー */}
            <Box className="flex flex-col sm:flex-row w-full items-start sm:items-center gap-2 sm:gap-0 pb-[39px]">
              <Box className="text-[#707070] mt-6 font-normal text-base sm:text-[1.75rem] text-nowrap min-w-[120px] sm:min-w-[200px]">
                交通系電子マネー
                <br />
                <p className="text-[#C44546] mt-2 font-normal text-sm sm:text-[0.9375rem]">PiTaPaはご利用できません</p>
              </Box>
              <Box className="flex items-center gap-[17px] pl-2">
                <img
                  src="/images/group_1278.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[34.15px] !h-[34.15px]"
                />
                <img
                  src="/images/group_1280.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[37.94px] !h-[23.65px]"
                />
                <img
                  src="/images/group_1282.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[37.94px] !h-[22.37px]"
                />
                <img
                  src="/images/group_1284.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[37.94px] !h-[15.52px]"
                />
                <img
                  src="/images/group_1286.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[62.55px] !h-[9.11px]"
                />
                <img
                  src="/images/group_1288.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[37.94px] !h-[22.31px]"
                />
                <img
                  src="/images/group_1296.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[37.94px] !h-[17.56px]"
                />
                <img
                  src="/images/group_1292.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[44.66px] !h-[11.72px]"
                />
                <img
                  src="/images/group_1294.svg"
                  alt="交通系"
                  className="h-6 sm:h-8 flex-shrink-0 w-[37.94px] !h-[13.02px]"
                />
              </Box>
            </Box>

            {/* WAON */}
            <Box className="flex flex-col sm:flex-row w-full items-start sm:items-center gap-2 sm:gap-0 pb-[39px] !mt-0">
              <Box className="text-[#707070] font-normal text-base sm:text-[1.75rem] text-nowrap mr-[17px]">
                WAON
              </Box>
              <Box className="flex items-center">
                <img
                  src="/images/group_1299.svg"
                  alt="WAON"
                  className="h-6 sm:h-8 flex-shrink-0 w-[40.31px] !h-[25.41px]"
                />
              </Box>
            </Box>

            {/* iD */}
            <Box className="flex flex-col sm:flex-row w-full items-start sm:items-center gap-2 sm:gap-0 pb-[39px] !mt-0">
              <Box className="text-[#707070] font-normal text-base sm:text-[1.75rem] text-nowrap mr-[17px]">
                iD
              </Box>
              <Box className="flex items-center">
                <img
                  src="/images/group_1301.svg"
                  alt="iD"
                  className="h-6 sm:h-8 flex-shrink-0 w-[40.31px] !h-[26.2px]"
                />
              </Box>
            </Box>

            {/* nanaco */}
            <Box className="flex flex-col sm:flex-row w-full items-start sm:items-center gap-2 sm:gap-0 pb-[39px] !mt-0">
              <Box className="text-[#707070] font-normal text-base sm:text-[1.75rem] text-nowrap mr-[17px]">
                nanaco
              </Box>
              <Box className="flex items-center">
                <img
                  src="/images/group_1308.svg"
                  alt="nanaco"
                  className="h-6 sm:h-8 flex-shrink-0 w-[40.79px] !h-[25.6px]"
                />
              </Box>
            </Box>

            {/* Edy */}
            <Box className="flex flex-col sm:flex-row w-full items-start sm:items-center gap-2 sm:gap-0 pb-[39px] !mt-0">
              <Box className="text-[#707070] font-normal text-base sm:text-[1.75rem] text-nowrap mr-[17px]">
                Edy
              </Box>
              <Box className="flex items-center">
                <img
                  src="/images/group_1310.svg"
                  alt="Edy"
                  className="h-6 sm:h-8 flex-shrink-0 w-[36.54px] !h-[36.54px]"
                />
              </Box>
            </Box>

            {/* QUICPay */}
            <Box className="flex flex-col sm:flex-row w-full items-start sm:items-center gap-2 sm:gap-0 !mt-0">
              <Box className="text-[#707070] font-normal text-base sm:text-[1.75rem] text-nowrap mr-[17px]">
                QUICPay
              </Box>
              <Box className="flex items-center">
                <img
                  src="/images/group_1312.svg"
                  alt="QUICPay"
                  className="h-6 sm:h-8 flex-shrink-0 w-[42.52px] !h-[27.86px]"
                />
              </Box>
            </Box>
          </Box>
        </Box>

        <hr className="w-full sm:w-[60%] mx-auto border-gray-300" />

        {/* Fee Display */}
        <Box className="w-full flex flex-col space-y-6">
          <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
            <FormField label="初期費用" />
            <Box className="text-[#707070] text-base sm:text-[1.75rem] w-[300px] text-right">
              {initialFee.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1)}
            </Box>
            <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal">
              &nbsp;&nbsp;円 &nbsp;&nbsp;(税抜)
            </Box>
          </Box>

          <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
            <FormField label="月額費用" />
            <Box className="text-[#707070] text-base sm:text-[1.75rem] w-[300px] text-right">
              {monthlyFee.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1)}
            </Box>
            <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal">
            &nbsp;&nbsp;円 &nbsp;&nbsp;(税抜)
            </Box>
          </Box>
        </Box>
      </Box>
      {/* Fee Notice */}
      <Box className="w-full flex flex-col px-4 sm:px-0">
        <Box className="flex flex-col lg:flex-row items-start gap-4 lg:gap-6">
          <FormField />
          <Box className="text-[#C44546] text-sm sm:text-[0.9375rem] font-normal">
            <p>
              月額費用にはクレジットカードの600円が含まれて計算されています（月額費用ご請求の3ヶ月以内にご利用があれば、500円の値引きとなります）
            </p>
            <p>
              月額費用は、それぞれの決済種別において、審査完了後（約1-2ヶ月）後から発生します。{" "}
            </p>
            <p>（ご利用有無にかかわらず月額費用が発生します）</p>
          </Box>
        </Box>
      </Box>

      <FormButtons
        className="mb-32"
        onSave={showConfirmDialog}
        onNext={onSubmit}
        onBack={handleBack}
        isSubmitting={false}
        showBackButton={true}
      />
      <ConfirmDialog
        open={show}
        onOpenChange={setShow}
        onConfirm={onSave}
        title="入力内容を一時保存します。"
        confirmLabel="一時保存"
        confirmVariant="danger"
        cancelLabel="戻る"
        onCancel={() => setShow(false)}
      />
    </>
  );
};
