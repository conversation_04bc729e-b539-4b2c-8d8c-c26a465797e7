import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { DialogDescription } from '@radix-ui/react-dialog';
import React from 'react'

interface IProps {
  agxArea: any,
  showAreaModal: boolean;
  setShowAreaModal: (v: boolean) => void;
  handleChangeArea: any;
  handleSelectSubArea: any;
  handleCreateOrUpdateArea: any;
  dataAreaSetting: any;
}

export const AreaModal = ({ agxArea, showAreaModal, setShowAreaModal, handleChangeArea, handleSelectSubArea, handleCreateOrUpdateArea, dataAreaSetting }: IProps) => {
  return (
    <Dialog open={showAreaModal} onOpenChange={setShowAreaModal}>
      <DialogContent className="w-full max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">エリアの作成</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <form>
          <div className="mb-6">
            <label htmlFor="area-name" className="block text-gray-700 font-medium mb-2">新規に作成するエリアの名前を入力してください。</label>
            <input
              type="text"
              id="area-name"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none"
              value={agxArea.agxAreaName}
              onChange={handleChangeArea}
            />
          </div>
          <div className="mb-2">
            <label className="block text-gray-700 font-medium mb-2">エリアに紐付けするサブエリアを選択して下さい。</label>
            <div className="border rounded-md overflow-hidden">
              {/* Fixed Header */}
              <div className="bg-gray-50 border-b">
                <div className="grid grid-cols-[48px_1fr_1fr] gap-0">
                  <div className="px-4 py-3 text-center font-medium text-gray-700"></div>
                  <div className="px-4 py-3 text-center font-medium text-gray-700">サブエリア名</div>
                  <div className="px-4 py-3 text-center font-medium text-gray-700">エリア名</div>
                </div>
              </div>

              {/* Scrollable Body */}
              <div className="max-h-80 overflow-y-auto">
                <div className="divide-y divide-gray-200">
                  {dataAreaSetting?.agxSubAreas?.map((item, index) => (
                    <div key={index} className="grid grid-cols-[48px_1fr_1fr] gap-0 hover:bg-gray-50 transition-colors">
                      <div className="px-4 py-3 text-center flex items-center justify-center">
                        <input
                          id={item.agxSubAreaid}
                          type="checkbox"
                          className="form-checkbox h-5 w-5 text-blue-600 rounded"
                          checked={!!agxArea.agxSubAreaids?.find((s) => s.agxSubAreaid === item.agxSubAreaid && s.isSelected)}
                          onChange={(e) => handleSelectSubArea(e, item.agxSubAreaid, item.agxAreaid)}
                        />
                      </div>
                      <div className="px-4 py-3 text-center flex items-center justify-center">{item.agxSubAreaName}</div>
                      <div className="px-4 py-3 text-center flex items-center justify-center">{item.agxAreaName}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </form>

        <DialogFooter className="flex justify-end gap-2 mt-6">
          <Button
            className="text-[20px] w-[20%] max-md:w-full h-[48px] bg-gray-400 text-white rounded-[8px] hover:bg-gray-500 transition-colors"
            onClick={() => setShowAreaModal(false)}>閉じる</Button>
          <Button
            type="button"
            className="bg-[#1D9987] hover:bg-[#1D9987]/80 text-white font-medium px-8 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm text-[18px]"
            onClick={handleCreateOrUpdateArea}>作成</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
