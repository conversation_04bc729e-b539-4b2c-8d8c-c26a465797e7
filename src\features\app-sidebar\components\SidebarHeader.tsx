import React from 'react';
import { SidebarHeader } from '@/components/ui/sidebar';
import { SIDEBAR_ICONS, SIDEBAR_STYLES } from '../constants/sidebarConstants';

interface SidebarHeaderProps {
  agxMerchantNo?: string;
  agxStoreName?: string;
}

export const AppSidebarHeader: React.FC<SidebarHeaderProps> = React.memo(({ 
  agxMerchantNo, 
  agxStoreName 
}) => {
  return (
    <SidebarHeader className={SIDEBAR_STYLES.header}>
      <div className="flex">
        <img
          src={SIDEBAR_ICONS.logo}
          alt="ChoQi Logo"
          className={SIDEBAR_STYLES.logo}
        />
      </div>
      <div className="pt-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-[20px] text-[#6F6F6E]">加盟店番号</span>
          <span className="text-[20px] text-[#6F6F6E]">｜</span>
          <span className="text-[20px] text-black">
            {agxMerchantNo || ''}
          </span>
        </div>
        <p className="text-[20px] text-[#6F6F6E]">
          {agxStoreName || ''}
        </p>
      </div>
      <hr className='mt-3 border-1 border-gray-500' />
    </SidebarHeader>
  );
});

AppSidebarHeader.displayName = 'AppSidebarHeader'; 