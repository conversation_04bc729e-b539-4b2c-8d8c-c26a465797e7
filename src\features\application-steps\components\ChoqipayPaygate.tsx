import Box from "@/components/ui/box";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useCallback, useEffect, useState } from "react";
import { moneyData } from "../utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { AgxMerchantParams, ApplicationStepProps } from "../types";
import { FormButtons } from "./ui/FormButtons";
import { STEP } from "@/constants/common.constant";
import { FormField } from "./ui/Field";
import { choqipayPaygateSchema, type ChoqipayPaygateFormData } from "../schemas/choqipayPaygate.schema";
import ConfirmDialog from "@/components/ConfirmDialog";

export const ChoqipayPaygate = ({ setStep, agxMerchantParams, setAgxMerchantParams, updateMerchant, isUpdating }: ApplicationStepProps) => {

  const [initialFee, setInitialFee] = useState(0);
  const [monthlyFee, setMonthlyFee] = useState(0);
  const [show, setShow] = useState(false);

  const { register, handleSubmit, formState: { errors }, trigger, setValue, watch, getValues } = useForm<ChoqipayPaygateFormData>({
    resolver: zodResolver(choqipayPaygateSchema),
    mode: "onBlur",
    defaultValues: {
      agxNumberOfTerminal: agxMerchantParams?.agxNumberOfTerminal || 1,
      agxSettlementCard: agxMerchantParams?.agxSettlementCard || true,
      agxSettlementTraffic: agxMerchantParams?.agxSettlementTraffic || false,
      agxSettlementNanaco: agxMerchantParams?.agxSettlementNanaco || false,
      agxSettlementWaon: agxMerchantParams?.agxSettlementWaon || false,
      agxSettlementEdy: agxMerchantParams?.agxSettlementEdy || false,
      agxSettlementAid: agxMerchantParams?.agxSettlementAid || false,
      agxSettlementQuicpay: agxMerchantParams?.agxSettlementQuicpay || false,
      agxSettlementQrCode: agxMerchantParams?.agxSettlementQrCode || false,
      agxSettlementPackage1: agxMerchantParams?.agxSettlementPackage1 || false,
      agxSettlementPackage2: agxMerchantParams?.agxSettlementPackage2 || false,
    },
  });

  const caclMoney = useCallback((agxNumberOfTerminal: number, agxSettlementCard: boolean, agxSettlementTraffic: boolean, agxSettlementNanaco: boolean, agxSettlementWaon: boolean, agxSettlementEdy: boolean, agxSettlementAid: boolean, agxSettlementQuicpay: boolean, agxSettlementQrCode: boolean, agxSettlementPackage1: boolean, agxSettlementPackage2: boolean) => () => {

    const money = agxNumberOfTerminal * (
      (agxSettlementCard ? moneyData.money_card : 0) +
      (agxSettlementTraffic ? moneyData.money_Traffic : 0) +
      (agxSettlementNanaco ? moneyData.money_nanaco : 0) +
      (agxSettlementWaon ? moneyData.money_WAON : 0) +
      (agxSettlementEdy ? moneyData.money_Edy : 0) +
      (agxSettlementAid ? moneyData.money_iD : 0) +
      (agxSettlementQuicpay ? moneyData.money_QUICPay : 0) +
      (agxSettlementQrCode ? moneyData.money_QR : 0));

    setInitialFee(agxNumberOfTerminal * moneyData.money_core);
    setMonthlyFee(money);
  }, [])

  const handleChangeAgxNumberOfTerminal = (e) => {
    const value = parseInt(e.target.value, 10);
    setValue('agxNumberOfTerminal', value);
    trigger('agxNumberOfTerminal');
    if (Number.isInteger(value)) {
      caclMoney(value, getValues().agxSettlementCard, getValues().agxSettlementTraffic, getValues().agxSettlementNanaco, getValues().agxSettlementWaon, getValues().agxSettlementEdy, getValues().agxSettlementAid, getValues().agxSettlementQuicpay, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
    }
  }

  const handleChangeAgxSettlementTraffic = (checked: boolean) => {
    setValue('agxSettlementTraffic', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, checked, getValues().agxSettlementNanaco, getValues().agxSettlementWaon, getValues().agxSettlementEdy, getValues().agxSettlementAid, getValues().agxSettlementQuicpay, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const handleChangeAgxSettlementNanaco = (checked: boolean) => {
    setValue('agxSettlementNanaco', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, getValues().agxSettlementTraffic, checked, getValues().agxSettlementWaon, getValues().agxSettlementEdy, getValues().agxSettlementAid, getValues().agxSettlementQuicpay, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const handleChangeAgxSettlementWaon = (checked: boolean) => {
    setValue('agxSettlementWaon', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, getValues().agxSettlementTraffic, getValues().agxSettlementNanaco, checked, getValues().agxSettlementEdy, getValues().agxSettlementAid, getValues().agxSettlementQuicpay, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const handleChangeAgxSettlementEdy = (checked: boolean) => {
    setValue('agxSettlementEdy', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, getValues().agxSettlementTraffic, getValues().agxSettlementNanaco, getValues().agxSettlementWaon, checked, getValues().agxSettlementAid, getValues().agxSettlementQuicpay, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const handleChangeAgxSettlementAid = (checked: boolean) => {
    setValue('agxSettlementAid', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, getValues().agxSettlementTraffic, getValues().agxSettlementNanaco, getValues().agxSettlementWaon, getValues().agxSettlementEdy, checked, getValues().agxSettlementQuicpay, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const handleChangeAgxSettlementQuicpay = (checked: boolean) => {
    setValue('agxSettlementQuicpay', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, getValues().agxSettlementTraffic, getValues().agxSettlementNanaco, getValues().agxSettlementWaon, getValues().agxSettlementEdy, getValues().agxSettlementAid, checked, getValues().agxSettlementQrCode, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const handleChangeAgxSettlementQrCode = (checked: boolean) => {
    setValue('agxSettlementQrCode', checked);
    caclMoney(getValues().agxNumberOfTerminal, getValues().agxSettlementCard, getValues().agxSettlementTraffic, getValues().agxSettlementNanaco, getValues().agxSettlementWaon, getValues().agxSettlementEdy, getValues().agxSettlementAid, getValues().agxSettlementQuicpay, checked, getValues().agxSettlementPackage1, getValues().agxSettlementPackage2)();
  }

  const onSubmit = () => {
    setAgxMerchantParams({
      ...agxMerchantParams,
      ...getValues(),
    } as AgxMerchantParams);

    setStep(STEP.KIYAKU);
  };

  const showConfirmDialog = async () => {
    const isValid = await trigger();
    if (!isValid) {
      return false;
    }
    setShow(true);
  }

  const onSave = () => {
    updateMerchant(
      {
        ...agxMerchantParams,
        ...getValues(),
      }
    );
    setShow(false);
  };

  const handleBack = () => {
    setAgxMerchantParams({
      ...agxMerchantParams,
      ...getValues(),
    } as AgxMerchantParams);

    setStep(STEP.ADDITIONAL1);
  };

  useEffect(() => {
    if (agxMerchantParams) {
      // Update form values
      setValue('agxNumberOfTerminal', agxMerchantParams.agxNumberOfTerminal);
      setValue('agxSettlementCard', agxMerchantParams.agxSettlementCard);
      setValue('agxSettlementTraffic', agxMerchantParams.agxSettlementTraffic);
      setValue('agxSettlementNanaco', agxMerchantParams.agxSettlementNanaco);
      setValue('agxSettlementWaon', agxMerchantParams.agxSettlementWaon);
      setValue('agxSettlementEdy', agxMerchantParams.agxSettlementEdy);
      setValue('agxSettlementAid', agxMerchantParams.agxSettlementAid);
      setValue('agxSettlementQuicpay', agxMerchantParams.agxSettlementQuicpay);
      setValue('agxSettlementQrCode', agxMerchantParams.agxSettlementQrCode);
      setValue('agxSettlementPackage1', agxMerchantParams.agxSettlementPackage1);
      setValue('agxSettlementPackage2', agxMerchantParams.agxSettlementPackage2);

      // Calculate money
      if (agxMerchantParams.agxNumberOfTerminal != null) {
        caclMoney(agxMerchantParams.agxNumberOfTerminal, agxMerchantParams.agxSettlementCard, agxMerchantParams.agxSettlementTraffic, agxMerchantParams.agxSettlementNanaco, agxMerchantParams.agxSettlementWaon, agxMerchantParams.agxSettlementEdy, agxMerchantParams.agxSettlementAid, agxMerchantParams.agxSettlementQuicpay, agxMerchantParams.agxSettlementQrCode, agxMerchantParams.agxSettlementPackage1, agxMerchantParams.agxSettlementPackage2)();
      }
    }
  }, [agxMerchantParams, caclMoney, setValue]);

  return (
    <>
      <Box className="mx-auto space-y-6 md:space-y-8 lg:space-y-10 sm:mb-10 mt-6 sm:mt-10 px-4 sm:px-0">
        {/* 端末台数 Section */}
        <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
          <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 w-full lg:w-auto">
            <FormField label="端末台数" required />
            <Box className="w-full sm:w-48 md:w-64">
              <Input
                type="number"
                {...register("agxNumberOfTerminal")}
                min={1}
                max={10}
                onChange={handleChangeAgxNumberOfTerminal}
                className="w-full rounded-[13px] px-[17px] py-[13px] text-base sm:text-[1.75rem]"
              />
              {errors.agxNumberOfTerminal && (
                <span className="text-red-500 text-sm font-bold">
                  {errors.agxNumberOfTerminal.message}
                </span>
              )}
            </Box>
            <span className="text-[#707070] text-base sm:text-[1.75rem] font-normal">
              台
            </span>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal">
            ※59,800円(税抜)/台
          </Box>
        </Box>

        {/* 端末カラー Section */}
        <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
          <FormField label="端末" required />
          <Box className="text-[#707070] text-base sm:text-[1.75rem]">
            黒(新)
          </Box>
        </Box>

        {/* 決済種別 Section */}
        <Box className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-6">
          <FormField label="決済種別" required />
          <Box className="text-[#FF0002] text-base sm:text-[1.75rem] font-normal">
            決済種別により、月額費用（税抜）が発生します。ページ下部をご確認ください
          </Box>
        </Box>

        {/* Credit Card - Always checked */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementCard"
              checked={getValues().agxSettlementCard}
              disabled={true}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementCard"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              クレジットカード
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_card}円(税抜)
          </Box>
          <Box className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem] ml-0 sm:ml-10 pt-4">
            VISA・Mastercard・JCB・AMEX・Diners・Discover・銀聯がご利用可能になります<br />
            **JCB・AMEX・Diners・Discoverは、株式会社ジェーシービーと既にご契約がある場合、既存の契約条件を引き継ぐことがあります。
          </Box>
        </Box>

        {/* Traffic IC Card */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementTraffic"
              checked={getValues().agxSettlementTraffic}
              onCheckedChange={handleChangeAgxSettlementTraffic}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementTraffic"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              交通系電子マネー
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_Traffic}円(税抜)
          </Box>
          <Box className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem] ml-0 sm:ml-10 pt-4">
            PiTaPaはご利用になれません
          </Box>
        </Box>

        {/* nanaco */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementNanaco"
              checked={getValues().agxSettlementNanaco}
              onCheckedChange={handleChangeAgxSettlementNanaco}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementNanaco"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              nanaco
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_nanaco}円(税抜)
          </Box>
        </Box>

        {/* WAON */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementWaon"
              checked={getValues().agxSettlementWaon}
              onCheckedChange={handleChangeAgxSettlementWaon}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementWaon"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              WAON
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_WAON}円(税抜)
          </Box>
        </Box>

        {/* Edy */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementEdy"
              checked={getValues().agxSettlementEdy}
              onCheckedChange={handleChangeAgxSettlementEdy}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementEdy"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              Edy
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_Edy}円(税抜)
          </Box>
        </Box>

        {/* iD */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementAid"
              checked={getValues().agxSettlementAid}
              onCheckedChange={handleChangeAgxSettlementAid}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementAid"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              iD
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_iD}円(税抜)
          </Box>
        </Box>

        {/* QUICPay */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementQuicpay"
              checked={getValues().agxSettlementQuicpay}
              onCheckedChange={handleChangeAgxSettlementQuicpay}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementQuicpay"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              QUICPay
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_QUICPay}円(税抜)
          </Box>
          <Box className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem] ml-0 sm:ml-10 pt-4">
            ** QUICPayは、株式会社ジェーシービーと既にご契約がある場合、既存の契約条件を引き継ぐことがあります。
          </Box>
        </Box>

        {/* QR Code */}
        <Box className="px-0 sm:ml-[16%] items-center justify-start sm:pl-[120px]">
          <Box className="flex gap-3 sm:gap-4 items-center">
            <Checkbox
              id="agxSettlementQrCode"
              checked={getValues().agxSettlementQrCode}
              onCheckedChange={handleChangeAgxSettlementQrCode}
              className="w-[20px] h-[20px] sm:w-[27px] sm:h-[27px] bg-white data-[state=checked]:bg-[rgba(26,164,146,1)] border-gray-300 flex-shrink-0 mt-1"
            />
            <Label
              htmlFor="agxSettlementQrCode"
              className="text-[#707070] text-base sm:text-[1.75rem] font-normal cursor-pointer"
            >
              QRコード決済
            </Label>
          </Box>
          <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal ml-0 sm:ml-10 pt-4">
            ※月額{moneyData.money_QR}円(税抜)
          </Box>
          <Box className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem] ml-0 sm:ml-10 pt-4">
            対応サービス：PayPay、d払い、auPAY、BankPay、メルペイ、WeChatPay、アリペイ
          </Box>
        </Box>

        {/* Fee Display */}
        <Box className="w-full flex flex-col space-y-6 mt-10 px-0 sm:ml-[16%]">
          <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
            <FormField label="初期費用" />
            <Box className="text-[#707070] text-base sm:text-[1.75rem] w-[300px] text-right">
              {initialFee.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1)}円(税抜)
            </Box>
            <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal"> &nbsp;&nbsp;円 &nbsp;&nbsp;(税抜) </Box>
          </Box>

          <Box className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
            <FormField label="月額費用" />
            <Box className="text-[#707070] text-base sm:text-[1.75rem] w-[300px] text-right">
              {monthlyFee.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1)}円(税抜)
            </Box>
            <Box className="text-[#707070] text-base sm:text-[1.75rem] font-normal"> &nbsp;&nbsp;円 &nbsp;&nbsp;(税抜) </Box>
          </Box>
        </Box>
      </Box>
      {/* Fee Notice */}
      <Box className="w-full flex flex-col px-4 sm:px-0">
        <Box className="flex flex-col lg:flex-row items-start gap-4 lg:gap-6">
          <FormField />
          <Box className="text-[#C44546] font-normal text-sm sm:text-[0.9375rem]">
            <p>
              月額費用発生月の直近3ヶ月にご利用がある場合、決済端末1台分のクレジットカード月額費用は値引きされます。なお、月額費用は銀行振込またはクレジットカードにて決済されますが、銀行振込の場合は、事務取扱手数料500円（税抜）をご負担いただきます。詳細は月額費用の請求・領収書ページをご参照ください。
            </p>
          </Box>
        </Box>
      </Box>
      <Box className="w-full flex flex-col px-4 sm:px-0">
        <Box className="flex flex-col lg:flex-row items-start gap-4 lg:gap-6">
          <FormField />
          <Box className="text-[#13AE9C] font-normal text-sm sm:text-[0.9375rem] pt-4">
            <p>
              <Box>
                <div>【ご注意】月額費用は、それぞれの決済種別において、決済事業者の審査（約1-3ヶ月）後に端末上ご利用できる状態になった時から発生します。</div>
                <div className="ml-0 sm:ml-[10%] pt-4">
                  <p>（クレジットカードの月額費用以外は、ご利用有無にかかわらず、発生する手数料ですので、ご注意ください）</p>
                </div>
                <p>なお、QRコード決済につきましては決済事業者での審査にお時間を要しており、利用開始まで平均4ヶ月以上かかる場合がございます。</p>
              </Box>
            </p>
          </Box>
        </Box>
      </Box>

      <FormButtons
        className="mb-32"
        onSave={showConfirmDialog}
        onNext={onSubmit}
        onBack={handleBack}
        isSubmitting={false}
        showBackButton={true}
      />
      <ConfirmDialog
        open={show}
        onOpenChange={setShow}
        onConfirm={onSave}
        title="入力内容を一時保存します。"
        confirmLabel="一時保存"
        confirmVariant="danger"
        cancelLabel="戻る"
        onCancel={() => setShow(false)}
      />
    </>
  );
};
