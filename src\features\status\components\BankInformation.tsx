import { Card, CardContent } from "@/components/ui/card";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { mapAccountType } from "@/constants/common.constant";

interface BankInformationProps {
  merchantData: GetMerchantStatusResponse;
}
function BankInformation({ merchantData }: BankInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            金融機関コード
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBankNo ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            支店コード
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBranchNo ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            金融機関名
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBankName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">支店名</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBranchName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            金融機関名（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBankPhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            支店名（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxBranchPhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">預金種類</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxAccountType !== null
                ? mapAccountType.get(merchantData?.agxAccountType)
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">口座番号</Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxAccountNo ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            口座名義（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full h-10 rounded-md flex items-center px-3">
              {merchantData?.agxAccountHolder ?? ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default BankInformation;
