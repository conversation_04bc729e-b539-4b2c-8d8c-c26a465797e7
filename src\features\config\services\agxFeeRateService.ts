import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { GetAgxFeeRateResponse } from "../types";

class AgxFeeRateService{
    async getAllData(agxBusinessType: number): Promise<GetAgxFeeRateResponse> {
        const response = await apiService.get<{ data: GetAgxFeeRateResponse }>(`${API_ENDPOINTS.AGX_FEE_RATE(agxBusinessType)}`);
        return response.data;
    }
}

export const agxFeeRateService = new AgxFeeRateService();
export default agxFeeRateService; 