import { useQuery } from "@tanstack/react-query";
import merchantStatusService from "../services/merchantStatusService";

export const useQueryMerchantStatus = ({agxMerchantNo}: {agxMerchantNo: string}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['merchantStatus', agxMerchantNo],
    queryFn: async () => merchantStatusService.getMerchantStatus(agxMerchantNo)  
  });

  return {
    data, isLoading, isError, error, refetch
  };
}