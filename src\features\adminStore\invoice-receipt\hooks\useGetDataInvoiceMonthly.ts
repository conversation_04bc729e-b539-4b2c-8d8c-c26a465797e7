import { useQuery } from '@tanstack/react-query';
import { invoiceService } from '../services/invoiceReceiptService';
import { InvoiceMonthlyType } from '../types';

export const useGetDataInvoiceMonthly = (merchantNo: string, yearMonth: string) => {
  return useQuery({
    queryKey: ['admin-invoice-monthly', merchantNo, yearMonth],
    queryFn: async (): Promise<InvoiceMonthlyType> => {
      if (!merchantNo || !yearMonth) {
        throw new Error('Merchant number and year month are required');
      }
      const response = await invoiceService.getDataInvoiceMonthly(merchantNo, yearMonth);
      return response.data;
    },
    enabled: !!merchantNo && !!yearMonth,
  });
};
