import exclamationPointIcon from "@/assets/images/グループ 1351.svg";
import flagIcon from "@/assets/images/グループ 1350.svg";
import HamburgerIcon from "@/assets/images/グループ 1345.svg";
import ScrollIcon from "@/assets/images/グループ 1346.svg";
import BookIcon from "@/assets/images/グループ 1347.svg";
import ScreenIcon from "@/assets/images/グループ 1348.svg";
import QuestionIcon from "@/assets/images/グループ 1393.svg";
import MailIcon from "@/assets/images/グループ 1389.svg";
import SettingsIcon from '@/assets/images/Icon settings.svg';
import ReceiptIcon from '@/assets/images/Icon payment-invoice-sign-alt-o.svg';
import { TypeStore, AccountTypes } from "@/types/globalType";
import { Link } from "react-router-dom";
import { useGetUrlRedirect } from "@/features/overview/hooks/useGetUrlRedirect";

const MainMenu = () => {
  const { merchantNo, storeName, typeStore, accountType, handleRollPaperClick, handleFaqClick, getUrlDeposit, getUrlConfig, getUrlInvoiceReceipt } = useGetUrlRedirect();

  return (
    <div className="min-h-[calc(100vh-180px)] p-4 md:py-6 md:px-1 lg:p-6 text-[20px]">
      <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">
        こんにちは、加盟店番号{merchantNo} {storeName}さん
      </h2>
      <div className="py-6 mt-2">
        <div className="flex">
          <img src={exclamationPointIcon} alt="" />
          <h2 className="text-xl md:text-2xl text-[#6F6F6E]">お知らせ</h2>
        </div>
        <div
          style={{
            boxShadow: "0px 3px 6px #********",
          }}
          className="border min-h-[130px] mt-4 w-full max-w-full md:max-w-[90%] lg:max-w-[1373px] border-gray-500 rounded-lg bg-white"
        >
          <div className="p-4 md:p-4 lg:pl-10">
            <div className="flex items-start">
              <div className="">
                <ul className="py-0 text-[#6F6F6E] list-disc pl-5">
                  <li className=" text-[#FF0002] py-1">
                    <div className="flex items-center space-x-2 font-medium">
                      <Link 
                        to={getUrlConfig()} 
                        className="text-[18px] md:text-[20px] lg:text-[22px] hover:underline cursor-pointer"
                      >
                        申し込みを受け付けました。現在のステータスは加盟店情報よりご確認ください。
                      </Link>
                    </div>
                  </li>
                  <li>
                    <div className="flex items-center space-x-2 font-normal">
                      <span className="text-[18px] md:text-[20px] lg:text-[22px]">キャンペーンのご案内</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Shortcuts Section */}
        <div className="">
          <div className="flex items-center py-6">
            <img src={flagIcon} alt="" />
            <h2 className="text-xl md:text-2xl text-[#6F6F6E]">
              ショートカット
            </h2>
          </div>

          {/* Shortcuts Grid */}
          <div className="w-full md:w-[95%] lg:w-[80%] xl:w-[70%] grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
            <Link 
              to={getUrlDeposit()}
              className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
            >
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={HamburgerIcon} className="w-6 h-5 md:w-8 md:h-7 mb-2" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">振込一覧</span>
              </div>
            </Link>

            <Link 
              to={getUrlInvoiceReceipt()}
              className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
            >
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={ReceiptIcon} alt="" className="h-8 md:h-10 w-6 mb-2" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">領収書・請求書</span>
              </div>
            </Link>

            <div className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white" onClick={handleRollPaperClick}>
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={ScrollIcon} alt="" className="h-5 md:h-7 mb-2" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">ロール紙の購入</span>
              </div>
            </div>

            {/* {(typeStore === TypeStore.STORE_MIGRATE || typeStore === TypeStore.STORE_PAYGATE) && (
              <div className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white">
                <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                  <img src={ScreenIcon} alt="" className="h-8 md:h-10" />
                  <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">端末管理画面</span>
                </div>
              </div>
            )}

            <div className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white" onClick={() => handleClickOpenNewTab("https://devhome.choqipay.choqi.co.jp/contact")}>
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={BookIcon} alt="" className="h-8 md:h-10 mb-2" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">マニュアル</span>
              </div>
            </div> */}

            {(typeStore === TypeStore.STORE_MIGRATE || typeStore === TypeStore.STORE_PAYGATE) && (
              <Link 
                to="/overview"
                className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
              >
                <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                  <img src={ScreenIcon} alt="" className="h-8 md:h-10" />
                  <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">決済データ</span>
                </div>
              </Link>
            )}

            <Link 
              to="/store/agreement"
              className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
            >
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={BookIcon} alt="" className="h-8 md:h-10 mb-2" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">決済端末ヘルプサイト</span>
              </div>
            </Link>

            <div className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white" onClick={handleFaqClick}>
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={QuestionIcon} alt="" className="h-4 md:h-6 mb-3" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">よくあるご質問</span>
              </div>
            </div>

            {accountType === AccountTypes.STORE && (
              <Link 
                to="/store/config"
                className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
              >
                <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                  <img src={SettingsIcon} alt="" className="h-8 md:h-10 w-8 mb-2" />
                  <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">加盟店情報</span>
                </div>
              </Link>
            )}

            <Link 
              to="/contact"
              className="hover:shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
            >
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={MailIcon} alt="" className="h-8 md:h-10 mb-2" />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">問い合わせ</span>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MainMenu;
