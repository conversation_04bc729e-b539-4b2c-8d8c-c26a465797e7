import { useState, useEffect } from 'react';
import { AgxMerchantParams } from '../types';

// Kiyaku Rules State Interface
interface KiyakuRulesState {
    rule_1: boolean;
    rule_2: boolean;
    rule_3: boolean;
    rule_4: boolean;
    rule_5: boolean;
    rule_6: boolean;
    rule_7: boolean;
    rule_8: boolean;
    rule_9: boolean;
    rule_10: boolean;
    rule_11: boolean;
    rule_12: boolean;
    rule_13: boolean;
    rule_14: boolean;
    rule_15: boolean;
    rule_16: boolean;
    rule_17: boolean;
}

// Kiyaku Label State Interface
interface KiyakuLabelState {
    label_1: boolean;
    label_2: boolean;
    label_3: boolean;
    label_4: boolean;
    label_5: boolean;
    label_6: boolean;
    label_7: boolean;
    label_8: boolean;
    label_9: boolean;
    label_10: boolean;
    label_11: boolean;
    label_12: boolean;
    label_13: boolean;
    label_14: boolean;
    label_15: boolean;
    label_16: boolean;
    label_17: boolean;
}

const initialRulesState: KiyakuRulesState = {
    rule_1: false,
    rule_2: false,
    rule_3: false,
    rule_4: false,
    rule_5: false,
    rule_6: false,
    rule_7: false,
    rule_8: false,
    rule_9: false,
    rule_10: false,
    rule_11: false,
    rule_12: false,
    rule_13: false,
    rule_14: false,
    rule_15: false,
    rule_16: false,
    rule_17: false,
};

const initialLabelState: KiyakuLabelState = {
    label_1: true,
    label_2: true,
    label_3: true,
    label_4: true,
    label_5: true,
    label_6: true,
    label_7: true,
    label_8: true,
    label_9: true,
    label_10: true,
    label_11: true,
    label_12: true,
    label_13: true,
    label_14: true,
    label_15: true,
    label_16: true,
    label_17: true,
};

// PDF URLs mapping
const PDF_URLS = {
    choqipay: 'https://choqi.co.jp/choqipay/rule.pdf',
    mufg: 'https://www.cr.mufg.jp/merchant/rule/mufgcard/shop_01.pdf',
    jcb: 'https://www.jcb.co.jp/kiyaku/pdf/kameiten0705_05.pdf',
    jcbPremo: 'https://www.jcb.co.jp/kiyaku/pdf/premo_kameiten.pdf',
    tanako: 'https://www.jcb.co.jp/kiyaku/pdf/tanako_kameiten.pdf',
    ginren: 'https://www.cr.mufg.jp/merchant/rule/ginrei/shop.pdf',
    traffic: 'https://www.cr.mufg.jp/merchant/rule/index_01.pdf',
    nanaco: 'https://www.cr.mufg.jp/merchant/rule/e_money/nanaco.pdf',
    waon: 'https://www.cr.mufg.jp/merchant/rule/e_money/waon.pdf',
    edy: 'https://www.cr.mufg.jp/merchant/rule/e_money/edy.pdf',
    id: 'https://www.cr.mufg.jp/merchant/rule/e_money/id.pdf',
    qrCode: 'https://www.daiwahousefinancial.co.jp/docs/terms/omatome-terms/',
    crepicoCredit: '/pdf/クレピコ端末設置使用規約（クレジット編）.pdf',
    crepicoMaintenance: '/pdf/クレピコ端末保守サービス規約.pdf',
    crepicoService: '/pdf/クレピコサービス加入規約.pdf',
    smeProgram: '/pdf/中小企業優遇プログラム_同意事項.pdf',
    smeStatement: '/pdf/中小企業優遇プログラム_表明・確約事項.pdf'
};

export const useKiyakuImproved = (agxMerchantParams: AgxMerchantParams | null) => {
    const [kiyakuRulesState, setKiyakuRulesState] = useState<KiyakuRulesState>(initialRulesState);
    const [kiyakuLabelState, setKiyakuLabelState] = useState<KiyakuLabelState>(initialLabelState);

    // Effect to restore kiyaku state from agxMerchantParams on mount
    useEffect(() => {
        if (agxMerchantParams) {
            // Restore rules from agxMerchantParams
            if (agxMerchantParams.agxKiyakuRules) {
                setKiyakuRulesState(prev => ({
                    ...prev,
                    ...agxMerchantParams.agxKiyakuRules
                }));
            }
            
            // Restore labels from agxMerchantParams
            if (agxMerchantParams.agxKiyakuLabels) {
                setKiyakuLabelState(prev => ({
                    ...prev,
                    ...agxMerchantParams.agxKiyakuLabels
                }));
            }
        }
    }, [agxMerchantParams]);

    // Handle checkbox change
    const handleKiyakuClickCheckbox = (ruleKey: string, checked: boolean) => {
        setKiyakuRulesState(prev => ({
            ...prev,
            [ruleKey]: checked
        }));
    };

    // Handle PDF open and enable checkbox
    const handleKiyakuPDFOpen = (pdfKey: string, labelKey: string) => {
        const url = PDF_URLS[pdfKey as keyof typeof PDF_URLS];
        if (url) {
            window.open(url, '_blank');
            setKiyakuLabelState(prev => ({
                ...prev,
                [labelKey]: false
            }));
        }
    };

    // Check if all required rules are checked based on settlement types (improved logic based on index.js)
    const handleKiyakuCheck = (): boolean => {
        if (!agxMerchantParams) return false;

        // Basic required rules for card settlements (rules 1-6)
        const basicRequiredRules = [
            'rule_1', 'rule_2', 'rule_3', 'rule_4', 'rule_5', 'rule_6'
        ];

        // Check if basic card rules are satisfied and at least one card settlement is selected
        const isBasicCardValid = basicRequiredRules.every(rule => 
            kiyakuRulesState[rule as keyof KiyakuRulesState]
        ) && (agxMerchantParams.agxSettlementCard || agxMerchantParams.agxSettlementPackage1 || agxMerchantParams.agxSettlementPackage2);

        // Check individual electronic money settlement types (only required if selected)
        const isTrafficValid = agxMerchantParams.agxSettlementTraffic ? 
            kiyakuRulesState.rule_7 : true;
        
        const isNanacoValid = agxMerchantParams.agxSettlementNanaco ? 
            kiyakuRulesState.rule_8 : true;
        
        const isWaonValid = agxMerchantParams.agxSettlementWaon ? 
            kiyakuRulesState.rule_9 : true;
        
        const isEdyValid = agxMerchantParams.agxSettlementEdy ? 
            kiyakuRulesState.rule_10 : true;
        
        const isAidValid = agxMerchantParams.agxSettlementAid ? 
            kiyakuRulesState.rule_11 : true;
        
        const isQrCodeValid = agxMerchantParams.agxSettlementQrCode ? 
            kiyakuRulesState.rule_12 : true;

        // Check Crepico rules (always required)
        const isCrepicoValid = kiyakuRulesState.rule_13 && 
                              kiyakuRulesState.rule_14 && 
                              kiyakuRulesState.rule_15;

        // Check all settlement validations
        const allSettlementsValid = isBasicCardValid && 
                                  isTrafficValid && 
                                  isNanacoValid && 
                                  isWaonValid && 
                                  isEdyValid && 
                                  isAidValid && 
                                  isQrCodeValid && 
                                  isCrepicoValid;

        // Check for SME program rules if business type is 283260008
        if (agxMerchantParams.agxBusinessType === 283260008) {
            return allSettlementsValid && kiyakuRulesState.rule_16 && kiyakuRulesState.rule_17;
        }

        return allSettlementsValid;
    };

    // Helper function to check which settlement types are selected
    const getSelectedSettlementTypes = () => {
        if (!agxMerchantParams) return [];
        
        const selectedTypes = [];
        
        if (agxMerchantParams.agxSettlementCard) selectedTypes.push('Card');
        if (agxMerchantParams.agxSettlementPackage1) selectedTypes.push('Package1');
        if (agxMerchantParams.agxSettlementPackage2) selectedTypes.push('Package2');
        if (agxMerchantParams.agxSettlementTraffic) selectedTypes.push('Traffic');
        if (agxMerchantParams.agxSettlementNanaco) selectedTypes.push('Nanaco');
        if (agxMerchantParams.agxSettlementWaon) selectedTypes.push('Waon');
        if (agxMerchantParams.agxSettlementEdy) selectedTypes.push('Edy');
        if (agxMerchantParams.agxSettlementAid) selectedTypes.push('Aid');
        if (agxMerchantParams.agxSettlementQrCode) selectedTypes.push('QrCode');
        
        return selectedTypes;
    };

    // Helper function to get validation status for debugging
    const getValidationStatus = () => {
        if (!agxMerchantParams) return {};
        
        const basicRequiredRules = [
            'rule_1', 'rule_2', 'rule_3', 'rule_4', 'rule_5', 'rule_6'
        ];

        const isBasicCardValid = basicRequiredRules.every(rule => 
            kiyakuRulesState[rule as keyof KiyakuRulesState]
        ) && (agxMerchantParams.agxSettlementCard || agxMerchantParams.agxSettlementPackage1 || agxMerchantParams.agxSettlementPackage2);

        return {
            selectedSettlementTypes: getSelectedSettlementTypes(),
            isBasicCardValid,
            isTrafficValid: agxMerchantParams.agxSettlementTraffic ? kiyakuRulesState.rule_7 : true,
            isNanacoValid: agxMerchantParams.agxSettlementNanaco ? kiyakuRulesState.rule_8 : true,
            isWaonValid: agxMerchantParams.agxSettlementWaon ? kiyakuRulesState.rule_9 : true,
            isEdyValid: agxMerchantParams.agxSettlementEdy ? kiyakuRulesState.rule_10 : true,
            isAidValid: agxMerchantParams.agxSettlementAid ? kiyakuRulesState.rule_11 : true,
            isQrCodeValid: agxMerchantParams.agxSettlementQrCode ? kiyakuRulesState.rule_12 : true,
            isCrepicoValid: kiyakuRulesState.rule_13 && kiyakuRulesState.rule_14 && kiyakuRulesState.rule_15,
            isSmeRequired: agxMerchantParams.agxBusinessType === 283260008,
            isSmeValid: kiyakuRulesState.rule_16 && kiyakuRulesState.rule_17,
            // Individual rule states for debugging
            individualRules: {
                rule_1: kiyakuRulesState.rule_1,
                rule_2: kiyakuRulesState.rule_2,
                rule_3: kiyakuRulesState.rule_3,
                rule_4: kiyakuRulesState.rule_4,
                rule_5: kiyakuRulesState.rule_5,
                rule_6: kiyakuRulesState.rule_6,
                rule_7: kiyakuRulesState.rule_7,
                rule_8: kiyakuRulesState.rule_8,
                rule_9: kiyakuRulesState.rule_9,
                rule_10: kiyakuRulesState.rule_10,
                rule_11: kiyakuRulesState.rule_11,
                rule_12: kiyakuRulesState.rule_12,
                rule_13: kiyakuRulesState.rule_13,
                rule_14: kiyakuRulesState.rule_14,
                rule_15: kiyakuRulesState.rule_15,
                rule_16: kiyakuRulesState.rule_16,
                rule_17: kiyakuRulesState.rule_17,
            }
        };
    };

    // Helper function to check if a specific settlement type requires rules
    const isSettlementTypeSelected = (settlementType: string): boolean => {
        if (!agxMerchantParams) return false;
        
        switch (settlementType) {
            case 'card':
                return Boolean(agxMerchantParams.agxSettlementCard || agxMerchantParams.agxSettlementPackage1 || agxMerchantParams.agxSettlementPackage2);
            case 'traffic':
                return Boolean(agxMerchantParams.agxSettlementTraffic);
            case 'nanaco':
                return Boolean(agxMerchantParams.agxSettlementNanaco);
            case 'waon':
                return Boolean(agxMerchantParams.agxSettlementWaon);
            case 'edy':
                return Boolean(agxMerchantParams.agxSettlementEdy);
            case 'aid':
                return Boolean(agxMerchantParams.agxSettlementAid);
            case 'qrCode':
                return Boolean(agxMerchantParams.agxSettlementQrCode);
            case 'sme':
                return Boolean(agxMerchantParams.agxBusinessType === 283260008);
            default:
                return false;
        }
    };

    // Helper function to save kiyaku state to agxMerchantParams
    const saveKiyakuState = (setAgxMerchantParams?: (data: AgxMerchantParams | null) => void) => {
        if (setAgxMerchantParams && agxMerchantParams) {
            setAgxMerchantParams({
                ...agxMerchantParams,
                agxKiyakuRules: kiyakuRulesState,
                agxKiyakuLabels: kiyakuLabelState
            });
        }
    };

    return {
        kiyakuRulesState,
        kiyakuLabelState,
        handleKiyakuClickCheckbox,
        handleKiyakuPDFOpen,
        handleKiyakuCheck,
        setKiyakuRulesState,
        setKiyakuLabelState,
        getSelectedSettlementTypes,
        getValidationStatus,
        isSettlementTypeSelected,
        saveKiyakuState
    };
}; 