export enum Roles {
  ROLE_MEMBER = "ROLE_MEMBER",
  ROLE_ADMIN = "ROLE_ADMIN"
}
export enum STORE {
  CREPICO = "crepico",
  PAYGATE = "paygate"
}
export enum AccountTypes {
  STORE = 2,
  ADMIN_STORE = 1,
  APPLICATION = 3,
  APPLICATION_STEPS = 4,
  APPLICATION_COMPLETE = 5,
  APPLICATION_CANCEL = 6,
  APPLICATION_ACCEPTED_CORRECTIONS = 7,
  APPLICATION_REJECTED = 8,
}

export enum TypeStore {
  STORE_CREPICO = 10,
  STORE_PAYGATE = 11,
  STORE_MIGRATE = 12
}
