import { useState, useEffect } from 'react';
import Box from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { DisplayField } from './ui/Field';
import { STEP } from '@/constants/common.constant';
import { ApplicationStepProps } from '../types';
import ConfirmDialog from '@/components/ConfirmDialog';
import { agxFeeRateService } from '../services/agxFeeRateService';
import { formatPhoneNumber, gender } from '../utils';

const initDataFeeRate = {
    creditVisaRate: '',
    creditUnionRate: '',
    creditJcbRate: '',
    transportationRate: '',
    nanacoRate: '',
    waonRate: '',
    edyRate: '',
    idRate: '',
    quicpayRate: '',
    qrBankpayRate: '',
    qrOtherBankpayRate: '',
};

export const ConfirmPaygate = ({ setStep, agxMerchantParams, updateMerchant }: ApplicationStepProps) => {
    const [activeTab, setActiveTab] = useState('請求情報');
    const [feeRate, setFeeRate] = useState(initDataFeeRate);
    const [show, setShow] = useState(false);

    // Format fee helper function
    const formatFee = (amount: number) => {
        return amount.toLocaleString("ja-JP");
    };

    // Calculate fees
    const fees = {
        initialFee: (agxMerchantParams?.agxNumberOfTerminal || 0) * 59800,
        monthlyFee: (() => {
            let monthlyTotal = 0;
            const terminals = agxMerchantParams?.agxNumberOfTerminal || 0;
            monthlyTotal += terminals * 600; // Credit card
            if (agxMerchantParams?.agxSettlementTraffic) monthlyTotal += terminals * 400;
            if (agxMerchantParams?.agxSettlementNanaco) monthlyTotal += terminals * 400;
            if (agxMerchantParams?.agxSettlementWaon) monthlyTotal += terminals * 400;
            if (agxMerchantParams?.agxSettlementEdy) monthlyTotal += terminals * 400;
            if (agxMerchantParams?.agxSettlementAid) monthlyTotal += terminals * 400;
            if (agxMerchantParams?.agxSettlementQuicpay) monthlyTotal += terminals * 400;
            if (agxMerchantParams?.agxSettlementQrCode) monthlyTotal += terminals * 600;
            return monthlyTotal;
        })()
    };

    // Format date helper function
    const formatDate = (value: string) => {
        if (value && value.length > 0) {
            const dates = value.split('-');
            const result = dates[0] + '年' + dates[1] + '月';
            return result;
        } else {
            return '';
        }
    };

    // Format date Japan helper function
    const formatDateJapan = (value: string) => {
        if (value && value.length > 0) {
            const dates = value.split('-');
            const result = dates[0] + '年' + dates[1] + '月' + dates[2] + '日';
            return result;
        } else {
            return '';
        }
    };

    // Calculate total money for electronic money
    const totalMoney = () => {
        let result = 0;
        const baseMoney = (agxMerchantParams?.agxNumberOfTerminal || 0) * 1 * 400;
        if (agxMerchantParams?.agxSettlementTraffic) {
            result += baseMoney;
        }
        if (agxMerchantParams?.agxSettlementNanaco) {
            result += baseMoney;
        }
        if (agxMerchantParams?.agxSettlementWaon) {
            result += baseMoney;
        }
        if (agxMerchantParams?.agxSettlementEdy) {
            result += baseMoney;
        }
        if (agxMerchantParams?.agxSettlementAid) {
            result += baseMoney;
        }
        if (agxMerchantParams?.agxSettlementQuicpay) {
            result += baseMoney;
        }
        return result.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1);
    };

    // Get money for specific service
    const getMoney = (param: number) => {
        const money = (agxMerchantParams?.agxNumberOfTerminal || 0) * 1 * param * 1;
        const result = money.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1);
        return result;
    };

    // Get fee rate data
    const getAllDataFeeRate = async () => {
        try {
            // Mock API call - replace with actual service call
            const { data } = await agxFeeRateService.getAllData(agxMerchantParams?.agxBusinessType?.toString());

            // For now, set default values
            setFeeRate({
                creditVisaRate: data.creditVisaRate,
                creditUnionRate: data.creditUnionRate,
                creditJcbRate: data.creditJcbRate,
                transportationRate: data.transportationRate,
                nanacoRate: data.nanacoRate,
                waonRate: data.waonRate,
                edyRate: data.edyRate,
                idRate: data.idRate,
                quicpayRate: data.quicpayRate,
                qrBankpayRate: data.qrBankpayRate,
                qrOtherBankpayRate: data.qrOtherBankpayRate,
            });
        } catch (error) {
            console.error('Error fetching fee rate:', error);
            setFeeRate(initDataFeeRate);
        }
    };

    const handleConfirmAndProceed = async () => {
        setShow(false);
        try {
            // Chuyển sang bước COMPLETE ngay lập tức
            // handleSaveAll sẽ được gọi khi vào màn Complete
            setStep(STEP.COMPLETE);
        } catch (error) {
            console.error('Error changing step:', error);
            alert('Có lỗi xảy ra. Vui lòng thử lại.');
        }
    };

    // Load fee rate data on component mount
    useEffect(() => {
        getAllDataFeeRate();
    }, [agxMerchantParams?.agxBusinessType]);

    const handleTabClick = (tabName: string) => {
        setActiveTab(tabName);
    };

    const renderTabContent = () => {
        switch (activeTab) {
            case '請求情報':
                return (
                    <div className="space-y-[67px]">
                        {/* 審査完了後の案内 */}
                        <div className="p-4 sm:p-6 rounded-lg">
                            <h3 className="text-lg sm:text-xl lg:text-[1.75rem] font-medium mb-2 text-[#707070]">審査完了後にご請求が確定します</h3>
                            <p className="text-sm sm:text-[0.9375rem] text-[#C44546]">
                                代理店経由のお申し込みの場合、初期費用は代理店からのご請求となります
                            </p>
                        </div>

                        {/* 初期費用 */}
                        <div className="flex flex-col lg:flex-row px-4 sm:px-6 lg:pl-10">
                            <div className="w-full lg:w-[630px] xl:w-[730px]">
                                <div className="space-y-[67px]">
                                    <div className="flex flex-col justify-between items-center py-2 sm:py-3 border-b border-[#707070]">
                                        <div className="w-full text-lg sm:text-xl lg:text-[1.75rem] text-[#707070] mb-4 sm:mb-6 lg:mb-[28px]">
                                            初期費用 | PAYGATE
                                        </div>
                                        <div className="w-full text-end text-lg sm:text-xl lg:text-[1.75rem] text-[#707070]">
                                            {getMoney(600)} 円 (税抜)*1
                                        </div>
                                    </div>

                                    {/* 電子マネー - Only display when at least 1 type is selected */}
                                    {(agxMerchantParams?.agxSettlementTraffic ||
                                        agxMerchantParams?.agxSettlementNanaco ||
                                        agxMerchantParams?.agxSettlementWaon ||
                                        agxMerchantParams?.agxSettlementEdy ||
                                        agxMerchantParams?.agxSettlementAid ||
                                        agxMerchantParams?.agxSettlementQuicpay) && (
                                            <div className="flex flex-col justify-between items-center py-2 sm:py-3 border-b border-[#707070]">
                                                <div className="w-full text-lg sm:text-xl lg:text-[1.75rem] text-[#707070] mb-4 sm:mb-6 lg:mb-[28px]">
                                                    月額費用 | 電子マネー
                                                </div>
                                                <div className="w-full text-end text-lg sm:text-xl lg:text-[1.75rem] text-[#707070]">
                                                    {totalMoney()} 円 (税抜)
                                                </div>
                                            </div>
                                        )}

                                    {/* QRコード決済 - Only display when selected */}
                                    {agxMerchantParams?.agxSettlementQrCode && (
                                        <div className="flex flex-col justify-between items-center py-2 sm:py-3 border-b border-[#707070]">
                                            <div className="w-full text-lg sm:text-xl lg:text-[1.75rem] text-[#707070] mb-4 sm:mb-6 lg:mb-[28px]">
                                                月額費用 | QRコード決済
                                            </div>
                                            <div className="w-full text-end text-lg sm:text-xl lg:text-[1.75rem] text-[#707070]">
                                                {getMoney(600)} 円 (税抜)
                                            </div>
                                        </div>
                                    )}

                                    {/* 決済手数料率 */}
                                    <div className="mt-6 sm:mt-8">
                                        <div className="text-lg sm:text-xl lg:text-[1.75rem] text-[#707070] mb-3 sm:mb-4">決済手数料率</div>
                                        <div className="space-y-1 sm:space-y-2 text-sm sm:text-base lg:text-[1rem] text-[#707070]">
                                            {/* クレジットカード rates - Always display */}
                                            <div>VISA・Mastercard: {feeRate?.creditVisaRate || ''}%</div>
                                            <div>銀聯: {feeRate?.creditUnionRate || ''}%</div>
                                            <div>JCB他: {feeRate?.creditJcbRate || ''}% *2</div>

                                            {/* 電子マネー rates - Only display when selected */}
                                            {agxMerchantParams?.agxSettlementTraffic && (
                                                <div>交通系: {feeRate?.transportationRate || ''}%</div>
                                            )}
                                            {agxMerchantParams?.agxSettlementNanaco && (
                                                <div>nanaco: {feeRate?.nanacoRate || ''}%</div>
                                            )}
                                            {agxMerchantParams?.agxSettlementWaon && (
                                                <div>WAON: {feeRate?.waonRate || ''}%</div>
                                            )}
                                            {agxMerchantParams?.agxSettlementEdy && (
                                                <div>Edy: {feeRate?.edyRate || ''}%</div>
                                            )}
                                            {agxMerchantParams?.agxSettlementAid && (
                                                <div>iD: {feeRate?.idRate || ''}%</div>
                                            )}
                                            {agxMerchantParams?.agxSettlementQuicpay && (
                                                <div>QUICPay: {feeRate?.quicpayRate || ''}% *2</div>
                                            )}

                                            {/* QRコード決済 rates - Only display when selected */}
                                            {agxMerchantParams?.agxSettlementQrCode && (
                                                <>
                                                    <div>Bank Pay: {feeRate?.qrBankpayRate || ''}%</div>
                                                    <div>Bank Pay以外: {feeRate?.qrOtherBankpayRate || ''}%</div>
                                                </>
                                            )}
                                        </div>
                                    </div>

                                    {/* 費用合計 */}
                                    <div className="mt-8 sm:mt-10 lg:mt-12 pt-6 sm:pt-8 border-t border-[#707070]">
                                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-0">
                                            <div className="flex-1">
                                                <div className="text-lg sm:text-xl lg:text-[1.75rem] text-[#707070] mb-2">初期費用</div>
                                                <div className="text-xl sm:text-2xl lg:text-[2rem] font-bold text-[#707070]">
                                                    {(() => {
                                                        // 初期費用計算: 端末代 + セットアップ費用等
                                                        const terminalCost = (agxMerchantParams?.agxNumberOfTerminal || 0) * 59800;
                                                        return terminalCost.toLocaleString("ja-JP");
                                                    })()}円(税抜)
                                                </div>
                                            </div>
                                            <div className="flex-1 sm:text-right">
                                                <div className="text-lg sm:text-xl lg:text-[1.75rem] text-[#707070] mb-2">月額費用</div>
                                                <div className="text-xl sm:text-2xl lg:text-[2rem] font-bold text-[#707070]">
                                                    {(() => {
                                                        let monthlyTotal = 0;
                                                        const terminals = agxMerchantParams?.agxNumberOfTerminal || 0;

                                                        // クレジットカード: 600円/台
                                                        monthlyTotal += terminals * 600;

                                                        // 電子マネー: 400円/台 per service
                                                        if (agxMerchantParams?.agxSettlementTraffic) monthlyTotal += terminals * 400;
                                                        if (agxMerchantParams?.agxSettlementNanaco) monthlyTotal += terminals * 400;
                                                        if (agxMerchantParams?.agxSettlementWaon) monthlyTotal += terminals * 400;
                                                        if (agxMerchantParams?.agxSettlementEdy) monthlyTotal += terminals * 400;
                                                        if (agxMerchantParams?.agxSettlementAid) monthlyTotal += terminals * 400;
                                                        if (agxMerchantParams?.agxSettlementQuicpay) monthlyTotal += terminals * 400;

                                                        // QRコード決済: 600円/台
                                                        if (agxMerchantParams?.agxSettlementQrCode) monthlyTotal += terminals * 600;

                                                        return monthlyTotal.toLocaleString("ja-JP");
                                                    })()}円(税抜)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 注意事項 */}
                        <div className="px-4 sm:px-6 lg:pl-10 space-y-2 text-sm sm:text-[0.9375rem] mt-4 sm:mt-6">
                            <div>*1 月額費用発生月の直近3ヶ月にご利用がある場合、決済端末1台分のクレジットカード月額費用は値引きされます。なお、月額費用は銀行振込またはクレジットカードにて決済されますが、銀行振込の場合は、事務取扱手数料500円（税抜）をご負担いただきます。詳細は月額費用の請求・領収書ページをご参照ください。</div>
                            <div>*2 JCB・AMEX・Diners・DiscoverやQUICPayは、株式会社ジェーシービーと既にご契約がある場合、そのご契約に基づいた決済手数料率になります（精算も株式会社ジェーシービーから別途行われます）。</div>
                            <div className="text-red-500 font-medium">【ご注意】月額費用は、それぞれの決済種別において、決済事業者の審査（約1-3ヶ月）後に端末上ご利用できる状態になった時から発生します。 （クレジットカードの月額費用以外は、ご利用有無にかかわらず、発生する手数料ですので、ご注意ください）</div>
                        </div>
                    </div>
                );
            case '法人情報':
                return (
                    <div className="space-y-[67px]">
                        <DisplayField label="法人名" value={agxMerchantParams?.agxCorporateName || ''} required />
                        <DisplayField label="法人名（カナ）" value={agxMerchantParams?.agxCorporatePhoneticName || ''} required />
                        <DisplayField label="法人名（英字）" value={agxMerchantParams?.agxCorporateEnglishName || ''} required />
                        <DisplayField label="法人番号" value={agxMerchantParams?.agxCorporateNumber || ''} required />
                        <DisplayField label="郵便番号" value={agxMerchantParams?.agxCorporatePostalCode || ''} required />
                        <DisplayField label="都道府県" value={agxMerchantParams?.agxCorporatePrefecture || ''} required />
                        <DisplayField label="市町村" value={agxMerchantParams?.agxCorporateAddress1 || ''} required />
                        <DisplayField label="市町村（カナ)" value={agxMerchantParams?.agxCorporatePhoneticAddress1 || ''} required />
                        <DisplayField label="丁目番地建物名" value={agxMerchantParams?.agxCorporateAddress2 || ''} required />
                        <DisplayField label="丁目番地建物名（カナ)" value={agxMerchantParams?.agxCorporatePhoneticAddress2 || ''} required />
                        <DisplayField label="電話番号" value={formatPhoneNumber(agxMerchantParams?.agxCorporatePhoneNumber) || ''} required />
                        <DisplayField label="FAX" value={formatPhoneNumber(agxMerchantParams?.agxCorporateFaxNumber) || ''} />
                    </div>
                );
            case '代表者情報':
                return (
                    <div className="space-y-[67px]">
                        <DisplayField label="お名前" value={agxMerchantParams?.agxRepresentativeName || ''} required />
                        <DisplayField label="お名前（カナ)" value={agxMerchantParams?.agxRepresentativePhoneticName || ''} required />
                        <DisplayField label="性別" value={gender(agxMerchantParams?.agxRepresentativeGender)} required />
                        <DisplayField label="生年月日" value={agxMerchantParams?.agxRepresentativeBirthday ? formatDateJapan(agxMerchantParams?.agxRepresentativeBirthday) : ''} required />
                        <DisplayField label="郵便番号" value={agxMerchantParams?.agxRepresentativePostalCode || ''} required />
                        <DisplayField label="都道府県" value={agxMerchantParams?.agxRepresentativePrefecture || ''} required />
                        <DisplayField label="市町村" value={agxMerchantParams?.agxRepresentativeAddress1 || ''} required />
                        <DisplayField label="市町村（カナ)" value={agxMerchantParams?.agxRepresentativePhoneticAddress1 || ''} required />
                        <DisplayField label="丁目番地建物名" value={agxMerchantParams?.agxRepresentativeAddress2 || ''} required />
                        <DisplayField label="丁目番地建物名（カナ)" value={agxMerchantParams?.agxRepresentativePhoneticAddress2 || ''} required />
                        <DisplayField label="電話番号" value={formatPhoneNumber(agxMerchantParams?.agxRepresentativePhoneNumber) || ''} required />
                        <DisplayField label="FAX" value={formatPhoneNumber(agxMerchantParams?.agxRepresentativeFaxNumber) || ''} />
                    </div>
                );
            case '店舗情報':
                return (
                    <div className="space-y-[67px]">
                        <DisplayField label="店舗名" value={agxMerchantParams?.agxStoreName || ''} required />
                        <DisplayField label="店舗名（カナ)" value={agxMerchantParams?.agxStorePhoneticName || ''} required />
                        <DisplayField label="英語表記名称" value={agxMerchantParams?.agxStoreEnglishName || ''} required />
                        <DisplayField label="WEBサイトURL" value={agxMerchantParams?.agxUrl || ''} />
                        <DisplayField label="ブランド名" value={agxMerchantParams?.agxBrandName || ''} />
                        <DisplayField label="定休日" value={agxMerchantParams?.agxRegularHoliday || ''} />
                        <DisplayField label="営業時間" value={agxMerchantParams?.agxBusinesssHours || ''} />
                        <DisplayField label="郵便番号" value={agxMerchantParams?.agxStorePostalCode || ''} required />
                        <DisplayField label="都道府県" value={agxMerchantParams?.agxStorePrefecture || ''} required />
                        <DisplayField label="市町村" value={agxMerchantParams?.agxStoreAddress1 || ''} required />
                        <DisplayField label="市町村（カナ)" value={agxMerchantParams?.agxStorePhoneticAddress1 || ''} required />
                        <DisplayField label="丁目番地建物名" value={agxMerchantParams?.agxStoreAddress2 || ''} required />
                        <DisplayField label="丁目番地建物名（カナ)" value={agxMerchantParams?.agxStorePhoneticAddress2 || ''} required />
                        <DisplayField label="電話番号" value={formatPhoneNumber(agxMerchantParams?.agxStorePhoneNumber) || ''} required />
                        <DisplayField label="FAX" value={formatPhoneNumber(agxMerchantParams?.agxStoreFaxNumber) || ''} />
                    </div>
                );
            case '銀行情報':
                return (
                    <div className="space-y-[67px]">
                        <DisplayField label="銀行番号" value={agxMerchantParams?.agxBankNo || ''} required />
                        <DisplayField label="支店番号" value={agxMerchantParams?.agxBranchNo || ''} required />
                        <DisplayField label="銀行名" value={agxMerchantParams?.agxBankName || ''} required />
                        <DisplayField label="支店名" value={agxMerchantParams?.agxBranchName || ''} required />
                        <DisplayField label="銀行名（カナ）" value={agxMerchantParams?.agxBankPhoneticName || ''} required />
                        <DisplayField label="支店名（カナ）" value={agxMerchantParams?.agxBranchPhoneticName || ''} required />
                        <DisplayField label="口座種別" value={
                            agxMerchantParams?.agxAccountType == ********* ? '普通預金' :
                                agxMerchantParams?.agxAccountType == ********* ? '当座預金' : ''
                        } required />
                        <DisplayField label="口座番号" value={agxMerchantParams?.agxAccountNo || ''} required />
                        <DisplayField label="口座名義" value={agxMerchantParams?.agxAccountHolder || ''} required />
                    </div>
                );
            case 'セキュリティ情報':
                return (
                    <div className="space-y-[67px]">
                        <DisplayField label="お名前" value={agxMerchantParams?.agxContactName || ''} required />
                        <DisplayField label="お名前（カナ）" value={agxMerchantParams?.agxContactPhoneticName || ''} required />
                        <DisplayField label="メールアドレス" value={agxMerchantParams?.agxContactEmail || ''} required />
                        <DisplayField label="電話番号" value={formatPhoneNumber(agxMerchantParams?.agxContactPhoneNumber) || ''} required />
                        <DisplayField label="資本金" value={agxMerchantParams?.agxCapital ? `${agxMerchantParams.agxCapital.toLocaleString()} 万円` : ''} required />
                        <DisplayField label="従業員数" value={agxMerchantParams?.agxNumberOfEmployees ? `${agxMerchantParams.agxNumberOfEmployees} 人` : ''} required />
                        <DisplayField label="設立年月日" value={agxMerchantParams?.agxFoundingDate ? formatDate(agxMerchantParams.agxFoundingDate) : ''} required />
                        <DisplayField label="月商" value={agxMerchantParams?.agxMonthlySales ? `${agxMerchantParams.agxMonthlySales} 万円` : ''} required />
                        <DisplayField label="訪問販売" value={agxMerchantParams?.agxDoorToDoorSales ? '有' : '無'} />
                        <DisplayField label="電話勧誘販売" value={agxMerchantParams?.agxTelemarketingSales ? '有' : '無'} />
                        <DisplayField label="連鎖販売取引" value={agxMerchantParams?.agxPyramidScheme ? '有' : '無'} />
                        <DisplayField label="業務提供誘引販売取引" value={agxMerchantParams?.agxBusinessOpportunityRelatedSales ? '有' : '無'} />
                        <DisplayField label="特定継続的役務" value={agxMerchantParams?.agxSpecifiedContinuousServices ? '有' : '無'} />
                        <DisplayField label="クレジットカード情報の保持状況について" value={
                            agxMerchantParams?.agxCardInformationRetentionStatus === ********* ? '保持している' :
                                agxMerchantParams?.agxCardInformationRetentionStatus === ********* ? '保持していない' :
                                    agxMerchantParams?.agxCardInformationRetentionStatus === 283260002 ? '非保持化の予定あり' : ''
                        } />
                        <DisplayField label="PCI DSSの準拠状況について" value={
                            agxMerchantParams?.agxPcidssStatus === ********* ? '準拠している' :
                                agxMerchantParams?.agxPcidssStatus === ********* ? '準拠予定なし' :
                                    agxMerchantParams?.agxPcidssStatus === 283260002 ? '準拠予定あり' : ''
                        } date={agxMerchantParams?.agxPcidssStatus === 283260002 ? agxMerchantParams?.agxPcidssExpectedComplianceDate : ''} />
                        <DisplayField label="本人認証サービス(3Dセキュア)" value={
                            agxMerchantParams?.agxThreeDSecureStatus === ********* ? '導入済み' :
                                agxMerchantParams?.agxThreeDSecureStatus === ********* ? '導入予定なし' :
                                    agxMerchantParams?.agxThreeDSecureStatus === 283260002 ? '導入予定' : ''
                        } date={agxMerchantParams?.agxThreeDSecureStatus === 283260002 ? agxMerchantParams?.agxThreeDSecureDate : ''} />
                        <DisplayField label="セキュリティコードチェック" value={
                            agxMerchantParams?.agxSecurityCodeCheckStatus === ********* ? '導入済み' :
                                agxMerchantParams?.agxSecurityCodeCheckStatus === ********* ? '導入予定なし' :
                                    agxMerchantParams?.agxSecurityCodeCheckStatus === 283260002 ? '導入予定' : ''
                        } date={agxMerchantParams?.agxSecurityCodeCheckStatus === 283260002 ? agxMerchantParams?.agxSecurityCodeCheckDate : ''} />
                        <DisplayField label="不正配送先情報の活用" value={
                            agxMerchantParams?.agxIllegalDeliveryDestinationStatus === ********* ? '導入済み' :
                                agxMerchantParams?.agxIllegalDeliveryDestinationStatus === ********* ? '導入予定なし' :
                                    agxMerchantParams?.agxIllegalDeliveryDestinationStatus === 283260002 ? '導入予定' : ''
                        } date={agxMerchantParams?.agxIllegalDeliveryDestinationStatus === 283260002 ? agxMerchantParams?.agxIllegalDeliveryDestinationDate : ''} />
                        <DisplayField label="属性・行動分析" value={
                            agxMerchantParams?.agxBehaviorAnalysisStatus === ********* ? '導入済み' :
                                agxMerchantParams?.agxBehaviorAnalysisStatus === ********* ? '導入予定なし' :
                                    agxMerchantParams?.agxBehaviorAnalysisStatus === 283260002 ? '導入予定' : ''
                        } date={agxMerchantParams?.agxBehaviorAnalysisStatus === 283260002 ? agxMerchantParams?.agxBehaviorAnalysisDate : ''} />
                        <DisplayField label="その他の対策" value={
                            agxMerchantParams?.agxOtherMeasuresStatus === ********* ? '導入済み' :
                                agxMerchantParams?.agxOtherMeasuresStatus === ********* ? '導入予定なし' :
                                    agxMerchantParams?.agxOtherMeasuresStatus === 283260002 ? '導入予定' : ''
                        } date={agxMerchantParams?.agxOtherMeasuresStatus === 283260002 ? agxMerchantParams?.agxOtherMeasuresDate : ''} />
                        <DisplayField label="対策内容" value={agxMerchantParams?.agxOtherMeasuresDescription || ''} />
                    </div>
                );
            case 'チョキペイ情報':
                return (
                    <div className="space-y-[67px]">
                        <DisplayField label="端末台数" value={agxMerchantParams?.agxNumberOfTerminal ? `${agxMerchantParams.agxNumberOfTerminal}台` : ''} required />
                        <DisplayField label="端末" value={'PAYGATE'} />
                        <DisplayField label="VISA・Mastercard" value={agxMerchantParams?.agxSettlementCard ? '利用する' : '利用しない'} />
                        <DisplayField label="JCB" value={agxMerchantParams?.agxSettlementJcb ? '利用する' : '利用しない'} />
                        <DisplayField label="Bank Pay" value={agxMerchantParams?.agxSettlementQrCode ? '利用する' : '利用しない'} />
                        <DisplayField label="Bank Pay以外" value={agxMerchantParams?.agxSettlementQrCode ? '利用する' : '利用しない'} />
                        <DisplayField label="交通系" value={agxMerchantParams?.agxSettlementPackage2 ? '利用する' : '利用しない'} />
                        <DisplayField label="nanaco" value={agxMerchantParams?.agxSettlementNanaco ? '利用する' : '利用しない'} />
                        <DisplayField label="WAON" value={agxMerchantParams?.agxSettlementWaon ? '利用する' : '利用しない'} />
                        <DisplayField label="Edy" value={agxMerchantParams?.agxSettlementEdy ? '利用する' : '利用しない'} />
                        <DisplayField label="iD" value={agxMerchantParams?.agxSettlementAid ? '利用する' : '利用しない'} />
                        <DisplayField label="QUICPay" value={agxMerchantParams?.agxSettlementQuicpay ? '利用する' : '利用しない'} />
                        <DisplayField label="端末代金" value={`${formatFee(fees.initialFee)}円（税抜）`} />
                        <DisplayField label="月額費用" value={`${formatFee(fees.monthlyFee)}円（税抜）`} />
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <>
            {/* Navigation Tabs */}
            <div className="mb-32 sm:mb-6 lg:mb-8">
                <nav className="flex overflow-x-auto gap-0 border-b border-gray-200 scrollbar-hide">
                    <div className="flex min-w-full w-full justify-stretch">
                        <Button
                            onClick={() => handleTabClick('請求情報')}
                            className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === '請求情報'
                                ? 'text-teal-600 border-b-2 border-teal-600'
                                : 'text-gray-500 border-b-2 border-transparent'
                                }`}
                        >
                            請求情報
                        </Button>
                        {agxMerchantParams?.agxBusinessForm === ********* && (
                            <Button
                                onClick={() => handleTabClick('法人情報')}
                                className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === '法人情報'
                                    ? 'text-teal-600 border-b-2 border-teal-600'
                                    : 'text-gray-500 border-b-2 border-transparent'
                                    }`}
                            >
                                法人情報
                            </Button>
                        )}
                        <Button
                            onClick={() => handleTabClick('代表者情報')}
                            className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === '代表者情報'
                                ? 'text-teal-600 border-b-2 border-teal-600'
                                : 'text-gray-500 border-b-2 border-transparent'
                                }`}
                        >
                            代表者情報
                        </Button>
                        <Button
                            onClick={() => handleTabClick('店舗情報')}
                            className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === '店舗情報'
                                ? 'text-teal-600 border-b-2 border-teal-600'
                                : 'text-gray-500 border-b-2 border-transparent'
                                }`}
                        >
                            店舗情報
                        </Button>
                        <Button
                            onClick={() => handleTabClick('銀行情報')}
                            className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === '銀行情報'
                                ? 'text-teal-600 border-b-2 border-teal-600'
                                : 'text-gray-500 border-b-2 border-transparent'
                                }`}
                        >
                            銀行情報
                        </Button>
                        <Button
                            onClick={() => handleTabClick('セキュリティ情報')}
                            className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === 'セキュリティ情報'
                                ? 'text-teal-600 border-b-2 border-teal-600'
                                : 'text-gray-500 border-b-2 border-transparent'
                                }`}
                        >
                            セキュリティ情報
                        </Button>
                        <Button
                            onClick={() => handleTabClick('チョキペイ情報')}
                            className={`flex-1 px-1 sm:px-2 py-2 sm:py-3 text-sm sm:text-base lg:text-[1.75rem] mb-1 font-normal rounded-none bg-white hover:bg-white whitespace-nowrap ${activeTab === 'チョキペイ情報'
                                ? 'text-teal-600 border-b-2 border-teal-600'
                                : 'text-gray-500 border-b-2 border-transparent'
                                }`}
                        >
                            チョキペイ情報
                        </Button>
                    </div>
                </nav>
            </div>

            {renderTabContent()}

            <Box className="flex flex-col gap-3 sm:gap-4 mt-6 sm:mt-8 justify-center items-center px-4">
                <Button
                    onClick={() => setShow(true)}
                    className="rounded-xl w-full max-w-[369px] h-12 sm:h-14 lg:h-[66px] bg-[#1AA492] text-white py-3 sm:py-4 px-4 sm:px-6 text-base sm:text-lg font-medium hover:bg-[#159080] transition-colors"
                >
                    申込を確定する
                </Button>

                <Button
                    onClick={() => setStep(STEP.KIYAKU)}
                    className="rounded-xl w-full max-w-[369px] h-12 sm:h-14 lg:h-[66px] bg-gray-400 text-white py-3 sm:py-4 px-4 sm:px-6 text-base sm:text-lg font-medium hover:bg-gray-500 transition-colors"
                >
                    戻る
                </Button>
            </Box>
            <ConfirmDialog
                open={show}
                onOpenChange={setShow}
                onConfirm={handleConfirmAndProceed}
                title="入力した内容で加盟店の申請を行います。よろしいですか？"
                confirmLabel="確定"
                confirmVariant="danger"
                cancelLabel="戻る"
                onCancel={() => setShow(false)}
            />
        </>
    );
};
