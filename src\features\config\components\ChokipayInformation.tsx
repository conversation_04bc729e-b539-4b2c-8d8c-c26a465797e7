import { Card, CardContent } from "@/components/ui/card";
import { useCallback, useEffect, useRef, useState } from "react";
import { useQueryAgxFeeRate } from "../hooks/useQueryAgxFeeRate";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { formatNumberJP, getTerminalColorName } from "@/utils/helper";
import onedot1dot1 from "/images/group_10.svg";
import onedot1dot2 from "/images/group_12.svg";
import onedot2 from "/images/group_1398.png";
import onedot3dot1 from '/images/group_23.svg';
import onedot3dot2 from '/images/group_25.svg';
import onedot3dot3 from '/images/group_27.png';
import onedot3dot4 from '/images/group_1257.png';
import onedot3dot5 from '/images/group_1259.png';
import twodot1 from '/images/group_1261.svg';
import twodot2 from '/images/group_1263.svg';
import twodot3 from '/images/group_1265.svg';
import twodot4 from '/images/group_1268.svg';
import twodot5 from '/images/group_1270.svg';
import twodot6 from '/images/group_1272.svg';
import twodot7 from '/images/group_1276.png';
import threedot1dot1 from '/images/group_1278.svg';
import threedot1dot2 from '/images/group_1280.svg';
import threedot1dot3 from '/images/group_1282.svg';
import threedot1dot4 from '/images/group_1284.svg';
import threedot1dot5 from '/images/group_1286.svg';
import threedot2dot1 from '/images/group_1288.svg';
import threedot2dot2 from '/images/group_1292.svg';
import threedot2dot3 from '/images/group_1294.svg';
import threedot2dot4 from '/images/group_1296.svg';
import threedot3 from '/images/group_1299.svg';
import threedot4 from '/images/group_1301.svg';
import threedot5 from '/images/group_1308.svg';
import threedot6 from '/images/group_1310.svg';
import threedot7 from '/images/group_1312.svg';

const moneyData = {
  money_Core_Crepico: 99800,
  money_Credit_Card_And_QRcode: 500,
  money_All_Crepico: 1500,
};

interface ChokipayInformationProps {
  merchantData: GetMerchantStatusResponse;
  merchantCrepicoData: GetMerchantStatusResponse;
}

function ChokipayInformation({ merchantData ,merchantCrepicoData}: ChokipayInformationProps) {
  const [initialFee, setInitialFee] = useState("");
  const [monthlyFee, setMonthlyFee] = useState("");
  const creditRef = useRef<HTMLDivElement>(null);
  const qrRef = useRef<HTMLDivElement>(null);
  const emoneyRef = useRef<HTMLDivElement>(null);
  const monthlyRef = useRef<HTMLDivElement>(null);
  
  // Hàm scroll tới ref
  const scrollToRef = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  const { data: feeRate } = useQueryAgxFeeRate({
    agxBusinessType: merchantData?.agxBusinessType,
  });
  const caclMoney = useCallback(
    (agxNumberOfTerminal, agxSettlementPackage1, agxSettlementPackage2) =>
      () => {
        const moneyCrepico =
          parseInt(agxNumberOfTerminal) *
          ((agxSettlementPackage1
            ? moneyData.money_Credit_Card_And_QRcode
            : 0) +
            (agxSettlementPackage2 ? moneyData.money_All_Crepico : 0));
        setInitialFee(
          (agxNumberOfTerminal * moneyData.money_Core_Crepico)
            .toLocaleString("ja-JP", { style: "currency", currency: "JPY" })
            .substring(1)
        );
        setMonthlyFee(
          moneyCrepico
            .toLocaleString("ja-JP", { style: "currency", currency: "JPY" })
            .substring(1)
        );
      },
    []
  );

  const getMoney = (param) => {
    const money = merchantData?.agxNumberOfTerminal * 1 * param * 1;
    const result = money.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1);
    return result;
  }

  const totalMoney = () => {
    let result = 0;
    const baseMoney = merchantData?.agxNumberOfTerminal * 1 * 400;
    if (merchantData?.agxSettlementTraffic) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementNanaco) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementWaon) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementEdy) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementAid) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementQuicpay) {
      result += baseMoney;
    }
    return result.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1);
  }

  useEffect(() => {
    caclMoney(
      merchantData?.agxNumberOfTerminal,
      merchantData?.agxSettlementPackage1,
      merchantData?.agxSettlementPackage2
    )();
  }, []);
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        {/* Thanh menu tab */}
        <div className="flex  gap-12 mb-8">
          <button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
            onClick={() => scrollToRef(creditRef)}
            type="button"
          >
            クレジットカード
          </button>
          <button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
            onClick={() => scrollToRef(qrRef)}
            type="button"
          >
            QRコード決済
          </button>
          <button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
            onClick={() => scrollToRef(emoneyRef)}
            type="button"
          >
            電子マネー
          </button>
          <button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
            onClick={() => scrollToRef(monthlyRef)}
            type="button"
          >
            月額費用
          </button>
        </div>

        <div className="grid grid-cols-12 items-center max-md:gap-1 gap-6">
          <div className="col-span-12" ref={creditRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">クレジットカード</div>
            <table className="w-full border-none">
              <tbody>
                <tr className="border-t border-gray-300 h-16">
                  <th className="w-[22px]"></th>
                  <th className="w-[22px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">ステータス</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">利用開始日</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">決済手数料率</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[166px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">備考</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                </tr>
                {/* <tr className="h-16">
                  <td colSpan={3} className="w-[167px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">クレジットカード</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">利用可能</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span>
                      <span id='settlement_credit_amount' className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{getMoney(600)}</span>
                      <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">円<sup><span style={{ fontSize: '14px' }}>*1</span></sup></span></span>
                  </td>
                  <td className="w-[156px]"></td>
                </tr> */}
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={onedot1dot1} alt="Visa" className="mr-2" />
                        <img src={onedot1dot2} alt="Visa" className="" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">利用可能</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span>
                      <span id="span_credit_visa_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.creditVisaRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={onedot2} alt="Visa" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">利用可能</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span>
                      <span id="span_credit_visa_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.creditUnionRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={onedot3dot1} alt="two" className="mr-2" />
                        <img src={onedot3dot2} alt="two" className="mr-2" />
                        <img src={onedot3dot3} alt="two" className="mr-2" />
                        <img src={onedot3dot4} alt="two" className="mr-2" />
                        <img src={onedot3dot5} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">利用可能</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span>
                      <span id="span_credit_jcb_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.creditJcbRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%
                        {/* <sup><span style={{ fontSize: '14px' }}>*2</span></sup> */}
                        </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">弊社外での契約</span>
                  </td>
                </tr>
                {/* <tr className="h-16">
                  <td colSpan={3} className="w-[157px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">電子マネー</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {(
                        merchantData?.agxSettlementTraffic || merchantData?.agxSettlementNanaco || merchantData?.agxSettlementWaon
                        || merchantData?.agxSettlementEdy || merchantData?.agxSettlementAid || merchantData?.agxSettlementQuicpay
                      ) ? '利用可能' : '-'}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {(
                        merchantData?.agxSettlementTraffic || merchantData?.agxSettlementNanaco || merchantData?.agxSettlementWaon
                        || merchantData?.agxSettlementEdy || merchantData?.agxSettlementAid || merchantData?.agxSettlementQuicpay
                      ) ? `${totalMoney()}円` : '-'}
                    </span>
                  </td>
                  <td className="w-[156px]"></td>
                </tr> */}
                
                {/* <tr className="h-16">
                  <td colSpan={3} className="w-[157px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">QRコード決済</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_code_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? `${getMoney(600)}円` : '-'}</span>
                  </td>
                  <td className="w-[156px]"></td>
                </tr> */}
              </tbody>
            </table>
          </div>

          <div className="col-span-12"  ref={qrRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">QRコード決済</div>
            <table className="w-full border-none">
              <tbody>
                <tr className="border-t border-gray-300 h-16">
                  <th className="w-[22px]"></th>
                  <th className="w-[22px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">ステータス</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">利用開始日</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">決済手数料率</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[166px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">備考</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot1} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">取扱不可</span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot2} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">保険診療範囲外の医療行為は取扱不可</span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot3} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot4} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot5} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot6} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_paypay_rate_display">
                      <span id="span_qr_other_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrOtherBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={twodot7} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_qr_bankpay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}</span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">-</span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span id="settlement_qr_bankpay_rate_display">
                      <span id="span_qr_bankpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {feeRate?.qrBankpayRate}
                      </span>
                      <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="col-span-12" ref={emoneyRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">電子マネー</div>
            <table className="w-full border-none">
              <tbody>
                
                <tr className="border-t border-gray-300 h-16">
                  <th className="w-[22px]"></th>
                  <th className="w-[22px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">ステータス</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">利用開始日</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">決済手数料率</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[166px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">備考</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={threedot1dot1} alt="two" className="mr-2" />
                        <img src={threedot1dot2} alt="two" className="mr-2" />
                        <img src={threedot1dot3} alt="two" className="mr-2" />
                        <img src={threedot1dot4} alt="two" className="mr-2" />
                        <img src={threedot1dot5} alt="two" className="mr-2" />
                      </span>
                      <span className="flex items-center">
                        <img src={threedot2dot1} alt="two" className="mr-2" />
                        <img src={threedot2dot2} alt="two" className="mr-2" />
                        <img src={threedot2dot3} alt="two" className="mr-2" />
                        <img src={threedot2dot4} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_traffic_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementTraffic ? "利用可能" : "未申込"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {/* {merchantData?.agxSettlementTraffic ? `(${getMoney(400)}円)` : '-'} */}
                      -
                      </span>
                  </td>
                  <td className="w-[156px] text-center">
                    {merchantData?.agxSettlementTraffic
                      ?
                      <span>
                        <span id="span_transportation_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                          {feeRate?.transportationRate}
                        </span>
                        <span className="sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">%</span>
                      </span>
                      :
                      <span
                        id="settlement_traffic_rate_hiden"
                        style={{
                          display: merchantData?.agxSettlementTraffic
                            ? "none"
                            : "",
                        }} className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]"
                      >
                        -
                      </span>
                    }
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={threedot3} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_waon_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementWaon ? "利用可能" : "未申込"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {/* {merchantData?.agxSettlementWaon ? `(${getMoney(400)}円)` : '-'} */}
                      -
                      </span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_waon_rate_display"
                    >
                      <span id="span_waon_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementWaon ? `${feeRate?.waonRate}%` : '-'}</span>
                    </span>
                  </td>
                </tr>
                
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={threedot4} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_aid_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementAid ? "利用可能" : "未申込"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {/* {merchantData?.agxSettlementAid ? `(${getMoney(400)}円)` : '-'} */}
                      -
                      </span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_aid_rate_display"
                    >
                      <span id="span_id_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementAid ? `${feeRate?.idRate}%` : '-'}</span>
                    </span>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="flex items-center">
                        <img src={threedot5} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_nanaco_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementNanaco ? "利用可能" : "未申込"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {/* {merchantData?.agxSettlementNanaco ? `(${getMoney(400)}円)` : '-'} */}
                      -
                      </span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_nanaco_rate_display"
                    >
                      <span id="span_nanaco_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementNanaco ? `${feeRate?.nanacoRate}%` : '-'}</span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <button
                      className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
                      type="button"
                    >
                      お申し込みはこちら
                    </button>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={threedot6} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_edy_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementEdy ? "利用可能" : "未申込"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {/* {merchantData?.agxSettlementEdy ? `(${getMoney(400)}円)` : '-'} */}
                      -
                      </span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_edy_rate_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]"
                    >
                      <span id="span_edy_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">{merchantData?.agxSettlementWaon ? `${feeRate?.edyRate}%` : '-'}</span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <button
                      className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
                      type="button"
                    >
                      お申し込みはこちら
                    </button>
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      <span className="flex items-center">
                        <img src={threedot7} alt="two" className="mr-2" />
                      </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_QUICPay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {merchantData?.agxSettlementQuicpay ? "利用可能" : "未申込"}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                      {/* {merchantData?.agxSettlementQuicpay ? `(${getMoney(400)}円)` : '-'} */}
                      -
                      </span>
                  </td>
                  <td className="w-[156px] text-center">
                    <span
                      id="settlement_QUICPay_rate_display"
                    >
                      <span id="span_quicpay_rate" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {merchantData?.agxSettlementQuicpay 
                        ? (<>`${feeRate?.quicpayRate}% `<sup><span style={{ fontSize: '14px' }}>*2</span></sup></> )
                        : '-'}</span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <button
                      className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition"
                      type="button"
                    >
                      お申し込みはこちら
                    </button>
                  </td>
                </tr>
                
              </tbody>
            </table>
        </div>

        <div className="col-span-12" ref={monthlyRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">月額費用</div>
            <table className="w-full border-none">
              <tbody>
              <tr className="border-t border-gray-300 h-16">
                  <th className="w-[22px]"></th>
                  <th className="w-[22px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[114px]"></th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span className="font-normal">ステータス</span>
                    <div className="w-[80%] border-b border-gray-300 mx-auto"></div>
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                  </th>
                  <th className="w-[100px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                  </th>
                  <th className="w-[166px] text-center  sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                  </th>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    クレジットカード・QRコード
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                  <span id="settlement_qr_paypay_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    <span>
                      <span id='settlement_credit_amount' className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        {merchantData?.agxSettlementQrCode ? `${formatNumberJP(getMoney(600))}円` : '-'}
                      </span>
                    </span>
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                  </td>
                  <td className="w-[156px] text-center">
                  </td>
                  <td className="w-[94px] text-center">
                  </td>
                </tr>
                <tr className="h-16">
                  <td className="w-[22px]"></td>
                  <td className="w-[22px]"></td>
                  <td colSpan={2} className="w-[136px]">
                    <span className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                        電子マネー
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                    <span id="settlement_aid_display" className=" sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                    {(
                        merchantData?.agxSettlementTraffic || merchantData?.agxSettlementNanaco || merchantData?.agxSettlementWaon
                        || merchantData?.agxSettlementEdy || merchantData?.agxSettlementAid || merchantData?.agxSettlementQuicpay
                      ) ? `${formatNumberJP(totalMoney())}円` : '-'}
                    </span>
                  </td>
                  <td className="w-[94px] text-center">
                  </td>
                  <td className="w-[156px] text-center">
                  </td>
                  <td className="w-[94px] text-center">
                  </td>
                </tr>
              </tbody>
            </table>
        </div>
        </div>

        <div className="grid grid-cols-12 items-start max-md:gap-1 gap-6">
          <div className="col-span-12">
            {/* <div className="flex items-start ">
              <div className="mt-0.5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">
                <span>*</span>1 月額費用発生月の直近3ヶ月にご利用がある場合、決済端末1台分のクレジットカード月額費用は値引きされます。<br />
                &emsp;詳細は月額費用の請求・領収書ページをご参照ください。
              </div>
            </div> */}
            <div className="mt-4 text-red-500 sm:text-[18px] md:text-[20px] text-[24px]">
              <div>
                クレジットカードの月額費用は、対象月の直近3ヶ月間で利用があれば、無料になります。
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default ChokipayInformation;
