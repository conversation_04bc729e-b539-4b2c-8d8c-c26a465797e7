import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import apiService from '@/services/api';
import { checkExpToken } from '@/utils/checkExpToken';
import { useSignup } from '../../hooks/useSignup';
import { Button } from '@/components/ui/button';
import { IMerchantCoreType } from '../../types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { registrationSchema } from '@/features/auth/schema';
import { Input } from '@/components/ui/input';
import { removeAuthToken } from '@/config/axios';

interface Props {
    data: IMerchantCoreType;
    onNext: () => void;
    onPrev: () => void;
    onUpdate: (data: Partial<IMerchantCoreType>) => void;
}

type RegistrationForm = {
    password: string;
    confirmPassword: string;
};

const Registration = () => {
    const { token, type } = useParams();
    const { signupAsync, isLoading } = useSignup();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const navigate = useNavigate();

    const { register, handleSubmit, formState: { errors } } = useForm<RegistrationForm>({
        resolver: zodResolver(registrationSchema),
    });

    const onSubmit = async (formData: RegistrationForm) => {
        if (checkExpToken(token)) {
            removeAuthToken();
            // apiService.setHeader("choqipay-token-signup", `Bearer ${token}`);
            apiService.setHeader("Authorization", `Bearer ${token}`);
            apiService.setHeader("Content-Type", `application/json`);

            await signupAsync({
                email: null,
                username: null,
                password: formData.password,
                memberType: type
            });

            navigate('/register-merchant');
        } else {
            navigate("/login", { replace: true });
        }
    };

    return (
        <>
            <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-[747px] max-w-full flex-col items-stretch text-[28px] text-[rgba(112,112,112,1)] font-normal whitespace-nowrap mt-[33px] pt-[58px] pb-[92px] px-12 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
                <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
                    <div className="relative mt-11 max-md:mt-10">
                        <Input
                            {...register('password')}
                            id="password"
                            type={showPassword ? 'text' : 'password'}
                            placeholder="パスワード"
                            className={`bg-white border w-full h-[66px] px-[19px] rounded-[13px] border-solid !text-[28px] text-[rgba(112,112,112,1)] placeholder:text-[rgba(112,112,112,1)] focus:outline-none focus:ring-2 focus:ring-[rgba(25,164,146,1)] focus:border-transparent max-md:pr-5 ${errors.password ? 'border-red-500' : 'border-[rgba(112,112,112,1)]'}`}
                            required
                        />
                        {errors.password && (
                            <div className="text-red-500 text-[16px] mt-2 whitespace-normal">
                                {errors.password.message as string}
                            </div>
                        )}
                    </div>
                    <div className="relative mt-11 max-md:mt-10">
                        <Input
                            {...register('confirmPassword')}
                            id="confirmPassword"
                            type={showConfirmPassword ? 'text' : 'password'}
                            placeholder="パスワードの確認"
                            className={`bg-white border w-full h-[66px] px-[19px] rounded-[13px] border-solid !text-[28px] text-[rgba(112,112,112,1)] placeholder:text-[rgba(112,112,112,1)] focus:outline-none focus:ring-2 focus:ring-[rgba(25,164,146,1)] focus:border-transparent max-md:pr-5 ${errors.confirmPassword ? 'border-red-500' : 'border-[rgba(112,112,112,1)]'}`}
                            required
                        />
                        {errors.confirmPassword && (
                            <div className="text-red-500 text-[16px] mt-2 whitespace-normal">
                                {errors.confirmPassword.message as string}
                            </div>
                        )}
                        {/* <div className="text-red-500 text-[16px] mt-2 whitespace-normal">
                            {ERROR_MESSAGES[error] ?? ""}
                        </div> */}
                    </div>
                    <div className="flex justify-between space-x-6 text-sm mt-[50px] mx-auto">
                        <a href="#" className="text-teal-500 hover:text-teal-600 transition-colors text-[20px]">
                            プライバシーポリシー
                        </a>
                        <a href="#" className="text-teal-500 hover:text-teal-600 transition-colors text-[20px]">
                            加盟店規約
                        </a>
                    </div>
                    <div className="flex justify-center space-x-4 mt-0">
                        <Button
                            type="submit"
                            disabled={isLoading}
                            className={`flex items-center justify-center h-[66px] text-[28px] rounded-[13px] transition-opacity hover:bg-[#15b19d] focus:outline-none focus:ring-2 focus:ring-[#19A492] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-[70px] py-4 bg-[#19A492] text-[rgba(246,246,246,1)] w-full max-md:px-5 mt-[39px]`}
                            aria-label="同意してチョキペイIDを作成"
                            aria-disabled={isLoading}
                        >
                            <span className="relative z-10 text-[28px]">{isLoading ? '処理中...' : '同意してチョキペイIDを作成'}</span>
                        </Button>
                    </div>
                </form>
            </section>
        </>
    );
};

export default Registration;
