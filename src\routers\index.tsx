import React from "react";
import { <PERSON><PERSON>er<PERSON>outer, Routes, Route } from "react-router-dom";
import privateRoutes from "./privateRoutes";
import publicRoutes from "./publicRoutes";
import { RouteConfig } from "./publicRoutes";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { PublicRoute } from "@/components/PublicRoute";

const renderRoutes = (routes: RouteConfig[]) => {
  return routes.map((route) => {
    // For routes with children, we need to use the Outlet pattern
    if (route.children) {
      return (
        <Route
          key={route.path}
          path={route.path}
          element={
            route.requiredAccountType || route.requiredTypeStore ? (
              <ProtectedRoute requiredAccountType={route.requiredAccountType} requiredTypeStore={route.requiredTypeStore}>
                {route.element}
              </ProtectedRoute>
            ) : (
              <PublicRoute>
                {route.element}
              </PublicRoute>
            )
          }
        >
          {/* Render child routes here */}
          {route.children.map((childRoute) => (
            <Route
              key={childRoute.path}
              path={childRoute.path}
              element={
                childRoute.requiredAccountType || childRoute.requiredTypeStore || route.requiredAccountType? (
                  <ProtectedRoute requiredAccountType={childRoute.requiredAccountType || route.requiredAccountType} requiredTypeStore={childRoute.requiredTypeStore}>
                    {childRoute.element}
                  </ProtectedRoute>
                ) : (
                  <PublicRoute>
                    {childRoute.element}
                  </PublicRoute>
                )
              }
            />
          ))}
        </Route>
      );
    }

    // For routes without children
    return (
      <Route
        key={route.path}
        path={route.path}
        element={
          route.requiredAccountType || route.requiredTypeStore ? (
            <ProtectedRoute requiredAccountType={route.requiredAccountType} requiredTypeStore={route.requiredTypeStore}>
              {route.element}
            </ProtectedRoute>
          ) : (
            <PublicRoute>
              {route.element}
            </PublicRoute>
          )
        }
      />
    );
  });
};

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        {renderRoutes(publicRoutes)}
        {renderRoutes(privateRoutes)}
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;