import axiosInstance from '@/config/axios';
import { AxiosResponse, AxiosProgressEvent, AxiosRequestConfig } from 'axios';

class ApiService {
  /**
   * Get request
   * @param url - API endpoint
   * @param params - Query parameters
   * @returns Response data
   */
  async get<T>(url: string, params?: Record<string, unknown>): Promise<T> {
    const response: AxiosResponse = await axiosInstance.get(
      url,
      { params, timeout: 300000 }
    );
    return response.data;
  }

  /**
   * Post request
   * @param url - API endpoint
   * @param data - Request body
   * @returns Response data
   */
  async post<T>(url: string, data?: unknown): Promise<T> {
    const response: AxiosResponse = await axiosInstance.post(url, data);
    return response.data;
  }

  /**
   * Put request
   * @param url - API endpoint
   * @param data - Request body
   * @returns Response data
   */
  async put<T>(url: string, data?: unknown): Promise<T> {
    const response: AxiosResponse = await axiosInstance.put(url, data);
    return response.data;
  }

  /**
   * Patch request
   * @param url - API endpoint
   * @param data - Request body
   * @returns Response data
   */
  async patch<T>(url: string, data?: unknown): Promise<T> {
    const response: AxiosResponse = await axiosInstance.patch(url, data);
    return response.data;
  }

  /**
   * Delete request
   * @param url - API endpoint
   * @returns Response data
   */
  async delete<T>(url: string): Promise<T> {
    const response: AxiosResponse = await axiosInstance.delete(url);
    return response.data;
  }

  /**
   * File upload
   * @param url - API endpoint
   * @param file - File to upload
   * @param onUploadProgress - Progress event
   * @returns Response data
   */
  async uploadFile<T>(url: string, file: File, onUploadProgress?: (progressEvent: AxiosProgressEvent) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response: AxiosResponse = await axiosInstance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });

    return response.data;
  }

  /**
   * File download
   * @param url - API endpoint
   * @param filename - Filename
   * @param onDownloadProgress - Progress event
   * @returns Response data
   */
  async downloadFile(url: string, filename?: string, onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void): Promise<void> {
    const response: AxiosResponse<Blob> = await axiosInstance.get(url, {
      responseType: 'blob',
      onDownloadProgress,
    });

    // Tạo URL cho blob
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);

    // Tạo element anchor để download
    const link = document.createElement('a');
    link.href = downloadUrl;

    // Lấy filename từ response header hoặc sử dụng filename được truyền vào
    const contentDisposition = response.headers['content-disposition'] as string;
    let downloadFilename = filename;

    if (!downloadFilename && contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        downloadFilename = filenameMatch[1];
      }
    }

    // Set default filename nếu không có
    link.download = downloadFilename || 'download';

    // Trigger download
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }

  setHeader(name: string, value: string) {
    axiosInstance.defaults.headers.common[name] = value;
  }

  removeHeader(name: string) {
    delete axiosInstance.defaults.headers.common[name];
  }
}

export const apiService = new ApiService();
export default apiService;