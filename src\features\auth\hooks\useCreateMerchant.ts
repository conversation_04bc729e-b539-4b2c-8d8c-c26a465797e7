import { useToast } from '@/hooks/use-toast';
import { applicationService } from '@/features/auth/services/applicationService';
import { useMutation } from '@tanstack/react-query';
import { IMerchantCoreType } from '@/features/auth/types';
import { useAuthStore } from '@/store';

export const useCreateMerchant = () => {
    const { toast } = useToast();
    const { setAgxMerchantNo } = useAuthStore();

    const createMerchantMutation =  useMutation({
        mutationFn: async (merchant: IMerchantCoreType) => {
            const data  = await applicationService.create(merchant);
            if (data) setAgxMerchantNo(data.agxMerchantNo);
            return data;
        },
        onSuccess: () => {
            toast({
                variant: "default",
                title: '作成完了しました。',
                description: "",
            });
        },
        onError: () => {
            toast({
                variant: "destructive",
                title: '作成が失敗しました。',
                description: "",
            });
        }
    });
    return {
        createMerchant: createMerchantMutation.mutate,
        createMerchantAsync: createMerchantMutation.mutateAsync,
        isLoading: createMerchantMutation.isPending,
        isError: createMerchantMutation.isError,
        error: createMerchantMutation.error,
        reset: createMerchantMutation.reset,
    };
};