import React from 'react';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { STORE } from '@/types/globalType';
import SummaryMonthlyPage from '@/features/store/summary/components/SummaryMonthlyPage';

const SummaryMonthlyCrepico: React.FC = () => {
  const { user } = useAuthStore();
  const agxMerchantNo = user?.memberType ? user?.agxMerchantNo : user?.agxNewMerchantNo || '';
  return (
    <SummaryMonthlyPage agxMerchantNo={agxMerchantNo} type={STORE.CREPICO} />
  );
};

export default SummaryMonthlyCrepico;
