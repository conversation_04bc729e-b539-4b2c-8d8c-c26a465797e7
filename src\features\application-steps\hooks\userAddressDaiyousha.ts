import { useEffect, useState } from "react";
import { adAddressService } from "../services/addressService";
import { AgxMerchantParams } from "../types";

interface UseAddressReturn {

    // States
    agxRepresentativePostalCode: string;
    stateErrorPostalCode: boolean;
    agxRepresentativePhoneNumber1: string;
    agxRepresentativePhoneNumber2: string;
    agxRepresentativePhoneNumber3: string;
    stateErrorPhoneNumber: boolean;
    agxRepresentativeFaxNumber1: string;
    agxRepresentativeFaxNumber2: string;
    agxRepresentativeFaxNumber3: string;
    stateErrorFaxNumber: boolean;

    isLoadingAddress: boolean;
    handleFindAddress: (setValue: any, trigger: any) => Promise<void>;
    
    // Handlers
    handleChangeAgxRepresentativePostalCode: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxRepresentativePostalCode: (setValue: any, trigger: any) => void;
    handleChangeAgxRepresentativePhoneNumber1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxRepresentativePhoneNumber2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxRepresentativePhoneNumber3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxRepresentativePhoneNumber: (setValue: any, trigger: any) => void;
    handleChangeAgxRepresentativeFaxNumber1: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxRepresentativeFaxNumber2: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleChangeAgxRepresentativeFaxNumber3: (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleSetDataAgxRepresentativeFaxNumber: (setValue: any, trigger: any) => void;
    setErrorStates: () => void;
    
    // Setters for external access
    setAgxRepresentativePostalCode: (value: string) => void;
    setAgxRepresentativePhoneNumber1: (value: string) => void;
    setAgxRepresentativePhoneNumber2: (value: string) => void;
    setAgxRepresentativePhoneNumber3: (value: string) => void;
    setAgxRepresentativeFaxNumber1: (value: string) => void;
    setAgxRepresentativeFaxNumber2: (value: string) => void;
    setAgxRepresentativeFaxNumber3: (value: string) => void;
}

const useAddressDaiyousha = (agxMerchantParams: AgxMerchantParams): UseAddressReturn => {
    // States for handling postal code
    const [agxRepresentativePostalCode, setAgxRepresentativePostalCode] = useState('');
    const [stateErrorPostalCode, setStateErrorPostalCode] = useState(false);
    
    // States for handling phone numbers
    const [agxRepresentativePhoneNumber1, setAgxRepresentativePhoneNumber1] = useState('');
    const [agxRepresentativePhoneNumber2, setAgxRepresentativePhoneNumber2] = useState('');
    const [agxRepresentativePhoneNumber3, setAgxRepresentativePhoneNumber3] = useState('');
    const [stateErrorPhoneNumber, setStateErrorPhoneNumber] = useState(false);
    
    // States for handling fax numbers
    const [agxRepresentativeFaxNumber1, setAgxRepresentativeFaxNumber1] = useState('');
    const [agxRepresentativeFaxNumber2, setAgxRepresentativeFaxNumber2] = useState('');
    const [agxRepresentativeFaxNumber3, setAgxRepresentativeFaxNumber3] = useState('');
    const [stateErrorFaxNumber, setStateErrorFaxNumber] = useState(false);

    const [isLoadingAddress, setIsLoadingAddress] = useState(false);

    const handleFindAddress = async (setValue: any, trigger: any) => {
        setIsLoadingAddress(true);
        try {
            const { data } = await adAddressService.getData(agxRepresentativePostalCode);

            if (data) {
                setValue("agxRepresentativePrefecture", data.kenName);
                setValue("agxRepresentativeAddress1", data.cityTownName);
                setValue("agxRepresentativePhoneticAddress1", data.cityTownFuri);
                
                await trigger(["agxRepresentativePrefecture", "agxRepresentativeAddress1", "agxRepresentativePhoneticAddress1"]);
            } else {
                setValue("agxRepresentativePrefecture", "");
                setValue("agxRepresentativeAddress1", "");
                setValue("agxRepresentativePhoneticAddress1", "");
                
            }
        } catch (error) {
            setValue("agxRepresentativePrefecture", "");
            setValue("agxRepresentativeAddress1", "");
            setValue("agxRepresentativePhoneticAddress1", "");
            await trigger(["agxRepresentativePrefecture", "agxRepresentativeAddress1", "agxRepresentativePhoneticAddress1"]);
        } finally {
            setIsLoadingAddress(false);
        }
    };

    // Handler for postal code input
    const handleChangeAgxRepresentativePostalCode = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        if (value.trim().includes('-')) {
            if (value.trim().length <= 8) {
                setAgxRepresentativePostalCode(value);
            }
        } else {
            if (value.trim().length <= 7) {
                setAgxRepresentativePostalCode(value);
            }
        }
    };

    const handleSetDataAgxRepresentativePostalCode = (setValue: any, trigger: any) => {
        setStateErrorPostalCode(true);
        let value = agxRepresentativePostalCode;
        if (agxRepresentativePostalCode.trim().length > 3 && !agxRepresentativePostalCode.trim().includes('-')) {
            value = agxRepresentativePostalCode.substring(0, 3) + '-' + agxRepresentativePostalCode.substring(3, agxRepresentativePostalCode.trim().length);
        }
        setAgxRepresentativePostalCode(value);
        setValue("agxRepresentativePostalCode", value);
        trigger(["agxRepresentativePostalCode"]);
    };

    // Handlers for phone numbers
    const handleChangeAgxRepresentativePhoneNumber1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setAgxRepresentativePhoneNumber1(value);
    };
    
    const handleChangeAgxRepresentativePhoneNumber2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setAgxRepresentativePhoneNumber2(value);
    };
    
    const handleChangeAgxRepresentativePhoneNumber3 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setAgxRepresentativePhoneNumber3(value);
    };

    const handleSetDataAgxRepresentativePhoneNumber = (setValue: any, trigger: any) => {
        setStateErrorPhoneNumber(true);
        setValue("agxRepresentativePhoneNumber1", agxRepresentativePhoneNumber1?.trim());
        setValue("agxRepresentativePhoneNumber2", agxRepresentativePhoneNumber2?.trim());
        setValue("agxRepresentativePhoneNumber3", agxRepresentativePhoneNumber3?.trim());
        setValue("agxRepresentativePhoneNumber", `${agxRepresentativePhoneNumber1?.trim()}-${agxRepresentativePhoneNumber2?.trim()}-${agxRepresentativePhoneNumber3?.trim()}`);
        trigger(["agxRepresentativePhoneNumber1", "agxRepresentativePhoneNumber2", "agxRepresentativePhoneNumber3"]);
    };

    // Handlers for fax numbers  
    const handleChangeAgxRepresentativeFaxNumber1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxRepresentativeFaxNumber1(e.target.value.trim());
    };
    
    const handleChangeAgxRepresentativeFaxNumber2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxRepresentativeFaxNumber2(e.target.value.trim());
    };
    
    const handleChangeAgxRepresentativeFaxNumber3 = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAgxRepresentativeFaxNumber3(e.target.value.trim());
    };

    const handleSetDataAgxRepresentativeFaxNumber = (setValue: any, trigger: any) => {
        setStateErrorFaxNumber(true);
        setValue("agxRepresentativeFaxNumber1", agxRepresentativeFaxNumber1.trim());
        setValue("agxRepresentativeFaxNumber2", agxRepresentativeFaxNumber2.trim());
        setValue("agxRepresentativeFaxNumber3", agxRepresentativeFaxNumber3.trim());
        setValue("agxRepresentativeFaxNumber", `${agxRepresentativeFaxNumber1.trim()}-${agxRepresentativeFaxNumber2.trim()}-${agxRepresentativeFaxNumber3.trim()}`);
        trigger(["agxRepresentativeFaxNumber1", "agxRepresentativeFaxNumber2", "agxRepresentativeFaxNumber3"]);
    };

    const setErrorStates = () => {
        setStateErrorPostalCode(true);
        setStateErrorPhoneNumber(true);
        setStateErrorFaxNumber(true);
    };

    useEffect(() => {
        if(agxMerchantParams) {
            setAgxRepresentativePostalCode(agxMerchantParams?.agxRepresentativePostalCode || "");
            
            // Handle phone number
            if (agxMerchantParams?.agxRepresentativePhoneNumber) {
                const phoneParts = agxMerchantParams.agxRepresentativePhoneNumber.split("-");
                setAgxRepresentativePhoneNumber1(phoneParts[0] || "");
                setAgxRepresentativePhoneNumber2(phoneParts[1] || "");
                setAgxRepresentativePhoneNumber3(phoneParts[2] || "");
            } else {
                setAgxRepresentativePhoneNumber1("");
                setAgxRepresentativePhoneNumber2("");
                setAgxRepresentativePhoneNumber3("");
            }
            
            // Handle fax number
            if (agxMerchantParams?.agxRepresentativeFaxNumber) {
                const faxParts = agxMerchantParams.agxRepresentativeFaxNumber.split("-");
                setAgxRepresentativeFaxNumber1(faxParts[0] || "");
                setAgxRepresentativeFaxNumber2(faxParts[1] || "");
                setAgxRepresentativeFaxNumber3(faxParts[2] || "");
            } else {
                setAgxRepresentativeFaxNumber1("");
                setAgxRepresentativeFaxNumber2("");
                setAgxRepresentativeFaxNumber3("");
            }
        }
    }, [agxMerchantParams]);

    return {
        // States
        agxRepresentativePostalCode,
        stateErrorPostalCode,
        agxRepresentativePhoneNumber1,
        agxRepresentativePhoneNumber2,
        agxRepresentativePhoneNumber3,
        stateErrorPhoneNumber,
        agxRepresentativeFaxNumber1,
        agxRepresentativeFaxNumber2,
        agxRepresentativeFaxNumber3,
        stateErrorFaxNumber,
        isLoadingAddress,
        handleFindAddress,
        
        // Handlers
        handleChangeAgxRepresentativePostalCode,
        handleSetDataAgxRepresentativePostalCode,
        handleChangeAgxRepresentativePhoneNumber1,
        handleChangeAgxRepresentativePhoneNumber2,
        handleChangeAgxRepresentativePhoneNumber3,
        handleSetDataAgxRepresentativePhoneNumber,
        handleChangeAgxRepresentativeFaxNumber1,
        handleChangeAgxRepresentativeFaxNumber2,
        handleChangeAgxRepresentativeFaxNumber3,
        handleSetDataAgxRepresentativeFaxNumber,
        setErrorStates,
        
        // Setters for external access
        setAgxRepresentativePostalCode,
        setAgxRepresentativePhoneNumber1,
        setAgxRepresentativePhoneNumber2,
        setAgxRepresentativePhoneNumber3,
        setAgxRepresentativeFaxNumber1,
        setAgxRepresentativeFaxNumber2,
        setAgxRepresentativeFaxNumber3,
    };
};

export default useAddressDaiyousha;