import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodR<PERSON>olver } from "@hookform/resolvers/zod";
import { useAuthStore } from "@/store";
import { passwordSchema } from "../schema";
import { EditMode, PasswordFormData } from "../types";
import { useChangePassword } from "../hooks/useChangePassword";

interface FormProps {
  editMode: EditMode;
  setEditMode: (mode: EditMode) => void;
}

function ChangePasswordForm({ editMode, setEditMode }: FormProps) {
  const { user } = useAuthStore();
  const { changePasswordAsync, isLoading, errorMessage } = useChangePassword();

  const passwordForm = useForm<PasswordFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(passwordSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const handleSubmit = passwordForm.handleSubmit(async (data) => {
    try {
      await changePasswordAsync({
        contactId: user?.id || "",
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      });

      // Success - reset form and exit edit mode
      setEditMode(EditMode.None);
      passwordForm.reset({
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (error) {
      // Error handling is done in the hook
      console.error('Password change failed:', error);
    }
  });

  return (
    <Card className="rounded-none border-t-0 border-r-0 border-l-0 shadow-none border-b-2 border-gray-400">
      <form
        onSubmit={handleSubmit}
        className="space-y-4 text-[#6F6F6E] font-[500]"
      >
        <CardContent className="px-0">
          {editMode === EditMode.Password ? (
            <>
              <div className="mb-6">
                <Label className="block text-[#6F6F6E] md:text-start text-[20px] font-[500] mb-1">
                  パスワード
                </Label>
                <div className="text-base md:text-[20px]">***********</div>
              </div>
              <div className="space-y-6 mt-10">
                <div className="grid grid-cols-[auto_1fr] gap-x-16 gap-y-8 items-center ml-8">
                  <Label
                    htmlFor="oldPassword"
                    className="font-[500] text-lg whitespace-nowrap text-left md:text-left font-semibold"
                  >
                    古いパスワード
                  </Label>
                  <div>
                    <Input
                      id="oldPassword"
                      type="password"
                      {...passwordForm.register("oldPassword")}
                      className="rounded-[8px] h-[40px] text-[16px] w-full max-w-[400px] border border-[#BABABA]"
                    />
                    {passwordForm.formState.errors.oldPassword && (
                      <p className="text-red-500 text-sm mt-1">
                        {passwordForm.formState.errors.oldPassword.message}
                      </p>
                    )}
                    {errorMessage && (
                      <p className="text-red-500 text-sm mt-1">
                        {errorMessage}
                      </p>
                    )}
                  </div>

                  <Label
                    htmlFor="newPassword"
                    className="font-[500] text-lg whitespace-nowrap text-left md:text-left font-semibold"
                  >
                    新しいパスワード
                  </Label>
                  <div>
                    <Input
                      id="newPassword"
                      type="password"
                      {...passwordForm.register("newPassword")}
                      className="rounded-[8px] h-[40px] text-[16px] w-full max-w-[400px] border border-[#BABABA]"
                    />
                    {passwordForm.formState.errors.newPassword && (
                      <p className="text-red-500 text-sm mt-1">
                        {passwordForm.formState.errors.newPassword.message}
                      </p>
                    )}
                  </div>

                  <Label
                    htmlFor="confirmPassword"
                    className="font-[500] text-lg whitespace-nowrap text-left md:text-left font-semibold"
                  >
                    新しいパスワード（確認）
                  </Label>
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Input
                        id="confirmPassword"
                        type="password"
                        {...passwordForm.register("confirmPassword")}
                        className="rounded-[8px] h-[40px] text-[16px] w-full max-w-[400px] border border-[#BABABA]"
                      />
                      {passwordForm.formState.errors.confirmPassword && (
                        <p className="text-red-500 text-sm mt-1">
                          {
                            passwordForm.formState.errors.confirmPassword
                              .message
                          }
                        </p>
                      )}
                    </div>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-teal-600 hover:bg-teal-700 text-white px-8 w-[196px] h-[50px] text-[16px] rounded-[8px]"
                    >
                      {isLoading ? "保存中..." : "保存"}
                    </Button>
                  </div>

                  <div className="col-span-2 text-gray-500 text-base">
                    パスワードは8文字以上で英小文字、英大文字、記号を含んでください
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-between text-[#6F6F6E]">
              <div>
                <Label className="font-[500] md:text-start text-[#6F6F6E] text-[20px]">
                  パスワード
                </Label>
                <div className="text-base md:text-[20px]">***********</div>
              </div>
              <Button
                variant="secondary"
                onClick={() => setEditMode(EditMode.Password)}
                className="bg-[#F7F7F7] hover:bg-[#CCCCCC] text-gray-700 w-[120px] md:w-[196px] h-[50px] text-base md:text-[20px]"
              >
                編集
              </Button>
            </div>
          )}
        </CardContent>
      </form>
    </Card>
  );
}

export default ChangePasswordForm;
