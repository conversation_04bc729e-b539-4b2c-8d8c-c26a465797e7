import logo from '@/assets/images/グループ 3.svg';

interface EmailSentProps {
    email?: string;
    onNext?: () => void;
}

const EmailSent = ({ email, onNext }: EmailSentProps) => {
    return (
        <div className="flex flex-col items-center justify-center text-center space-y-8 w-full max-w-[1080px] mx-auto h-[60vh]">
            <div className="flex items-center justify-center space-x-2 mb-1">
                <img src={logo} alt="ChoQi Logo" className="h-[85px] object-contain" />
            </div>

            <div className="bg-white shadow-md border rounded-[17px] border-gray-200 w-full max-w-[747px] p-10">
                <h2 className="text-[28px] text-gray-600 font-normal mb-6">メール登録完了のお知らせ</h2>

                <p className="text-[20px] text-gray-600 mb-8 leading-relaxed">
                    加盟店の申請登録のリンクはメールに送信しましたので、メールを確認お願い致します！
                </p>

                {email && (
                    <div className="bg-gray-50 rounded-md p-4 mb-8 border border-gray-200">
                        <p className="text-[16px] text-gray-500">送信先: <span className="text-[#19A492] font-medium">{email}</span></p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default EmailSent; 