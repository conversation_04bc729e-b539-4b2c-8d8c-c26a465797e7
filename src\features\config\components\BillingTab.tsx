import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { GetMerchantStatusResponse } from "../types";
import { mapBusinessForm, mapBusinessType } from "@/constants/common.constant";

interface BillingTabProps {
  merchantData: GetMerchantStatusResponse;
}
function BillingTab({ merchantData }: BillingTabProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            業種
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {mapBusinessType.get(merchantData?.agxBusinessType)}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            形態
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {mapBusinessForm.get(merchantData?.agxBusinessForm)}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            医療機関コード
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {merchantData?.agxMedicalInstitutionCode}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            管理コード
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full border-gray-300 h-10">
              {mapBusinessType.get(merchantData?.agxBusinessType)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default BillingTab;
