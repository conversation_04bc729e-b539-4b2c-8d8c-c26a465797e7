import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { depositService } from '@/features/store/deposit/services/depositService';

/**
 * Hook for managing deposit detail data for specific transaction type
 * Returns transactions for a SPECIFIC payment breakdown (fewer records, same transaction type)
 */
export const useDepositDetail = (
  merchantNo: string,
  paymentBId: string,
  transactionType: string,
  datetime: string
) => {
  const [totalSalesAmount, setTotalSalesAmount] = useState<number>(0);

  const {
    data: detailResponse,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ['deposit-paygate-detail', merchantNo, paymentBId, transactionType, datetime],
    queryFn: () => depositService.getElementStoreDepositDetail(merchantNo, paymentBId, transactionType, datetime),
    enabled: !!merchantNo && !!paymentBId && !!transactionType && !!datetime,
    staleTime: 0,
    gcTime: 0,
  });

  // Calculate total sales amount when data changes
  useEffect(() => {
    if (detailResponse?.data) {
      const total = detailResponse.data.reduce((sum, item) => sum + item.agxSalesAmount, 0);
      setTotalSalesAmount(total);
    } else {
      setTotalSalesAmount(0);
    }
  }, [detailResponse]);

  // Convert query error to string for compatibility
  const error = queryError ? (queryError instanceof Error ? queryError.message : 'データの取得に失敗しました') : null;

  return {
    depositDetail: detailResponse || { data: [], totalElement: 0 },
    totalSalesAmount,
    isLoading,
    error,
    refetch
  };
};

