import { Card, CardContent } from "@/components/ui/card";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { convertDate, convertPhoneAndFax } from "@/utils/helper";

interface RepresentativeInformationProps {
  merchantData: GetMerchantStatusResponse;
}

const startYear = 1912;
const endYear = new Date().getFullYear() * 1;

function RepresentativeInformation({
  merchantData,
}: RepresentativeInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-8">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            お名前<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-fullh-10 rounded-md flex items-center px-3">
              <div className="w-full h-full bg-transparent">
                {merchantData?.agxRepresentativeName ?? ""}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            フリガナ<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-8 pr0">
            <div className="w-full h-10 rounded-md px-3">
              {merchantData?.agxRepresentativePhoneticName ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            性別<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3">
            <div className="w-full h-10 rounded-md px-3">
              {merchantData?.agxRepresentativeGender || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            生年月日<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-full h-10 rounded-md px-3">
              {convertDate(merchantData?.agxRepresentativeBirthday)[0]}
            </div>
          </div>
        </div>

        {/* <div
          className="grid grid-cols-12 items-center gap-6"
          style={{
            display: merchantData?.agxBusinessForm === 283260001 ? "none" : "",
          }}
        >
          <div className="col-span-3"></div>
          <div className="col-span-6">
            <label className="flex items-center">
              <input
                id="copy_address_from_corporate2"
                type="checkbox"
                checked={merchantData?.agxStoreAddressCopyFlag1}
                disabled={true}
                className="opacity-60 mr-2"
              />
              法人所在地の入力内容をコピーする。
            </label>
          </div>
        </div> */}

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            郵便番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex items-center">
            <div className="w-full  h-10 rounded-md px-3">
              {merchantData?.agxRepresentativePostalCode ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            都道府県<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-3">
            {merchantData?.agxRepresentativePrefecture || ""}
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所１<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full  h-10 rounded-md px-3">
              {merchantData?.agxRepresentativeAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所1（フリガナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6">
            <div className="w-full  h-10 rounded-md px-3">
              {merchantData?.agxRepresentativePhoneticAddress1 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">住所2</Label>
          <div className="col-span-6">
            <div className="w-full  h-10 rounded-md px-3">
              {merchantData?.agxRepresentativeAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            住所2（フリガナ）
          </Label>
          <div className="col-span-6">
            <div className="w-full  h-10 rounded-md px-3">
              {merchantData?.agxRepresentativePhoneticAddress2 ?? ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">
            電話番号<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-16  h-10 rounded-md px-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativePhoneNumber
              )[0] || ""}
            </div>
            <span>-</span>
            <div className="w-16  h-10 rounded-md px-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativePhoneNumber
              )[1] || ""}
            </div>
            <span>-</span>
            <div className="w-16  h-10 rounded-md px-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativePhoneNumber
              )[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px]">FAX番号</Label>
          <div className="col-span-6 flex space-x-2">
            <div className="w-16  h-10 rounded-md px-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativeFaxNumber
              )[0] || ""}
            </div>
            <span>-</span>
            <div className="w-16  h-10 rounded-md px-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativeFaxNumber
              )[1] || ""}
            </div>
            <span>-</span>
            <div className="w-16  h-10 rounded-md px-3">
              {convertPhoneAndFax(
                merchantData?.agxRepresentativeFaxNumber
              )[2] || ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default RepresentativeInformation;
