// Interface định nghĩa cấu trúc của một breadcrumb item
export interface BreadcrumbItem {
  path: string;        // Đường dẫn URL để navigate
  label: string;       // Tên hiển thị trên breadcrumb
  isActive: boolean;   // C<PERSON> phải trang hiện tại không
  isClickable: boolean; // <PERSON><PERSON> thể click để navigate không
}

// Mapping từ path segment sang label tiếng Nhật tương ứng
export const PATH_LABELS: Record<string, string> = {
  // Store routes - Các route của store
  'store': 'ストア',
  'invoice-receipt': '請求書・領収書',
  'crepico': 'クレピコ',
  'paygate': 'Paygate',
  'invoice': '請求書',
  'invoice-monthly': '月次請求書',
  'receipt': '領収書',
  'receipt-monthly': '月次領収書',
  'deposit': '振込一覧',
  'notification': 'その他',
  'calendar': '振込カレンダー',
  'faq': '利用に関するFAQ',
  'summary': 'サマリー',
  'summary-monthly': '月次サマリー',
  'config': '加盟店情報',
  'detail': '詳細',
  'rollpaper': 'ロール紙の購入',
  'support': '端末サポートサイト',
  'crepico-payment': '決済データ',
  
  // Admin routes - Các route của admin
  'admin-store': '管理ストア',
  'admin-invoice-receipt': '管理請求書・領収書',
  'admin-invoice-monthly': '管理月次請求書',
  'admin-receipt-monthly': '管理月次領収書',
  'credit-card-monthly-fee': 'クレジットカード月次手数料',
  'monthly-cost-by-store': '店舗別月次コスト',
  
  // General - Các route chung
  'overview': 'ホーム',
  'account': 'アカウント設定',
  'new-account': '新規アカウント',
  'registered-store-details': '登録店舗詳細',

  'agreement': '加盟店規約',
};

/**
 * Kiểm tra xem một segment có phải là parameter động không
 * Dynamic parameter bao gồm:
 * - Parameters bắt đầu bằng ":" (route params)
 * - Chuỗi được mã hóa base64 (btoa())
 * - ID số, ngày tháng, UUID, v.v.
 * 
 * @param segment - Segment URL cần kiểm tra
 * @returns true nếu là dynamic parameter (cần bỏ qua trong breadcrumb)
 */
const isDynamicSegment = (segment: string): boolean => {
  // Bỏ qua các segment rỗng
  if (!segment || segment.length === 0) {
    return false;
  }
  
  // Kiểm tra route parameters bắt đầu bằng ":" (ví dụ: :invoiceNo, :transferDate)
  if (segment.startsWith(':')) {
    return true;
  }
  
  // Kiểm tra parameters được mã hóa base64 (kết quả của btoa())
  // Base64 chứa A-Z, a-z, 0-9, +, /, và = để padding
  const base64Pattern = /^[A-Za-z0-9+/]+=*$/;
  if (base64Pattern.test(segment) && segment.length > 10) {
    // Thử decode để xác minh có phải base64 hợp lệ không
    try {
      const decoded = atob(segment);
      // Nếu decode thành công và có nội dung, có thể là base64
      if (decoded.length > 0) {
        return true;
      }
    } catch (e) {
      // Không phải base64 hợp lệ, tiếp tục kiểm tra các pattern khác
    }
  }
  
  // Kiểm tra các pattern dynamic parameter phổ biến
  const patterns = [
    /^\d+$/,  // Số thuần túy (IDs) - ví dụ: 123456
    /^\d{4}-\d{2}-\d{2}$/,  // Ngày tháng (YYYY-MM-DD) - ví dụ: 2024-01-15
    /^\d{6}$/,  // Format YYYYMM - ví dụ: 202401
    /^\d{8}$/,  // Format YYYYMMDD - ví dụ: 20240115
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,  // UUIDs
    /^[A-Z0-9_-]{8,}$/,  // Chuỗi alphanumeric dài (merchant IDs, etc.) - ví dụ: MERCHANT123456
    /^[A-Z]{2,}\d+$/,  // Pattern như "INV123", "MER456", etc.
    /^\d+[A-Z]+\d*$/,  // Pattern như "123ABC", "456DEF789", etc.
  ];
  
  return patterns.some(pattern => pattern.test(segment));
};

/**
 * Parse URL pathname thành danh sách breadcrumb items
 * Logic:
 * 1. Luôn có "ホーム" (Overview) làm level đầu tiên
 * 2. Bỏ qua 'store' và 'admin-store' trong breadcrumb (nhưng vẫn build path)
 * 3. Bỏ qua các dynamic parameters (nhưng vẫn build path)
 * 4. Trang cuối cùng không thể click (isActive = true, isClickable = false)
 * 5. Các trang cha có thể click để navigate về
 * 
 * @param pathname - URL pathname hiện tại
 * @returns Mảng các breadcrumb items
 */
export const parseBreadcrumb = (pathname: string): BreadcrumbItem[] => {
  // Loại bỏ slash đầu và split thành segments
  const segments = pathname.replace(/^\/+/, '').split('/').filter(Boolean);
  
  // Nếu đang ở trang overview, không cần breadcrumb
  if (segments.length === 1 && segments[0] === 'overview') {
    return [];
  }
  
  const breadcrumbs: BreadcrumbItem[] = [];
  
  // Luôn thêm "ホーム" (Overview) làm level đầu tiên
  // (trừ khi đang ở chính trang overview)
  breadcrumbs.push({
    path: '/overview',
    label: 'ホーム',
    isActive: false,
    isClickable: true,
  });
  
  let currentPath = '';
  
  // Bước 1: Xác định tất cả các static segments (không phải dynamic, không phải store/admin-store)
  // Điều này giúp xác định đúng segment nào là trang cuối cùng (active)
  const staticSegments = [];
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    if (!isDynamicSegment(segment) && segment !== 'store' && segment !== 'admin-store') {
      staticSegments.push({ segment, index: i });
    }
  }
  
  // Bước 2: Xử lý từng segment để build breadcrumb
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    
    // Bỏ qua dynamic segments nhưng vẫn cần build path cho navigation
    if (isDynamicSegment(segment)) {
      currentPath += (currentPath ? '/' : '') + segment;
      continue; // Không tạo breadcrumb item cho dynamic segment
    }
    
    // Bỏ qua 'store' và 'admin-store' trong breadcrumb nhưng vẫn build path
    // Lý do: Để breadcrumb gọn gàng hơn, không hiển thị level trung gian không cần thiết
    if (segment === 'store' || segment === 'admin-store') {
      currentPath += (currentPath ? '/' : '') + segment;
      continue; // Không tạo breadcrumb item cho store/admin-store
    }
    
    // Build path đến segment hiện tại
    currentPath += (currentPath ? '/' : '') + segment;
    
    // Lấy label tiếng Nhật từ mapping, nếu không có thì dùng segment gốc
    let label = PATH_LABELS[segment] || segment;
    
    // Xử lý trường hợp đặc biệt cho segment 'paygate' dựa trên context
    if (segment === 'paygate' && i > 0) {
      const previousSegment = segments[i - 1];
      if (previousSegment === 'notification') {
        label = '初期設定';
      } else if (previousSegment === 'invoice-receipt') {
        label = 'Paygate';
      }
    }
    
    // Xác định xem segment này có phải trang hiện tại không
    // Nó là active nếu là static segment cuối cùng
    const currentSegmentIndex = staticSegments.findIndex(s => s.index === i);
    const isActive = currentSegmentIndex === staticSegments.length - 1;
    
    // Trang hiện tại không thể click (màu xám)
    const isClickable = !isActive;
    
    // Xây dựng path cho breadcrumb item
    let pathForBreadcrumb = '/' + currentPath;
    if (isClickable) {
      // Đối với các item có thể click, chỉ build path đến segment đó
      // (không bao gồm dynamic parameters phía sau)
      pathForBreadcrumb = '/' + segments.slice(0, i + 1).join('/');
    }
    
    // Tạo breadcrumb item và thêm vào danh sách
    breadcrumbs.push({
      path: pathForBreadcrumb,
      label,
      isActive,
      isClickable,
    });
  }
  
  return breadcrumbs;
};

/**
 * Kiểm tra xem có nên hiển thị breadcrumb cho pathname này không
 * Breadcrumb KHÔNG hiển thị trong các trường hợp:
 * - Trang overview (vì đây là trang gốc)
 * - Trang /store và /admin-store (trang chính của store/admin-store)
 * 
 * @param pathname - URL pathname hiện tại
 * @returns true nếu nên hiển thị breadcrumb
 */
export const shouldDisplayBreadcrumb = (pathname: string): boolean => {
  // Không hiển thị breadcrumb cho trang overview (vì đây là trang gốc)
  if (pathname === '/overview' || pathname === '/') {
    return false;
  }
  
  // Không hiển thị breadcrumb cho trang /store và /admin-store chính
  if (pathname === '/store' || pathname === '/admin-store') {
    return false;
  }
  
  // Hiển thị breadcrumb cho tất cả các trang khác
  // (vì chúng đều có overview làm trang cha)
  return true;
}; 