import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TransferCycleInfoProps } from '../types';
import iconImage from '@/assets/images/グループ 1351.svg';

export const TransferCycleInfo: React.FC<TransferCycleInfoProps> = ({ isAdmin: _isAdmin = false }) => {
  return (
    <div className="px-4 mb-8">
      {/* Title with icon outside the border */}
      <div className="flex items-center gap-3 mb-3">
        <div className="flex items-center justify-center flex-shrink-0">
          <img src={iconImage} alt="Info" className="w-8 h-8" />
        </div>
        <h3 className="text-[#6F6F6E] text-[22px]">
          振込サイクル
        </h3>
      </div>

      {/* Content inside the border */}
      <Card className="border border-gray-500 shadow-md transition-shadow duration-200 w-full 2xl:w-[1372px]">
        <CardContent className="px-4 py-4 xl:pl-10">
          <div className="space-y-2 text-[#6F6F6E] text-[22px]">
            <p>• 毎月1～15日に決済された売上金：翌月5日にお振込</p>
            <p>• 毎月16日から末日に決済された売上金：翌月20日にお振込</p>
          </div>
          <p className="text-md text-red-600 mt-3 ml-0 sm:ml-3">
            土日祝日の並びによって、お振込のタイミングが異なる場合があります。詳細はカレンダーをご確認ください。
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
