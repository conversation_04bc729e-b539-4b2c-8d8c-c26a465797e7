export interface DepositBreakdown {
  agxTransactionType: number;
  agxStoreName: string;
  agxMerchantNo: string;
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFeeRate: number;
  agxTotalFee: number;
  agxInHouseTax: number;
  agxPaymentAmount: number;
  agxPaymentBreakdownId: string;
  groupCodeName: string;
}

export interface MerchantPayment {
  merchantNo: string;
  storeName: string;
  numberOfSales: number;
  salesAmount: number;
  totalFee: number;
  sumTax: number;
  invoiceAmount: number;
  paymentAmount: number;
}

export interface AdminDepositData {
  agxPaymentBreakdowns: DepositBreakdown[];
  agxMerchantPayments: MerchantPayment[];
  subTotalConsumptionTax: number;
  subTotalInclTax10: number;
  subTotalNonTax: number;
  subTotalTaxIncl: number;
}

export interface Area {
  agx_areaid: string;
  agxAreaName: string;
  agxAreaId : string;
}

export interface SubArea {
  agxSubAreaid: string;
  agxSubAreaName: string;
  agxAreaid: string;
  agxAreaName: string;
}

export interface Merchant {
  agxMerchantNo: string;
  agxStoreName: string;
  agxAreaid: string;
  agxSubAreaid: string;
}

export interface SelectorData {
  agxAreaFormUsers: Area[];
  agxSubAreas: SubArea[];
  merchants: Merchant[];
}

export interface AdminDepositFilters {
  transferDate: string;
  areaSelected: string;
  subAreaSelected: string;
  merchantSelected: string;
}

export interface AdminDepositState {
  data: AdminDepositData | null;
  dates: string[];
  selectorData: SelectorData | null;
  filters: AdminDepositFilters;
  loading: boolean;
  error: string | null;
  dlEnable: boolean;
}

export interface CSVExportData {
  agxTransactionType: string;
  merchantNumber: string;
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFeeRate: string;
  agxTotalFee: number;
  agxInHouseTax: string;
  agxPaymentAmount: number;
}

// Types for Admin Deposit Detail page
export interface AdminDepositDetailItem {
  transactionDate: string;
  merchantNo: string;
  storeName: string;
  transactionType: number;
  salesAmount: number;
  paymentDate: string;
  memberId: string;
  groupCodeName: string;
}

export interface AdminDepositDetailData {
  data: AdminDepositDetailItem[];
  total: number;
  totalSales: number;
}

export interface AdminDepositDetailCSVData {
  transactionDate: string;
  merchantNo: string;
  storeName: string;
  transactionType: string;
  salesAmount: number;
  paymentDate: string;
  memberId: string;
}
