import React from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';

interface HeaderProps {
  showManualLink?: boolean;
}

export const Header: React.FC<HeaderProps> = () => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const currentStep = parseInt(searchParams.get('step') || '1');
  
  // Show manual link only if on application-steps path and NOT on CONFIRM or COMPLETE step
  const showManualLink = location.pathname.includes('/application-steps');
  
  return (
    <header className="fixed top-0 left-0 right-0 z-50 w-full bg-white px-4 md:px-8 py-3 md:pt-[50px]">
      <div className="max-w-[1686px] mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2 md:gap-3">
          <img 
            src="/logo.png" 
            alt="ChoQi Pay Logo" 
            className="w-32 h-auto md:w-60 lg:w-64 object-contain"
          />
        </div>
        {showManualLink && (
          <a 
            href="#" 
            className="text-sm md:text-lg lg:text-xl text-[#1D9987] font-normal mt-[25px] mr-[130px] hover:underline transition-all duration-200"
          >
            申し込みマニュアル
          </a>
        )}
      </div>
    </header>
  );
}; 