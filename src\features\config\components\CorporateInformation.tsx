import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { GetMerchantStatusResponse } from "../types";
import { convertPhoneAndFax } from "@/utils/helper";

interface CorporateInformationProps {
  merchantData: GetMerchantStatusResponse;
}
function CorporateInformation({ merchantData }: CorporateInformationProps) {
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-8 space-y-14">
        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            法人名<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporateName !== null
                ? merchantData?.agxCorporateName
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            法人名（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporatePhoneticName !== null
                ? merchantData?.agxCorporatePhoneticName
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            法人名（英字）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporateEnglishName !== null
                ? merchantData?.agxCorporateEnglishName
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">法人番号<span className="text-red-500 ml-1">*</span></Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporateNumber !== null
                ? merchantData?.agxCorporateNumber
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">郵便番号<span className="text-red-500 ml-1">*</span></Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporatePostalCode !== null
                ? merchantData?.agxCorporatePostalCode
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">都道府県<span className="text-red-500 ml-1">*</span></Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporatePrefecture !== null
                ? merchantData?.agxCorporatePrefecture
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">市町村<span className="text-red-500 ml-1">*</span></Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporateAddress1 !== null
                ? merchantData?.agxCorporateAddress1
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            市町村（カナ）<span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporatePhoneticAddress1 !== null
                ? merchantData?.agxCorporatePhoneticAddress1
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            丁目番地建物名
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporateAddress2 !== null
                ? merchantData?.agxCorporateAddress2
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">
            丁目番地建物名（カナ）
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <div className="col-span-6 text-[#6F6F6E]">
            <div className="w-full border-gray-300 h-7">
              {merchantData?.agxCorporatePhoneticAddress2 !== null
                ? merchantData?.agxCorporatePhoneticAddress2
                : ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">電話番号<span className="text-red-500 ml-1">*</span></Label>
          <div className="col-span-6 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md pl-3">
              {convertPhoneAndFax(
                merchantData?.agxCorporatePhoneNumber
              )[0] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxCorporatePhoneNumber)[1] ?  "-" :""}</span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(
                merchantData?.agxCorporatePhoneNumber
              )[1] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxCorporatePhoneNumber)[2] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(merchantData?.agxCorporatePhoneNumber)[2] || ""}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 items-center gap-6">
          <Label className="col-span-3 text-right text-[20px] text-[#6F6F6E]">FAX</Label>
          <div className="col-span-6 flex space-x-2 text-[#6F6F6E]">
            <div className="w-auto h-7 rounded-md pl-3">
              {convertPhoneAndFax(
                merchantData?.agxCorporateFaxNumber
              )[0] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxCorporateFaxNumber)[1] ?  "-" :""}</span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(
                merchantData?.agxCorporateFaxNumber
              )[1] || ""}
            </div>
            <span>
              {convertPhoneAndFax(merchantData?.agxCorporateFaxNumber)[2] ? "-":""}
            </span>
            <div className="w-auto h-7 rounded-md">
              {convertPhoneAndFax(merchantData?.agxCorporateFaxNumber)[2] || ""}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default CorporateInformation;
