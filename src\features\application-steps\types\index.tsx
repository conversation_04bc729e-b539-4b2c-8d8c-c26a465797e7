export interface AgxMerchantParams {
    agxMerchantNo: string;
    agxMerchantid: string;
    agxContactId: string;
    agxBusinessType: number;
    agxBusinessForm: number;
    agxMedicalInstitutionCode: string;
    agxEmsEmployeeNo: string;
    agxCorporateName: string;
    agxCorporatePhoneticName: string;
    agxCorporateEnglishName: string;
    agxCorporateNumber: string;
    agxCorporatePostalCode: string;
    agxCorporatePrefecture: string;
    agxCorporateAddress1: string;
    agxCorporateAddress2: string;
    agxCorporatePhoneticPrefecture: string;
    agxCorporatePhoneticAddress1: string;
    agxCorporatePhoneticAddress2: string;
    agxCorporatePhoneNumber: string;
    agxCorporateFaxNumber: string;
    agxRepresentativeName: string;
    agxRepresentativePhoneticName: string;
    agxRepresentativeGender: number;
    agxRepresentativeBirthday: string;
    agxRepresentativeAddressCopyFlag: boolean;
    agxRepresentativePostalCode: string;
    agxRepresentativePrefecture: string;
    agxRepresentativeAddress1: string;
    agxRepresentativeAddress2: string;
    agxRepresentativePhoneticPrefecture: string;
    agxRepresentativePhoneticAddress1: string;
    agxRepresentativePhoneticAddress2: string;
    agxRepresentativePhoneNumber: string;
    agxRepresentativeFaxNumber: string;
    agxStoreName: string;
    agxStorePhoneticName: string;
    agxStoreEnglishName: string;
    agxUrl: string;
    agxBrandName: string;
    agxBusinessDate: string;
    agxRegularHoliday: string;
    agxBusinesssHours: string;
    agxStoreAddressCopyFlag1: boolean;
    agxStoreAddressCopyFlag2: boolean;
    agxStorePostalCode: string;
    agxStorePrefecture: string;
    agxStoreAddress1: string;
    agxStoreAddress2: string;
    agxStorePhoneticPrefecture: string;
    agxStorePhoneticAddress1: string;
    agxStorePhoneticAddress2: string;
    agxStorePhoneNumber: string;
    agxStoreFaxNumber: string;
    agxBankNo: string;
    agxBankName: string;
    agxBankType: number;
    agxBranchNo: string;
    agxBranchName: string;
    agxBranchType: number;
    agxBankPhoneticName: string;
    agxBranchPhoneticName: string;
    agxAccountType: number;
    agxAccountNo: string;
    agxAccountHolder: string;
    agxContactName: string;
    agxContactEmail: string;
    agxContactPhoneticName: string;
    agxContactPhoneNumber: string;
    agxCapital: number;
    agxNumberOfEmployees: string;
    agxFoundingDate: string | null;
    agxMonthlySales: number;
    agxDoorToDoorSales: boolean;
    agxTelemarketingSales: boolean;
    agxPyramidScheme: boolean;
    agxBusinessOpportunityRelatedSales: boolean;
    agxSpecifiedContinuousServices: boolean;
    agxCardInformationRetentionStatus: number;
    agxNoRetainingCardInfoDate: string | null;
    agxPcidssStatus: number;
    agxPcidssExpectedComplianceDate: string | null;
    agxThreeDSecureStatus: number;
    agxThreeDSecureDate: string | null;
    agxSecurityCodeCheckStatus: number;
    agxSecurityCodeCheckDate: string | null;
    agxIllegalDeliveryDestinationStatus: number;
    agxIllegalDeliveryDestinationDate: string | null;
    agxBehaviorAnalysisStatus: number;
    agxBehaviorAnalysisDate: string | null;
    agxOtherMeasuresStatus: number;
    agxOtherMeasuresDate: string | null;
    agxOtherMeasuresDescription: string;
    agxNumberOfTerminal: number;
    agxColorOfTerminal: number;
    agxSettlementCard: boolean;
    agxSettlementJcb: boolean;
    agxSettlementTraffic: boolean;
    agxSettlementNanaco: boolean;
    agxSettlementWaon: boolean;
    agxSettlementEdy: boolean;
    agxSettlementAid: boolean;
    agxSettlementQuicpay: boolean;
    agxSettlementQrCode: boolean;
    memberType: boolean;
    agxSettlementPackage1: boolean;
    agxSettlementPackage2: boolean;
    agxApplicationStatus: number;
    // Kiyaku agreement states
    agxKiyakuRules?: {
        rule_1?: boolean;
        rule_2?: boolean;
        rule_3?: boolean;
        rule_4?: boolean;
        rule_5?: boolean;
        rule_6?: boolean;
        rule_7?: boolean;
        rule_8?: boolean;
        rule_9?: boolean;
        rule_10?: boolean;
        rule_11?: boolean;
        rule_12?: boolean;
        rule_13?: boolean;
        rule_14?: boolean;
        rule_15?: boolean;
        rule_16?: boolean;
        rule_17?: boolean;
    };
    agxKiyakuLabels?: {
        label_1?: boolean;
        label_2?: boolean;
        label_3?: boolean;
        label_4?: boolean;
        label_5?: boolean;
        label_6?: boolean;
        label_7?: boolean;
        label_8?: boolean;
        label_9?: boolean;
        label_10?: boolean;
        label_11?: boolean;
        label_12?: boolean;
        label_13?: boolean;
        label_14?: boolean;
        label_15?: boolean;
        label_16?: boolean;
        label_17?: boolean;
    };
}

export interface AgxMerchantResponse {
    agxMerchantNo: string;
    agxMerchantid: string;
    agxContactId: string;
    agxBusinessType: number;
    agxBusinessForm: number;
    agxMedicalInstitutionCode: string;
    agxEmsEmployeeNo: string;
    agxCorporateName: string;
    agxCorporatePhoneticName: string;
    agxCorporateEnglishName: string;
    agxCorporateNumber: string;
    agxCorporatePostalCode: string;
    agxCorporatePrefecture: string;
    agxCorporateAddress1: string;
    agxCorporateAddress2: string;
    agxCorporatePhoneticPrefecture: string;
    agxCorporatePhoneticAddress1: string;
    agxCorporatePhoneticAddress2: string;
    agxCorporatePhoneNumber: string;
    agxCorporateFaxNumber: string;
    agxRepresentativeName: string;
    agxRepresentativePhoneticName: string;
    agxRepresentativeGender: number;
    agxRepresentativeBirthday: string;
    agxRepresentativeAddressCopyFlag: boolean;
    agxRepresentativePostalCode: string;
    agxRepresentativePrefecture: string;
    agxRepresentativeAddress1: string;
    agxRepresentativeAddress2: string;
    agxRepresentativePhoneticPrefecture: string;
    agxRepresentativePhoneticAddress1: string;
    agxRepresentativePhoneticAddress2: string;
    agxRepresentativePhoneNumber: string;
    agxRepresentativeFaxNumber: string;
    agxStoreName: string;
    agxStorePhoneticName: string;
    agxStoreEnglishName: string;
    agxUrl: string;
    agxBrandName: string;
    agxBusinessDate: string;
    agxRegularHoliday: string;
    agxBusinesssHours: string;
    agxStoreAddressCopyFlag1: boolean;
    agxStoreAddressCopyFlag2: boolean;
    agxStorePostalCode: string;
    agxStorePrefecture: string;
    agxStoreAddress1: string;
    agxStoreAddress2: string;
    agxStorePhoneticPrefecture: string;
    agxStorePhoneticAddress1: string;
    agxStorePhoneticAddress2: string;
    agxStorePhoneNumber: string;
    agxStoreFaxNumber: string;
    agxBankNo: string;
    agxBankName: string;
    agxBankType: number;
    agxBranchNo: string;
    agxBranchName: string;
    agxBranchType: number;
    agxBankPhoneticName: string;
    agxBranchPhoneticName: string;
    agxAccountType: number;
    agxAccountNo: string;
    agxAccountHolder: string;
    agxContactName: string;
    agxContactEmail: string;
    agxContactPhoneticName: string;
    agxContactPhoneNumber: string;
    agxCapital: number;
    agxNumberOfEmployees: string;
    agxFoundingDate: string | null;
    agxMonthlySales: number;
    agxDoorToDoorSales: boolean;
    agxTelemarketingSales: boolean;
    agxPyramidScheme: boolean;
    agxBusinessOpportunityRelatedSales: boolean;
    agxSpecifiedContinuousServices: boolean;
    agxCardInformationRetentionStatus: number;
    agxNoRetainingCardInfoDate: string | null;
    agxPcidssStatus: number;
    agxPcidssExpectedComplianceDate: string | null;
    agxThreeDSecureStatus: number;
    agxThreeDSecureDate: string | null;
    agxSecurityCodeCheckStatus: number;
    agxSecurityCodeCheckDate: string | null;
    agxIllegalDeliveryDestinationStatus: number;
    agxIllegalDeliveryDestinationDate: string | null;
    agxBehaviorAnalysisStatus: number;
    agxBehaviorAnalysisDate: string | null;
    agxOtherMeasuresStatus: number;
    agxOtherMeasuresDate: string | null;
    agxOtherMeasuresDescription: string;
    agxNumberOfTerminal: number;
    agxColorOfTerminal: number;
    agxSettlementCard: boolean;
    agxSettlementJcb: boolean;
    agxSettlementTraffic: boolean;
    agxSettlementNanaco: boolean;
    agxSettlementWaon: boolean;
    agxSettlementEdy: boolean;
    agxSettlementAid: boolean;
    agxSettlementQuicpay: boolean;
    agxSettlementQrCode: boolean;
    memberType: boolean;
    agxSettlementPackage1: boolean;
    agxSettlementPackage2: boolean;
    agxApplicationStatus: number;
}

export interface ApplicationStepProps {
    setStep?: (step: number) => void;
    agxMerchantParams: AgxMerchantParams | null;
    setAgxMerchantParams?: (data: AgxMerchantParams | null) => void;
    updateMerchant: (data: AgxMerchantParams, isSendMail?: boolean) => void;
    isUpdating?: boolean;
}

export interface AgxFeeRateResponse {
    data: AgxFeeRate;
}
export interface AgxFeeRate {
    creditVisaRate: string;
    creditUnionRate: string;
    creditJcbRate: string;
    transportationRate: string;
    nanacoRate: string;
    waonRate: string;
    edyRate: string;
    idRate: string;
    quicpayRate: string;
    qrBankpayRate: string;
    qrOtherBankpayRate: string;
}
