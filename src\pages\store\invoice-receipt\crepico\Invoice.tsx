import React from 'react';
import { useParams } from 'react-router-dom';
import { InvoiceMonthlyLayout } from "@/features/store/invoice-receipt/components/InvoiceMonthlyLayout";
import { useStoreInvoiceDetails } from '@/features/store/invoice-receipt/hooks/useStoreInvoiceDetails';
import { formatNumber } from '@/utils/dateUtils';

const CrepicoInvoice: React.FC = () => {
    const { invoiceNo } = useParams<{ invoiceNo: string }>();

    const { data } = useStoreInvoiceDetails(invoiceNo);

    return (
        <InvoiceMonthlyLayout data={data} isMonthly={false} >
            <div className="row">
                <table className="table w-full border-collapse" style={{ fontWeight: '450', color: 'black' }}>
                    <thead>
                        <tr style={{ backgroundColor: '#757575' }}>
                            <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4">品目</th>
                            <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4 w-20">数量</th>
                            <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4 w-24">単価</th>
                            <th className="text-center border border-[#6F6F6E] text-white text-[18px] py-2 px-4 w-24">金額</th>
                        </tr>
                    </thead>
                    <tbody>
                        {data?.invoiceDetail?.length > 0 ? (
                            data.invoiceDetail.map((item, index) => (
                                <tr className='border border-[#6F6F6E] text-[16px]' key={index}>
                                    <td className='text-left border border-[#6F6F6E] py-2 px-4'>{item.name}</td>
                                    <td className='text-center border border-[#6F6F6E] py-2 px-4'>{formatNumber(item.quantity || 0)}</td>
                                    <td className='text-right border border-[#6F6F6E] py-2 px-4'>{formatNumber(item.unitPrice || 0)}</td>
                                    <td className='text-right border border-[#6F6F6E] py-2 px-4'>{formatNumber(item.amount || 0)}</td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan={4} className='text-[#6F6F6E] text-center border border-[#6F6F6E] py-2 px-4'>
                                    請求書に商品がありません。
                                </td>
                            </tr>
                        )}
                        <tr className='border border-[#6F6F6E] text-[16px]'>
                            <td className='text-left border border-[#6F6F6E] py-2 px-4'></td>
                            <td colSpan={2} className='text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4 text-left'>小計</td>
                            <td className='text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4'>
                                {formatNumber(data?.subTotal || 0)}</td>
                        </tr>
                        <tr className='border border-[#6F6F6E] text-[16px]'>
                            <td className='text-left border border-[#6F6F6E] py-2 px-4'></td>
                            <td colSpan={2} className='text-[#6F6F6E] border border-[#6F6F6E] py-2 px-4 text-left'>消費税(10%)</td>
                            <td className='text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4'>
                                {formatNumber(data?.tax || 0)} </td>
                        </tr>
                        <tr className='border border-[#6F6F6E] text-[16px]'>
                            <td className='text-left border border-[#6F6F6E] py-2 px-4'></td>
                            <td colSpan={2} className='text-[#6F6F6E] font-bold border border-[#6F6F6E] py-2 px-4 text-left'>
                                合計</td>
                            <td className='text-[#6F6F6E] text-right border border-[#6F6F6E] py-2 px-4 font-bold'>
                                {formatNumber(data?.total || 0)}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </InvoiceMonthlyLayout>
    );
};

export default CrepicoInvoice;