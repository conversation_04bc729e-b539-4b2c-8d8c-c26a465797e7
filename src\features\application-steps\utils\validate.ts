// 全角項目バリデーションチェック
export const validateWideString = (value: string, includeWhiteSpace: boolean = false) => {
    // eslint-disable-next-line no-control-regex
    const pat = includeWhiteSpace ? /[\x01-\x1f\x21-\x7E\uFF65-\uFF9F]/ : /[\x01-\x7E\uFF65-\uFF9F]/;
    if (value == null || value === "" || !value.match(pat)) {
        return true;
    } else {
        return false;
    }
}

// 全角カタカナバリデーションチェック
export const validateWideKana = (value: string, includeWhiteSpace: boolean = false) => {
    // eslint-disable-next-line no-irregular-whitespace
    const pat = includeWhiteSpace ? /^[ァ-ヶー　Ａ-Ｚａ-ｚ０-９！”＃＄％＆’（）＝〜｜−ー＾￥＋＊＜＞？＿：；、。・ ]*$/ : /^[ァ-ヶー　Ａ-Ｚａ-ｚ０-９！”＃＄％＆’（）＝〜｜−ー＾￥＋＊＜＞？＿：；、。・]*$/;
    if (value == null || value === "" || value.match(pat)) {
        return true;
    } else {
        return false;
    }
}

// ASCII文字バリデーションチェック
export const validateAscii = (value: string) => {
    if (value == null || value === "" || !value.match(/^[\x20-\x7e]*$/)) {
        return true;
    } else {
        return false;
    }
}

// 人名バリデーションチェック
export const validateHumanName = (value: string) => {
    // eslint-disable-next-line no-irregular-whitespace
    if (value === null || value.match(/^[^ 　]+[ 　]+[^ 　].*/)) {
        return true;
    }
    else {
        return false;
    }
}

// 正規表現バリデーションチェック
export function validatePattern(value, patternValue) {
    // var pattern = String(patternValue);
    if (value == null || value.match(patternValue)) {
        return true;
    }
    else {
        return false;
    }
}

// 半角カタカナバリデーションチェック
export function validateNarrowKana(value: string) {
    // eslint-disable-next-line no-useless-escape
    if (value == null || value === "" || value.match(/^[ｦ-ﾟ \\｢｣\(\)\-\/\*&$,.@=%+; ]*$/)) {
        return true;
    } else {
        return false;
    }
}

// 半角カタカナバリデーションチェック, 大文字を検証する, チェックデジットの検証
export function validateNarrowKanaNumericAndUpperCase(value) {
    // eslint-disable-next-line no-useless-escape
    if (value == null || value === "" || value.match(/^[ｦ-ﾟ \\｢｣\(\)\-\/\*&$,.@=%+; 0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ]*$/)) {
        return true;
    } else {
        return false;
    }
}